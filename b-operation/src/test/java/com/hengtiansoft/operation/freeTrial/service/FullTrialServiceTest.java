package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.order.service.OrderService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.entity.vo.OrderInfoActivityVO;
import com.hengtiansoft.privilege.entity.dto.FullTrialQueryDTO;
import com.hengtiansoft.privilege.entity.dto.FullTrialSaveDTO;
import com.hengtiansoft.privilege.entity.dto.FullTrialSaveDTO.Product;
import com.hengtiansoft.privilege.entity.dto.PayUserQueryDTO;
import com.hengtiansoft.privilege.entity.vo.FullTrialDetailVO;
import com.hengtiansoft.privilege.entity.vo.FullTrialListVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import com.hengtiansoft.security.po.UserAuthenticationToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-08-06 14:00
 **/
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class FullTrialServiceTest {

    @Autowired
    private FullTrialService fullTrialService;

    @Autowired
    private OrderService orderService;
    @Before
    public void setup() {
        UserDetailDTO dto = new UserDetailDTO();
        dto.setUserId(333L);
        dto.setUserName("test");
        Authentication auth = new UserAuthenticationToken(dto.getUserId(), "password", Collections.emptyList(), dto);
        SecurityContextHolder.getContext().setAuthentication(auth);
    }
    @Test
    public void getList() {
        FullTrialQueryDTO queryDto = new FullTrialQueryDTO();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
//        queryDto.setName("0603");
        queryDto.setId(5L);
        PageVO<FullTrialListVO> list = fullTrialService.getList(queryDto);
        log.info("Page: {}", JSON.toJSONString(list));
    }

    public void save() throws ParseException {
        String startTimeStr = "2024-08-06 17:00:00";
        String endTimeStr = "2024-08-06 18:50:00";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        FullTrialSaveDTO saveDTO = new FullTrialSaveDTO();
//        saveDTO.setId(2L);
        saveDTO.setName("海洋满赠活动080603");
        saveDTO.setNoticeTime(new Date());
        saveDTO.setStartTime(sdf.parse(startTimeStr));
        saveDTO.setEndTime(sdf.parse(endTimeStr));
        saveDTO.setPeopleLimit(3);
//        saveDTO.setLabelId(0L);
        saveDTO.setGradeList(Lists.newArrayList("1","2"));
        saveDTO.setIsPublic(1);
        saveDTO.setShareDiscount("1,2");

        Product freeProduct = new Product();
        freeProduct.setProductId(491L);
        freeProduct.setSkuId(1197L);
        freeProduct.setShowPrice(new BigDecimal("9.9"));
//        freeProduct.setActivityPrice(new BigDecimal("0"));
//        freeProduct.setLimitNum(0L);


        saveDTO.setFreeProduct(freeProduct);

        Product trialProduct = new Product();
        trialProduct.setProductId(491L);
        trialProduct.setSkuId(1198L);
        trialProduct.setShowPrice(new BigDecimal("88"));
        trialProduct.setActivityPrice(new BigDecimal("0.1"));
        trialProduct.setLimitNum(2L);


        saveDTO.setTrialProduct(trialProduct);

        fullTrialService.save(saveDTO);
    }

    @Test
    public void get() {
        FullTrialDetailVO detailVO = fullTrialService.get(3L);
        log.info("detail:{}", JSON.toJSONString(detailVO));
    }

    @Test
    public void promotion() {
        PromoVO promotion = fullTrialService.promotion(3L);
        log.info("promotion: {}", JSON.toJSONString(promotion));
    }

    @Test
    public void delete() {
        fullTrialService.delete(2L);
    }

    @Test
    public void end() {
        fullTrialService.end(3L);
    }

    @Test
    public void getPayUsers() {
        PayUserQueryDTO dto = new PayUserQueryDTO();
        dto.setPageSize(10);
        dto.setPageNum(1);
        dto.setActivityId(58L);
        dto.setSourceType(4);
        PageVO<OrderInfoActivityVO> payUserList = orderService.getPayUserList(dto);
        log.info("payUsers: {}", JSON.toJSONString(payUserList));
    }
}