package com.hengtiansoft.operation.privilege.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivityPageDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivitySaveDTO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityDetailVO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityPageVO;
import com.hengtiansoft.privilege.manager.PrivilegeLabelManager;
import com.hengtiansoft.security.po.UserAuthenticationToken;
import com.hengtiansoft.thirdpart.entity.dto.nascent.CustomerSaveDTO;
import com.hengtiansoft.thirdpart.interfaces.NascentCustomerManager;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.nascent.ecrp.opensdk.response.customer.CustomerSaveResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;

/**
 * @Author: haiyang
 * @Date: 2024-09-24 14:20
 * @Desc:
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class PrivilegeLabelActivityServiceTest {

    @Resource
    private PrivilegeLabelActivityService privilegeLabelActivityService;
    @Resource
    private PrivilegeLabelManager privilegeLabelManager;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private NascentCustomerManager nascentCustomerManager;
    @Resource
    private RedisOperation redisOperation;

    @Before
    public void setup() {
        UserDetailDTO dto = new UserDetailDTO();
        dto.setUserId(333L);
        dto.setUserName("test");
        Authentication auth = new UserAuthenticationToken(dto.getUserId(), "password", Collections.emptyList(), dto);
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    @Test
    public void getPage() {
        PrivilegeLabelActivityPageDTO dto = new PrivilegeLabelActivityPageDTO();
        PageVO<PrivilegeLabelActivityPageVO> page = privilegeLabelActivityService.getPage(dto);
        log.info("pageVO: {}", JSON.toJSONString(page));
    }

    @Test
    public void save() throws ParseException {
        PrivilegeLabelActivitySaveDTO saveDTO = new PrivilegeLabelActivitySaveDTO();
        saveDTO.setId(150L);
        saveDTO.setName("海洋特权标签测试");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        saveDTO.setStartTime(dateFormat.parse("2024-09-25 14:25:56"));
        saveDTO.setEndTime(dateFormat.parse("2024-09-26 14:25:59"));
        saveDTO.setPeopleLimit(4);
        saveDTO.setLabelIds(Lists.newArrayList(121L,122L));

        List<PrivilegeLabelActivitySaveDTO.Product> productList = Lists.newArrayList();
        PrivilegeLabelActivitySaveDTO.Product product = new PrivilegeLabelActivitySaveDTO.Product();
        product.setProductId(199L);
        product.setLimitNum(2);
        productList.add(product);
        saveDTO.setProductList(productList);

        privilegeLabelActivityService.save(saveDTO);
    }

    @Test
    public void get() {
        PrivilegeLabelActivityDetailVO privilegeLabelActivityDetailVO = privilegeLabelActivityService.get(139L);
        log.info("privilegeLabelActivityDetailVO: {}", JSON.toJSONString(privilegeLabelActivityDetailVO));
    }

    @Test
    public void end() {
        privilegeLabelActivityService.end(139L);
    }

    @Test
    public void start() {
        privilegeLabelActivityService.start(139L);
    }

    @Test
    public void delete() {
        privilegeLabelActivityService.delete(139L);
    }

    @Test
    public void testRefreshPrivilegeLabelUser() {
        privilegeLabelManager.refreshPrivilegeLabelUser(344L, "update");
    }

    @Test
    public void testUpdatePrivilegeLabelUser() {
        privilegeLabelManager.updatePrivilegeLabelUser(Lists.newArrayList(487L), Lists.newArrayList(487L), 211L);
    }

    @Test
    public void nonMember(){
        List<CustomerUser> customerUserList =  customerUserDao.findByPhone("18143461684");
        for (CustomerUser customerUser: customerUserList) {
            CustomerSaveDTO saveDTO = new CustomerSaveDTO();
            saveDTO.setMobile(customerUser.getPhone());
            String customerName = StringUtils.isBlank(customerUser.getNickName()) ? (StringUtils.isBlank(customerUser.getUserName()) ? customerUser.getPhone() : customerUser.getUserName()):customerUser.getNickName();
            saveDTO.setCustomerName(customerName);
            CustomerSaveResponse response = nascentCustomerManager.registerCustomer(saveDTO);
            System.out.println(response);
        }
    }
}