package com.hengtiansoft.operation.pintuan.service;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.vo.ProductSkuListVO;
import com.hengtiansoft.item.entity.vo.ProductSkuWithForbiddenReasonListVO;
import com.hengtiansoft.operation.item.cmp.ProductSkuContext;
import com.hengtiansoft.operation.item.dto.ProductSkuQueryDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivityDelDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivitySaveDTO.ProductInfo;

import com.hengtiansoft.operation.pintuan.dto.PtActivitySaveDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivityStartOrEndDTO;
import com.hengtiansoft.operation.pintuan.vo.PtActivityListVO;
import com.hengtiansoft.operation.pintuan.vo.PtActivityVO;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.privilege.entity.dto.PtActivityListDTO;
import com.hengtiansoft.security.po.UserAuthenticationToken;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.Collections;

import static org.junit.Assert.*;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-28 11:32
 **/

@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class PtActivityServiceTest {

    @Resource
    private PtActivityService ptActivityService;

    @Resource
    private FlowExecutor flowExecutor;

    @Before
    public void setup() {
        // 构建模拟认证信息
        UserDetailDTO dto = new UserDetailDTO();
        dto.setUserId(999L);
        dto.setUserName("test");
        Authentication auth = new UserAuthenticationToken(dto.getUserId(), "password", Collections.emptyList(), dto);
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    @Test
    public void save() throws ParseException {
        PtActivitySaveDTO saveDTO = new PtActivitySaveDTO();
        saveDTO.setName("测试标签");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        saveDTO.setStartTime(dateFormat.parse("2024-08-01 14:25:56"));
        saveDTO.setEndTime(dateFormat.parse("2024-08-02 14:25:59"));
        saveDTO.setPeopleLimit(1);
        ProductInfo productInfo = new ProductInfo();
        productInfo.setSkuId(2583L);
        productInfo.setProductId(1796L);
        productInfo.setPtPrice(new BigDecimal("1.00"));

        saveDTO.setProductInfo(productInfo);
        saveDTO.setValidityPeriod(15);
        saveDTO.setRequiredNum(3);
        saveDTO.setStock(1L);
        saveDTO.setLimitNum(1);
        saveDTO.setEnableMocker(1);
        saveDTO.setAdvanceNotice(1);
        saveDTO.setShareDiscount("1");
        saveDTO.setCustomerMarkList(Lists.newArrayList("363"));

        ptActivityService.save(saveDTO);

    }

    @Test
    public void get() {
        PtActivityVO ptActivityVO = ptActivityService.get(9L);
        log.info("ptActivityVO: {}", JSON.toJSONString(ptActivityVO));
    }

    @Test
    public void pageList() {
        PtActivityListDTO listDTO = new PtActivityListDTO();
        PageVO<PtActivityListVO> pageVO = ptActivityService.pageList(listDTO);
        log.info("pageVO: {}", JSON.toJSONString(pageVO));
    }

    @Test
    public void startOrEnd() {
        PtActivityStartOrEndDTO endDTO = new PtActivityStartOrEndDTO();
        endDTO.setId(2L);
        endDTO.setOperateType(2);
        ptActivityService.startOrEnd(endDTO);
    }

    @Test
    public void delete() {
        PtActivityDelDTO delDTO =  new PtActivityDelDTO();
        delDTO.setId(2L);
        ptActivityService.delete(delDTO);
    }

    @Test
    public void productSkuList() {
        ProductSkuQueryDTO baseSearchDTO =  new ProductSkuQueryDTO();
        baseSearchDTO.setCateIds(Lists.newArrayList(1635L));
        baseSearchDTO.setCateLevel(1);
//        baseSearchDTO.setProductId(2129L);
        LiteflowResponse response = flowExecutor.execute2Resp("productSkuPageListChain", baseSearchDTO, ProductSkuContext.class);
        if (response.isSuccess()) {
            log.info("stepStr: {}", response.getExecuteStepStr());
            ProductSkuContext productSkuContext = response.getContextBean("productSkuContext");
            PageVO<ProductSkuWithForbiddenReasonListVO> productSkuWithForbiddenReasonPageVO = productSkuContext.getProductSkuWithForbiddenReasonPageVO();
            log.info("pageVO: {}", JSON.toJSONString(productSkuWithForbiddenReasonPageVO));
        }
    }
}