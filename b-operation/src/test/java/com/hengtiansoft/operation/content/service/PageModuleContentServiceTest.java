package com.hengtiansoft.operation.content.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.*;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.entity.vo.PageModuleVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.po.UserAuthenticationToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-02 18:43
 **/
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class PageModuleContentServiceTest {

    @Resource
    private PageModuleContentService pageModuleContentService;

    @Resource
    private PageModuleService pageModuleService;


    @Before
    public void setup() {
        // 构建模拟认证信息
        UserDetailDTO dto = new UserDetailDTO();
        dto.setUserId(333L);
        dto.setUserName("test");
        Authentication auth = new UserAuthenticationToken(dto.getUserId(), "password", Collections.emptyList(), dto);
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    @Test
    public void savePersonalInfo() {
        PageModuleContentSaveDTO saveDTO = new PageModuleContentSaveDTO();
        saveDTO.setImageUrl("4334434334");
        saveDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        saveDTO.setModuleCode(PageModuleCodeEnum.PERSONAL_INFO.getCode());
        pageModuleContentService.save(saveDTO);
    }
    @Test
    public void saveMemberRights() {
        PageModuleContentSaveDTO saveDTO = new PageModuleContentSaveDTO();
        saveDTO.setName("会员333");
        saveDTO.setPeopleLimit(3);
        saveDTO.setSort(1);
        saveDTO.setGradeList(Lists.newArrayList("1","2"));
        PageModuleContentSaveDTO.ExtraContent extraContent1 = new PageModuleContentSaveDTO.ExtraContent();
        extraContent1.setGrade(1);
        extraContent1.setSubTitle("养牛新人副标题");
        extraContent1.setInnerPageUrl("234efas");

        PageModuleContentSaveDTO.ExtraContent extraContent2 = new PageModuleContentSaveDTO.ExtraContent();
        extraContent2.setGrade(1);
        extraContent2.setSubTitle("养牛红人副标题");
        extraContent2.setInnerPageUrl("dsjaksdjlfadsl");

        saveDTO.setExtraContents(Lists.newArrayList(extraContent1, extraContent2));
        saveDTO.setStartTime(new Date());
        saveDTO.setEndTime(new Date());
        saveDTO.setSort(1);
        saveDTO.setLinkType(1);
        saveDTO.setLink("1332");

        saveDTO.setImageUrl("https://www.google.com.hk/url?sa=i&url=https%3A%2F%2Fphotokit.com%2Ffeatures%2Fedit-text-in-image%2F%3Flang%3Dzh&psig=AOvVaw1ck1jL63yRAkUhekNp_fAQ&ust=1712223232046000&source=images&cd=vfe&opi=89978449&ved=0CBIQjRxqFwoTCNiJhN7epYUDFQAAAAAdAAAAABAE");
        saveDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        saveDTO.setModuleCode(PageModuleCodeEnum.MEMBER_RIGHTS.getCode());

        pageModuleContentService.save(saveDTO);
    }
    @Test
    public void saveGold() {
        PageModuleContentSaveDTO saveDTO = new PageModuleContentSaveDTO();
        saveDTO.setId(71L);
        saveDTO.setName("金刚区-0409");
        saveDTO.setPeopleLimit(1);
        saveDTO.setStartTime(null);
        saveDTO.setEndTime(null);
        saveDTO.setSort(1);
        saveDTO.setLinkType(3);
        saveDTO.setLink("1111");
        saveDTO.setAppId("44333232");

        saveDTO.setImageUrl("https://www.google.com.hk/");
        saveDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        saveDTO.setModuleCode(PageModuleCodeEnum.GOLD.getCode());
        pageModuleContentService.save(saveDTO);
    }
    @Test
    public void saveCarousel() {
        PageModuleContentSaveDTO saveDTO = new PageModuleContentSaveDTO();
        saveDTO.setId(58L);
        saveDTO.setName("轮播图-0411-2");
        saveDTO.setStyle(1);
        saveDTO.setPeopleLimit(2);
        saveDTO.setFirstJoinDuration(10);
        saveDTO.setBirthdayUser(1);
        saveDTO.setSort(8);
        saveDTO.setLinkType(2);
        saveDTO.setLink("https://www.google.com.hk11111111111/");

        saveDTO.setImageUrl("http://milkcard-dev.oss-cn-hangzhou.aliyuncs.com/picture/f05dd57130a34926a3938f513056f09c%E5%86%B0%E6%B7%87%E6%B7%8B.jpeg");
        saveDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        saveDTO.setModuleCode(PageModuleCodeEnum.CAROUSEL.getCode());
        pageModuleContentService.save(saveDTO);
    }

    @Test
    public void updateCarousel() {
        PageModuleContentSaveDTO saveDTO = new PageModuleContentSaveDTO();
        saveDTO.setId(17L);
        saveDTO.setName("轮播图-0409-2");
        saveDTO.setStyle(2);
        saveDTO.setPeopleLimit(2);
        saveDTO.setFirstJoinDuration(10);
        saveDTO.setBirthdayUser(1);
        saveDTO.setStartTime(new Date());
        saveDTO.setEndTime(new Date());
        saveDTO.setSort(2);
        saveDTO.setLinkType(6);

        saveDTO.setImageUrl("https://www.google.com.hk/");
        saveDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        saveDTO.setModuleCode(PageModuleCodeEnum.CAROUSEL.getCode());
        pageModuleContentService.save(saveDTO);
    }



    @Test
    public void getPersonalContent() {
        PageModuleContentQueryDTO queryDTO = new PageModuleContentQueryDTO();
//        queryDTO.setPageModuleContentId(0L);
        queryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        queryDTO.setModuleCode(PageModuleCodeEnum.PERSONAL_INFO.getCode());

        PageModuleContentVO content = pageModuleContentService.getContent(queryDTO);
        log.info(JSON.toJSONString(content));
    }

    @Test
    public void getGoldContent() {
        PageModuleContentQueryDTO queryDTO = new PageModuleContentQueryDTO();
        queryDTO.setPageModuleContentId(14L);
        queryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        queryDTO.setModuleCode(PageModuleCodeEnum.GOLD.getCode());

        PageModuleContentVO content = pageModuleContentService.getContent(queryDTO);
        log.info(JSON.toJSONString(content));
    }

    @Test
    public void getCarouselContent() {
        PageModuleContentQueryDTO queryDTO = new PageModuleContentQueryDTO();
        queryDTO.setPageModuleContentId(17L);
        queryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        queryDTO.setModuleCode(PageModuleCodeEnum.CAROUSEL.getCode());

        PageModuleContentVO content = pageModuleContentService.getContent(queryDTO);
        log.info(JSON.toJSONString(content));
    }

    @Test
    public void getMemberRightsContent() {
        PageModuleContentQueryDTO queryDTO = new PageModuleContentQueryDTO();
        queryDTO.setPageModuleContentId(15L);
        queryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        queryDTO.setModuleCode(PageModuleCodeEnum.MEMBER_RIGHTS.getCode());

        PageModuleContentVO content = pageModuleContentService.getContent(queryDTO);
        log.info(JSON.toJSONString(content));
    }


    @Test
    public void pageContent() {
        PageModuleContentPageQryDTO pageQryDTO = new PageModuleContentPageQryDTO();
        pageQryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        pageQryDTO.setModuleCode(PageModuleCodeEnum.CAROUSEL.getCode());
//        pageQryDTO.setName("83493489");
//        pageQryDTO.setLinkType(0);
//        pageQryDTO.setGrade("");
        pageQryDTO.setPageNum(1);
        pageQryDTO.setPageSize(10);
        pageQryDTO.setStatus(3);


        PageVO<PageModuleContentListVO> pageVO = pageModuleContentService.pageContent(pageQryDTO);
        log.info("pageVO: {}", JSON.toJSONString(pageVO) );

    }

    @Test
    public void pageMemberRightsContent() {
        PageModuleContentPageQryDTO pageQryDTO = new PageModuleContentPageQryDTO();
        pageQryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        pageQryDTO.setModuleCode(PageModuleCodeEnum.MEMBER_RIGHTS.getCode());
//        pageQryDTO.setName("83493489");
//        pageQryDTO.setLinkType(0);
        pageQryDTO.setGrade("1");
        pageQryDTO.setPageNum(1);
        pageQryDTO.setPageSize(10);


        PageVO<PageModuleContentListVO> pageVO = pageModuleContentService.pageContent(pageQryDTO);
        log.info("pageVO: {}", JSON.toJSONString(pageVO) );

    }


    @Test
    public void pageModuleConfigUpdate() {
        PageModuleSaveDTO saveDTO = new PageModuleSaveDTO();
        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("cols", 9);
//        jsonObject.put("rows", 2);
//        jsonObject.put("shape", 1);

        saveDTO.setConfig(jsonObject);
        saveDTO.setDisplay(0);
        saveDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        saveDTO.setModuleCode(PageModuleCodeEnum.PERSONAL_INFO.getCode());


        pageModuleService.update(saveDTO);
    }

    @Test
    public void pageModuleConfigGet() {
        PageModuleVO module = pageModuleService.getModule(PageCodeEnum.MEMBER_PAGE.getCode(), PageModuleCodeEnum.PERSONAL_INFO.getCode());
        log.info("module: {}", JSON.toJSONString(module));
    }

    @Test
    public void pageModuleConfigGetList() {
        List<PageModuleVO> modules = pageModuleService.getModuleList("memberPage");
        log.info("module: {}", JSON.toJSONString(modules));
    }

    @Test
    public void testSort() {
        PageModuleContentSortDTO sortDTO = new PageModuleContentSortDTO();
        sortDTO.setId(52L);
        sortDTO.setSort(3);
        pageModuleContentService.sort(sortDTO);
    }

    @Test
    public void testEnd() {
        PageModuleContentEndDTO endDTO = new PageModuleContentEndDTO();
        endDTO.setId(18L);
        pageModuleContentService.end(endDTO);
    }
}