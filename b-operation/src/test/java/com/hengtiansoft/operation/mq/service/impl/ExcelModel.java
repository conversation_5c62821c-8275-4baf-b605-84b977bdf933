package com.hengtiansoft.operation.mq.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
/**
 *
 * @author: shike
 */
@ContentRowHeight(15) //内容行高
@HeadRowHeight(20)//表头行高
@Data
public class ExcelModel {

    public static final String RESEXCELNAME = "document.xlsx";
    public static final String TEMPLATEEXCELNAME = "文章管理";
    public static final String SUFFIX = ".xlsx";

    /**
     * notice
     * 当采用模板上传Excel且.needHead(false)设置了不生成标题头  @ColumnWidth(10)标签将无效，根据模板头的长度来走
     */
    @ColumnWidth(10)//单元格长度
    @ExcelProperty(value = "序号", index = 0)
    private String order;

    @ColumnWidth(20)//单元格长度
    @ExcelProperty(value = "文章标题", index = 1)
    private String title;

    @ColumnWidth(15)//单元格长度
    @ExcelProperty(value = "单位", index = 2)
    private String company;

    @ColumnWidth(15)//单元格长度
    @ExcelProperty(value = "编号", index = 3)
    private String documentCode;

    @ColumnWidth(12)//单元格长度
    @ExcelProperty(value = "发文日期", index = 4)
    private String publishDate;

    @ColumnWidth(25)//单元格长度
    @ExcelProperty(value = "意见", index = 5)
    private String idea;
}