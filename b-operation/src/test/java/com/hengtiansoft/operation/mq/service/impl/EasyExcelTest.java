package com.hengtiansoft.operation.mq.service.impl;

import com.alibaba.excel.EasyExcel;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RunWith(JUnit4.class)
@Slf4j
public class EasyExcelTest {
    /**
     * 合并单元格
     * <p>1. 创建excel对应的实体对象 参照
     * <p>2. 创建一个merge策略 并注册
     * <p>3. 直接写即可
     */
    @Test
    public void mergeWrite() throws IOException {
        List<ExcelModel> datas = getData();
        String filename=EasyExcelTest.class.getResource("/").getPath() + System.currentTimeMillis() + ".xlsx";
        System.out.println("=================" + filename);
        File file1 = new File(filename);
        if(!file1.exists()){
            file1.createNewFile();
        }
        //打印单个sheel页
        EasyExcel.write(filename, ExcelModel.class )
                .autoCloseStream(Boolean.TRUE)
                .registerWriteHandler(new ExcelFillCellMergeStrategy())
                .sheet("测试导出合并单元格Excel").doWrite(datas);
    }

    private static List<ExcelModel> getData() {
        List<ExcelModel> list = new ArrayList<>();
        ExcelModel model1 = new ExcelModel();
        model1.setOrder("1");
        model1.setTitle("标题111");
        model1.setCompany("单位111");
        model1.setDocumentCode("编号111");
        model1.setIdea("意见111");
        model1.setPublishDate("2022-01-21");

        ExcelModel model2 = new ExcelModel();
        model2.setOrder("1");
        model2.setTitle("标题111");
        model2.setCompany("单位222");
        model2.setDocumentCode("编号222");
        model2.setIdea("意见111");
        model2.setPublishDate("2022-01-21");

        ExcelModel model3 = new ExcelModel();
        model3.setOrder("1");
        model3.setTitle("标题333");
        model3.setCompany("单位222");
        model3.setDocumentCode("编号222");
        model3.setIdea("意见333");
        model3.setPublishDate("2022-01-21");

        ExcelModel model4 = new ExcelModel();
        model4.setOrder("4");
        model4.setTitle("标题444");
        model4.setCompany("单位444");
        model4.setDocumentCode("编号444");
        model4.setIdea("意见444");
        model4.setPublishDate("2022-01-21");

        ExcelModel model5 = new ExcelModel();
        model5.setOrder("5");
        model5.setTitle("标题555");
        model5.setCompany("单位555");
        model5.setDocumentCode("编号555");
        model5.setIdea("意见555");
        model5.setPublishDate("2022-01-21");

        list.add(model1);
        list.add(model2);
        list.add(model3);
        list.add(model4);
        list.add(model5);
        return list;
    }
}
