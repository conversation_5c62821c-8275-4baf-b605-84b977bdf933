package com.hengtiansoft.operation.mq.service.impl;

import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.privilege.util.MonitorPageUtil;
import com.hengtiansoft.thirdpart.enumeration.BaiduLinkTypeEnum;
import com.hengtiansoft.thirdpart.enumeration.WeixinUrlLinkTypeEnum;
import com.hengtiansoft.thirdpart.interfaces.BaiduManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
class BaiduManagerImplTest {
    @Resource
    private BaiduManager baiduManager;
    @Test
    void testGetShortLink() {
        String longUrl = "https://mini-dev.ryytngroup.com";
        String linkType = BaiduLinkTypeEnum.ONE_YEAR.getCode();
        String shortLink = baiduManager.getShortLink(longUrl, linkType);
        log.info("获取百度短链 . 结果 --> shortLink:{}", shortLink);
    }
    @Test
    void testGetShortLink2() {
        // 长网址内容不安全
        String longUrl = "https://mini-dev2.ryytngroup.com";
        String linkType = BaiduLinkTypeEnum.ONE_YEAR.getCode();
        String shortLink = baiduManager.getShortLink(longUrl, linkType);
        log.info("获取百度短链 . 结果 --> shortLink:{}", shortLink);
    }

    @Test
    void testShortLing3(){
        CouponRule couponRule = new CouponRule();
        couponRule.setId(10266L);
        String localH5Domain = "https://h5.ryytngroup.com";
        String longLink = localH5Domain + MonitorPageUtil.H5_WECHAT_URL + "?id=" + couponRule.getId()+"&type="+ WeixinUrlLinkTypeEnum.COUPON.getCode();
        String shortLink = baiduManager.getShortLink(longLink, BaiduLinkTypeEnum.ONE_YEAR.getCode());
        System.out.println(shortLink);
    }
}