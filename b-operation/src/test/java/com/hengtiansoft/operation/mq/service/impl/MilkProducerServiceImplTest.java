package com.hengtiansoft.operation.mq.service.impl;

import com.hengtiansoft.operation.mq.service.MilkProducerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
class MilkProducerServiceImplTest {

    @Resource
    private MilkProducerService milkProducerService;
    @Test
    void producerDemo() {

        milkProducerService.producerDemo();
    }
}