package com.hengtiansoft.operation.coupon.service.impl;

import com.hengtiansoft.order.dao.CouponInfoDao;
import com.hengtiansoft.order.entity.po.CouponInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class CouponRuleServiceImplTest {
    @Resource
    private CouponInfoDao couponInfoDao;

    @Test
    public void test(){
        CouponInfo couponInfo = couponInfoDao.findById(790266L);
        couponInfoDao.update(couponInfo);
    }
    @Test
    public void updateCouponCancelByOrderNo(){
        CouponInfo couponInfo = couponInfoDao.findById(790246L);
        couponInfoDao.updateCouponCancelByOrderNo("LPK202309121026283455703", couponInfo.getType());
    }

}