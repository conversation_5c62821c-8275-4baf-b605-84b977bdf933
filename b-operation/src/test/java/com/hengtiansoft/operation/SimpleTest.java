package com.hengtiansoft.operation;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.util.BigDecimalUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.content.entity.dto.IndexModuleCommonConfigDTO;
import com.hengtiansoft.order.entity.dto.OrderAllTagDTO;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.entity.po.OrderSku;
import com.hengtiansoft.order.util.DispatchUtil;
import com.hengtiansoft.privilege.util.PeopleLabelUtil;
import org.apache.commons.lang3.tuple.MutablePair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RunWith(JUnit4.class)
public class SimpleTest {
    @Test
    public void testRecent(){
        MutablePair<Date, Date> recent = PeopleLabelUtil.recent(3);
        System.out.println(recent);
    }

    @Test
    public void testRecent1(){
        Date startTime = DateUtil.getStartTime(1);
        Date endTime = DateUtil.getEndTime(1);
        System.out.println(startTime);
        System.out.println(endTime);
    }

    @Test
    public void testDyna(){
        Date startDate = DateUtil.stringToDate("2022-12-10", "yyyy-MM-dd");
        MutablePair<Date, Date> recent = PeopleLabelUtil.dyna(startDate, null, 1);
        System.out.println(recent);

        // Date startDate = DateUtil.stringToDate("2022-12-10", "yyyy-MM-dd");
        MutablePair<Date, Date> recent1 = PeopleLabelUtil.dyna(null, 2, 1);
        System.out.println(recent1);
    }

    @Test
    public void tesetFeature() throws Exception{
        simpleTask();
        serialTask();
        andTask();
        orTask();
        complexTask();

        sleep(2000); // 等待子线程结束
        System.out.println("end.");
    }

    private static void simpleTask() throws ExecutionException, InterruptedException {
        // 1. runAsync 执行一个异步任务，没有返回值
        CompletableFuture.runAsync(()-> System.out.println("1. runAsync"));
        sleep(100);

        // 2. supplyAsync 执行一个异步任务，有返回值
        CompletableFuture<String> future = CompletableFuture.supplyAsync(()->{
            System.out.println("2.1 supplyAsync task be called");
            sleep(100);
            return "2.2 supplyAsync return value";
        });
        System.out.println("2.3 after supplyAsync");
        System.out.println(future.get());
        sleep(200);
    }

    private static void serialTask() throws ExecutionException, InterruptedException {
        // 3. thenRun
        CompletableFuture.supplyAsync(()->{
            System.out.println("3.1 supplyAsync begin");
            sleep(100);  // 用于证明B等待A结束才会执行
            return "3.2 supplyAsync end";
        }).thenRun(()->{
            System.out.println("3.3 thenRun be called.");
        });
        sleep(200);

        // 4. thenApply
        CompletableFuture<String> future4 = CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "4.1 apple";
        }).thenApply(returnVal->{
            return "4.2 " + returnVal + "-苹果";
        });
        System.out.println("4.3 get: " + future4.get());
        sleep(100);

        // 5. thenAccept
        CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "5.1 orange";
        }).thenAccept(returnVal->{
            System.out.println("5.2 " + returnVal + "-桔子");
        });
        sleep(100);

        // 6. thenCompose
        CompletableFuture<String> future6 = CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "6.1 apple";
        }).thenCompose((returnVal)->{
            return CompletableFuture.supplyAsync(()->{
                return "6.2 " + returnVal;
            });
        });
        System.out.println("6.3 get: " + future6.get());
        sleep(100);

        // 7. whenComplete
        CompletableFuture.supplyAsync(()->{
            sleep(100);
            if (true) {  //修改boolean值观察不同结果
                return "7.1 return value for whenComplete";
            } else {
                throw new RuntimeException("7.2 throw exception for whenComplete");
            }
        }).whenComplete((returnVal, throwable)->{
            System.out.println("7.2 returnVal: " + returnVal);  // 可以直接拿到返回值，不需要通过future.get()得到
            System.out.println("7.3 throwable: " + throwable);  // 异步任务抛出异常，并不会因为异常终止，而是会走到这里，后面的代码还会继续执行
        });
        sleep(100);

        // 8. exceptionally
        CompletableFuture<String> future8 = CompletableFuture.supplyAsync(()->{
            sleep(100);
            if (false) {  //修改boolean值观察不同结果
                return "8.1 return value for exceptionally";
            } else {
                throw new RuntimeException("8.2 throw exception for exceptionally");
            }
        }).exceptionally(throwable -> {
            throwable.printStackTrace();
            return "8.3 return value after dealing exception.";
        });
        System.out.println("8.4 get: " + future8.get());
        sleep(100);

        // 9. handle
        CompletableFuture<String> future9 = CompletableFuture.supplyAsync(()->{
            sleep(100);
            if (false) {  //修改boolean值观察不同结果
                return "9.1 return value for handle";
            } else {
                throw new RuntimeException("9.2 throw exception for handle");
            }
        }).handle((retuanVal, throwable)->{
            System.out.println("9.3 retuanVal: " + retuanVal);
            System.out.println("9.4 throwable: " + throwable);
            return "9.5 new return value.";
        });
        System.out.println("9.6 get: " + future9.get());
        sleep(100);
    }

    private static void andTask() throws ExecutionException, InterruptedException {
        // 10. thenCombine 合并结果
        CompletableFuture<String> future10 = CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "10.1 TaskA return value";
        }).thenCombine(CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "10.2 TaskB return value";
        }), (taskAReturnVal, taskBReturnVal) -> taskAReturnVal + taskBReturnVal);
        System.out.println("10.3 get: " + future10.get());
        sleep(200);

        // 11. thenAcceptBoth
        CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "11.1 TaskA return value";
        }).thenAcceptBoth(CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "11.2 TaskB return value";
        }), (taskAReturnVal, taskBReturnVal) -> System.out.println(taskAReturnVal + taskBReturnVal));
        sleep(200);

        // 12. runAfterBoth A，B都执行完后才执行C，C不关心前面任务的返回值
        CompletableFuture.supplyAsync(()->{
            sleep(200);  // 虽然这个任务先执行，但是执行时间比下面的任务长，所以最后会使用下面的返回结果
            System.out.println("12.1 TaskA be called.");
            return "12.2 TaskA return value";
        }).runAfterBoth(CompletableFuture.supplyAsync(()->{
            sleep(100);
            System.out.println("12.3 TaskB be called.");
            return "12.4 TaskB return value";
        }), () -> System.out.println("12.5 TaskC be called."));
        sleep(300);
    }

    private static void orTask() throws ExecutionException, InterruptedException {
        // 13. applyToEither 使用A,B两个异步任务优先返回的结果
        CompletableFuture<String> future13 = CompletableFuture.supplyAsync(()->{
            sleep(200);  // 虽然这个任务先执行，但是执行时间比下面的任务长，所以最后会使用下面的返回结果
            System.out.println("13.1 TaskA be called"); // 用于证明拿到B的结果后，A还会继续执行，并不会终止
            return "13.2 TaskA return value";
        }).applyToEither(CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "13.3 TaskB return value";
        }), (returnVal) -> returnVal);
        System.out.println("13.4 get: " + future13.get());
        sleep(300);

        // 14. acceptEither 使用A,B两个异步任务优先返回的结果
        CompletableFuture.supplyAsync(()->{
            sleep(200);  // 虽然这个任务先执行，但是执行时间比下面的任务长，所以最后会使用下面的返回结果
            return "14.1 TaskA return value";
        }).acceptEither(CompletableFuture.supplyAsync(()->{
            sleep(100);
            return "14.2 TaskB return value";
        }), (returnVal) -> System.out.println(returnVal));
        sleep(300);

        // 15. runAfterEither A，B任意一个执行完后就执行C，C不关心前面任务的返回值
        CompletableFuture.supplyAsync(()->{
            sleep(200);  // 虽然这个任务先执行，但是执行时间比下面的任务长，所以最后会使用下面的返回结果
            System.out.println("15.1 TaskA be called.");
            return "15.2 TaskA return value";
        }).runAfterEither(CompletableFuture.supplyAsync(()->{
            sleep(100);
            System.out.println("15.3 TaskB be called.");
            return "15.4 TaskB return value";
        }), () -> System.out.println("15.5 TaskC be called."));
        sleep(300);
    }

    private static void complexTask() throws ExecutionException, InterruptedException {
        // 16. anyOf
        CompletableFuture future16 = CompletableFuture.anyOf(CompletableFuture.supplyAsync(()->
        {
            sleep(300);
            System.out.println("16.1 TaskA be called.");
            return "16.2 TaskA return value.";
        }), CompletableFuture.supplyAsync(()->{
            sleep(100);
            System.out.println("16.3 TaskB be called.");
            return "16.4 TaskB return value.";
        }));
        System.out.println("16.5 get: " + future16.get());
        sleep(400);

        // 17. allOf
        CompletableFuture<Void> future17 = CompletableFuture.allOf(CompletableFuture.supplyAsync(()->
        {
            sleep(300);
            System.out.println("17.1 TaskA be called.");
            return "17.2 TaskA return value.";
        }), CompletableFuture.supplyAsync(()->{
            sleep(100);
            System.out.println("17.3 TaskB be called.");
            return "17.4 TaskB return value.";
        }));
        System.out.println("17.5 get: " + future17.get()); // allOf没有返回值
    }

    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    public void tesetLowNew() throws Exception{
        List<LocalDate>  nextShippingDates = getLowMilkDispatchDay2LocalDate(LocalDate.now(), 8);
        System.out.println(nextShippingDates.size());
        for (LocalDate deliveryDate : nextShippingDates) {
            System.out.println(deliveryDate);
        }

        System.out.println("====================");
        List<LocalDate>  nextShippingDates1 = getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-02-27", DateTimeFormatter.ofPattern("yyyy-MM-dd")), 8);
        System.out.println(nextShippingDates1.size());
        for (LocalDate deliveryDate : nextShippingDates1) {
            System.out.println(deliveryDate);
        }

        System.out.println("====================");
        List<LocalDate>  nextShippingDates2 = getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-03-04", DateTimeFormatter.ofPattern("yyyy-MM-dd")), 8);
        System.out.println(nextShippingDates2.size());
        for (LocalDate deliveryDate : nextShippingDates2) {
            System.out.println(deliveryDate);
        }
    }
    public static List<LocalDate> getLowMilkDispatchDay2LocalDate(LocalDate today, int n) {
        // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<LocalDate> dates = Lists.newArrayList();
        LocalDate saturday = null;
        LocalDate sunday = null;
        LocalDate wednesday = null;
        LocalDate thursday = null;
        for (int i = 0; i < n; i++) {
            if(i==0){
                if (today.getDayOfWeek().getValue() < DayOfWeek.TUESDAY.getValue()) {
                    saturday = today.plusDays(DayOfWeek.SATURDAY.getValue() - today.getDayOfWeek().getValue());
                    dates.add(saturday);
                }else{
                    saturday = today.plusDays(DayOfWeek.SATURDAY.getValue() - today.getDayOfWeek().getValue() + 7);
                    dates.add(saturday);
                }
                sunday = saturday.plusDays(1);
                dates.add(sunday);

                if (today.getDayOfWeek().getValue() < DayOfWeek.SATURDAY.getValue()) {
                    wednesday = today.plusDays(DayOfWeek.WEDNESDAY.getValue() - today.getDayOfWeek().getValue() + 7);
                    dates.add(wednesday);
                }else{
                    wednesday = today.plusDays(DayOfWeek.WEDNESDAY.getValue() - today.getDayOfWeek().getValue() + 14);
                    dates.add(wednesday);
                }
                thursday = wednesday.plusDays(1);
                dates.add(thursday);

            }else{
                saturday = saturday.plusDays(7);
                sunday = sunday.plusDays(7);
                dates.add(saturday);
                dates.add(sunday);

                wednesday = wednesday.plusDays(7);
                thursday = thursday.plusDays(7);
                dates.add(wednesday);
                dates.add(thursday);
            }
        }
        dates = dates.stream().sorted().collect(Collectors.toList());
        return dates;
    }
    @Test
    public void testGetLowMilkDispatchDay2LocalDate() {
        // Test case 1: Test with today's date and n=1
        LocalDate today = LocalDate.now();
        List<LocalDate> nextShippingDates1_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(today, 1, true);
        List<LocalDate> nextShippingDates1_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(today, 1, false);

        // Test case 2: Test with today's date and n=2
        List<LocalDate> nextShippingDates2_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2024-06-01"), 2,false);
        List<LocalDate> nextShippingDates2_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2024-05-28"), 2,false);
        System.out.println(nextShippingDates2_1);
        System.out.println(nextShippingDates2_2);

        // Test case 3: Test with today's date and n=0
        List<LocalDate> nextShippingDates3_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-12"), 2, true);
        List<LocalDate> nextShippingDates3_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-12"), 2, false);

        // Test case 4: Test with a date that is a Saturday and n=1
        List<LocalDate> nextShippingDates4_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-13"), 2,true);
        List<LocalDate> nextShippingDates4_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-13"), 2,false);

        // Test case 5: Test with a date that is a Sunday and n=1
        List<LocalDate> nextShippingDates5_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-14"), 2, true);
        List<LocalDate> nextShippingDates5_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-14"), 2, false);

        List<LocalDate> nextShippingDates6_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-15"), 2, true);
        List<LocalDate> nextShippingDates6_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-15"), 2, false);

        List<LocalDate> nextShippingDates7_1 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-16"), 2, true);
        List<LocalDate> nextShippingDates7_2 = DispatchUtil.getLowMilkDispatchDay2LocalDate(LocalDate.parse("2023-04-16"), 2, false);
    }

    @Test
    public void testJSON(){
        JSONObject jsonObject = null;
        IndexModuleCommonConfigDTO configDTO = jsonObject.toJavaObject(IndexModuleCommonConfigDTO.class);
        System.out.println(configDTO);
    }

    @Test
    public void t1(){
        OrderInfo a = new OrderInfo();
        a.setUserId(1L);
        OrderInfo b = new OrderInfo();
        b.setUserId(2L);
        List<OrderInfo> orderInfoList = Lists.newArrayList(a, b);
        Integer userCount = orderInfoList.stream().map(OrderInfo::getUserId).distinct().toArray().length;
        System.out.println(userCount);
    }

    @Test
    public void t2(){
        OrderSku a = new OrderSku();
        a.setCount(null);
        OrderSku b = new OrderSku();
        b.setCount(null);
        List<OrderSku> lsit = Lists.newArrayList(a, b);
        Integer sum = lsit.stream().map(OrderSku::getCount).filter(Objects::nonNull).reduce(0, Integer::sum);
        System.out.println(sum);
    }

    @Test
    public void t3(){
        List<Integer> skipTypes = Lists.newArrayList(1);
        Integer num = 4;
        List<OrderAllTagDTO> allTagDTO = Lists.newArrayList();
        OrderAllTagDTO tag1 = new OrderAllTagDTO();
        tag1.setType(1);
        tag1.setTags(Lists.newArrayList("11", "12", "13"));

        OrderAllTagDTO tag2 = new OrderAllTagDTO();
        tag2.setType(2);
        tag2.setTags(Lists.newArrayList("21", "22", "23"));

        OrderAllTagDTO tag3 = new OrderAllTagDTO();
        tag3.setType(3);
        tag3.setTags(Lists.newArrayList());

        allTagDTO.add(tag1);
        allTagDTO.add(tag2);
        allTagDTO.add(tag3);

        List<String> collect = allTagDTO.stream().filter(tmp -> !skipTypes.contains(tmp.getType()))
                .flatMap(tmp -> tmp.getTags().stream().limit(num)).collect(Collectors.toList());
        System.out.println(collect);
    }

    @Test
    public void testBigdecimal(){
        BigDecimal b1 = new BigDecimal("1.1111");
        System.out.println(BigDecimalUtils.convertTo2(b1));

        BigDecimal b2 = new BigDecimal("1.1110");
        System.out.println(BigDecimalUtils.convertTo2(b2));

        BigDecimal b3 = new BigDecimal("1.1100");
        System.out.println(BigDecimalUtils.convertTo2(b3));

        BigDecimal b4 = new BigDecimal("1.1000");
        System.out.println(BigDecimalUtils.convertTo2(b4));

        BigDecimal b5 = new BigDecimal("1.1151");
        System.out.println(BigDecimalUtils.convertTo2(b5));

        BigDecimal b6 = new BigDecimal("1.0000");
        System.out.println(BigDecimalUtils.convertTo2(b6));
    }
}
