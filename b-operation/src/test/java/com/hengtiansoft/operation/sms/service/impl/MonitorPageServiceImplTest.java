package com.hengtiansoft.operation.sms.service.impl;

import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.thirdpart.interfaces.FileManager;
import com.hengtiansoft.thirdpart.interfaces.QrcodeManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class MonitorPageServiceImplTest {
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private QrcodeManager qrcodeManager;
    @Resource
    private FileManager fileManager;

    @Test
    public void testQrcode1(){
        String url1 = "https://www.baidu.com/";
        byte[] qrcode1 = qrcodeManager.getQrcode(url1, 800);
        String ossUrl1 = fileManager.uploadQrcode(qrcode1);
        System.out.println(ossUrl1);

        String url2 = "https://mp.weixin.qq.com/s/4w7aoECfQ2HcfHUADg7poQ/";
        byte[] qrcode2 = qrcodeManager.getQrcode(url2, 800);
        String ossUrl2 = fileManager.uploadQrcode(qrcode2);
        System.out.println(ossUrl2);
    }
}