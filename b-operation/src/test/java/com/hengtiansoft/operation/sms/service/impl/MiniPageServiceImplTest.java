package com.hengtiansoft.operation.sms.service.impl;

import com.hengtiansoft.privilege.util.MiniPageUtil;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WechatShortLinkResp;
import com.hengtiansoft.thirdpart.interfaces.FileManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniSmsManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class MiniPageServiceImplTest {
    @Resource
    private WeChatMiniSmsManager weChatMiniSmsManager;
    @Resource
    private FileManager fileManager;

    @Test
    public void testMiniCode(){
        String pageUrl1 = MiniPageUtil.MINI_PAGE + "?id=134";
        WechatShortLinkResp shortLinkResp1 = weChatMiniSmsManager.getShortLink(pageUrl1, "活动", false);
        System.out.println(shortLinkResp1);

        String pageUrl2 = MiniPageUtil.MINI_PAGE;
        WechatShortLinkResp shortLinkResp2 = weChatMiniSmsManager.getShortLink(pageUrl2, "活动", false);
        System.out.println(shortLinkResp2);
    }

    @Test
    public void test(){
        String scene = "id=134";
        byte[] bytes = weChatMiniSmsManager.miniCode(scene, MiniPageUtil.MINI_PAGE, 800);
        String qrcode = fileManager.uploadQrcode(bytes);
        System.out.println(qrcode);
    }

    @Test
    public void testActivity(){
        // 生成杭野活动太阳码
        String scene = "start_rangers_data_check=1b07070300495c5c101c1d001c1f165d051c1f10161d141a1d165d101c1e";
        // String pageUrl= "packageB/pages/benefit/index";
        String pageUrl= "pages/index/index";
        byte[] bytes = weChatMiniSmsManager.miniCode(scene, pageUrl, 800);
        String qrcode = fileManager.uploadQrcode(bytes);
        System.out.println(qrcode);
    }

}