package com.hengtiansoft.operation.item.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.DiscountActivityDTO;
import com.hengtiansoft.item.entity.dto.DiscountSupDTO;
import com.hengtiansoft.item.entity.vo.DiscountActivityVO;
import com.hengtiansoft.operation.item.service.DiscountActivityService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-02-23 10:36
 **/
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class DiscountActivityServiceImplTest {

    @Autowired
    private DiscountActivityService discountActivityService;

    @Test
    public void getListNoParam() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(1);
        dto.setPageSize(20);
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void getListSetPage2() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(2);
        dto.setPageSize(10);
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void getListWithSkuCode() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(1);
        dto.setPageSize(20);
        dto.setSkuCode("3333333432");
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void getListWithProductId() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(1);
        dto.setPageSize(20);
        dto.setProductId(258L);
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void getListWithName() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(1);
        dto.setPageSize(20);
        dto.setName("汉最多支");
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void getListWithStatus() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(1);
        dto.setPageSize(20);
        dto.setStatus(1);
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void getListWithStatusList() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setPageNum(1);
        dto.setPageSize(20);
        dto.setStatusList(Lists.newArrayList(1,2));
        PageVO<DiscountActivityVO> list = discountActivityService.getList(dto);
        log.info("list: {}", JSON.toJSONString(list));
    }


    @Test
    public void getActivity() {
        DiscountActivityVO discountActivityVO = discountActivityService.get(106L);
        log.info("activity: {}", JSON.toJSONString(discountActivityVO));
    }


    @Test
    public void testSupplementStock() {
        List<DiscountSupDTO> list = Lists.newArrayList();
        DiscountSupDTO dto = new DiscountSupDTO();
        dto.setId(103L);
        dto.setAddCnt(10);
        list.add(dto);

        DiscountSupDTO dto1 = new DiscountSupDTO();
        dto1.setId(104L);
        dto1.setAddCnt(1);
        list.add(dto1);

        discountActivityService.supplement(list);
    }

}