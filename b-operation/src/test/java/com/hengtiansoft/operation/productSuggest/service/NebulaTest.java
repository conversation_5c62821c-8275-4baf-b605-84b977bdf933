package com.hengtiansoft.operation.productSuggest.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.EncryptUtils;
import com.hengtiansoft.common.util.HttpClientTemplate;
import com.hengtiansoft.privilege.dao.PeopleDao;
import com.hengtiansoft.privilege.entity.po.People;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class NebulaTest {

    @Resource
    private RedisOperation redisOperation;
    @Resource
    private HttpClientTemplate httpClientTemplate;
    @Resource
    private PeopleDao peopleDao;

    private String appid = "87lDGp80RCE79650";
    private String secret = "rL6XOmD3srs9UCinILGlVmoFFoS0406D";

    private String accessToken = "wmDzC121ece8194d271bf166cf98eb587a9303";
    private String tag = "eIoI4CVLA0u5m0MkjiMPwm0AfIkMutXnL2zUAhVGxRo";


    /**
     * 获取星云token
     */
    @Test
    public void test(){
        String url = "https://open-api.iyouke.com/smp/open-api/get-access-token?appid=" + appid;
        // 获取当前时间戳
        long timestamp = Instant.now().getEpochSecond();
        String timeString = Long.toString(timestamp);
        // 拼接时间戳和密钥
        String toHash = timeString + secret;
        // 生成MD5哈希
        String token = EncryptUtils.encryptMd5(toHash);
        Map<String, String> params = new HashMap<>();
        params.put("token",token);
        params.put("time",timeString);
        params.put("appid", appid);
        try {
            String doPost = httpClientTemplate.doPostJson(url, null, JSON.toJSONString(params), null ,null);
            System.out.println(doPost);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

    /**
     * 重新计算人群包
     */
    @Test
    public void test1(){
        String url = "https://open-api.iyouke.com/smp/open-api/cdp/marketing/group/run?appid=" + appid + "&access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("tag", tag);
        try {
            String doPost = httpClientTemplate.doPostJson(url, null, JSON.toJSONString(params), null ,null);
            System.out.println(doPost);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

    /**
     * 批量移除人群包
     */
    @Test
    public void test2(){
        String url = "https://open-api.iyouke.com/smp/open-api/cdp/marketing/group/user/remove?appid=" + appid + "&access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("15336584653");
        params.put("tag", tag);
        params.put("removeALL", true);
        params.put("userFlags", list);
        try {
            String doPost = httpClientTemplate.doPostJson(url, null, JSON.toJSONString(params), null ,null);
            System.out.println(doPost);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

    /**
     * 批量导入人群包
     */
    @Test
    public void test3(){
        String url = "https://open-api.iyouke.com/smp/open-api/cdp/marketing/group/user/add?appid=" + appid + "&access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("15336584653");
        params.put("tag", tag);
        params.put("userFlags", list);
        try {
            String doPost = httpClientTemplate.doPostJson(url, null, JSON.toJSONString(params), null ,null);
            System.out.println(doPost);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }


    /**
     * 创建人群包
     */
    @Test
    public void test4(){
        String url = "https://open-api.iyouke.com/smp/open-api/cdp/marketing/group/create?appid=" + appid + "&access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("groupId", 3);
        params.put("groupName", "it测试勿动it测试勿动it测试勿动it测试it测(奶卡)");
        params.put("importType", 1);
        try {
            String doPost = httpClientTemplate.doPostJson(url, null, JSON.toJSONString(params), null ,null);
            System.out.println(doPost);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void test5(){
        for (int i = 1; i <= 10; i++) {
            PageHelper.startPage(i, 10);
            List<People> peoples = peopleDao.findByLabelId(548L);
            System.out.println(peoples);
        }
    }

}