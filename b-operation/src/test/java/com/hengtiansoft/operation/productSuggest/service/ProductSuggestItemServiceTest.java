package com.hengtiansoft.operation.productSuggest.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSaveDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSortDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemListVO;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemSelectedVO;
import com.hengtiansoft.item.manager.ProductSuggestItemManager;
import com.hengtiansoft.order.util.CouponUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-21 18:52
 **/

@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class ProductSuggestItemServiceTest {

    @Resource
    private ProductSuggestItemService productSuggestItemService;
    @Resource
    private ProductSuggestItemManager productSuggestItemManager;
    @Resource
    private RedisOperation redisOperation;

    @Test
    public void productSuggestItemList() {

        ProductSuggestItemListDTO dto = new ProductSuggestItemListDTO();
        dto.setType(2);
        dto.setProductSuggestId(2L);
        PageVO<ProductSuggestItemListVO> listVOPageVO = productSuggestItemService.productSuggestItemList(dto);
        log.info("listVOPageVO: {}", JSON.toJSONString(listVOPageVO));
    }

    @Test
    public void productSuggestItemListWithProductId() {

        ProductSuggestItemListDTO dto = new ProductSuggestItemListDTO();
        dto.setType(2);
        dto.setProductId(4434L);
        dto.setProductSuggestId(2L);
        PageVO<ProductSuggestItemListVO> listVOPageVO = productSuggestItemService.productSuggestItemList(dto);
        log.info("listVOPageVO: {}", JSON.toJSONString(listVOPageVO));
    }

    @Test
    public void productSuggestItemSave() {
        ProductSuggestItemSaveDTO saveDTO = new ProductSuggestItemSaveDTO();
        saveDTO.setProductSuggestId(5L);
        saveDTO.setProductIds(Lists.newArrayList(434L, 853L, 854L, 863L, 353L, 376L, 378L, 380L, 382L, 318L, 322L, 324L,
                326L, 327L, 332L, 334L, 848L, 316L, 449L, 445L,268L,446L, 1800L));
        saveDTO.setType(1);
        productSuggestItemService.productSuggestItemSave(saveDTO);
    }


    @Test
    public void productSuggestItemSaveReverse() {
        ProductSuggestItemSaveDTO saveDTO = new ProductSuggestItemSaveDTO();
        saveDTO.setProductSuggestId(26L);
        saveDTO.setProductIds(Lists.newArrayList(426L));
        saveDTO.setType(2);
        productSuggestItemService.productSuggestItemSave(saveDTO);
    }



    @Test
    public void sortProductSuggestItem() {
        ProductSuggestItemSortDTO sortDTO = new ProductSuggestItemSortDTO();
        sortDTO.setSort(2);
        sortDTO.setId(1L);
        productSuggestItemService.sortProductSuggestItem(sortDTO);
    }

    @Test
    public void deleteProductSuggestItem() {
        productSuggestItemService.deleteProductSuggestItem(1L);
    }

    @Test
    public void productSuggestItemSelected() {
        List<ProductSuggestItemSelectedVO> itemSelectedVOS = productSuggestItemService.productSuggestItemSelected(2, 1L);
        log.info("itemSelectedVos: {}", JSON.toJSONString(itemSelectedVOS));
    }

    @Test
    public void export() {
        productSuggestItemService.export(new ReportFormExportBackupDTO(), null);
    }

    @Test
    public void autoRecommend() {
        productSuggestItemManager.autoRecommendProduct();
    }

    @Test
    public void redis() {
        String key = CouponUtil.COUPON_SINGLE_PRODUCT + 99L;
        System.out.println(redisOperation.hexists(key,String.valueOf(1)));
        redisOperation.hset(key, String.valueOf(1), true);
        System.out.println(redisOperation.hexists(key,String.valueOf(1)));
        System.out.println(redisOperation.ttl(key));
    }


}