package com.hengtiansoft.operation.label.service.impl;

import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.sms.service.PeopleLabelService;
import com.hengtiansoft.privilege.entity.po.PeopleLabel;
import com.hengtiansoft.privilege.entity.vo.PeopleLabelVO;
import com.hengtiansoft.privilege.manager.PeopleLabelManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
class PeopleLabelServiceImplTest {

    @Resource
    private PeopleLabelService peopleLabelService;
    @Resource
    private PeopleLabelManager peopleLabelManager;

    @Test
    void testDealRule() {
        PeopleLabel peopleLabel = peopleLabelManager.get(2L, true);
        peopleLabelService.compute(peopleLabel);
    }
}