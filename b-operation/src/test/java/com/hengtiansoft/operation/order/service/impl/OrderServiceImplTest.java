package com.hengtiansoft.operation.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.hengtiansoft.operation.order.service.OrderService;
import com.hengtiansoft.order.entity.dto.OrderSearchDTO;
import com.hengtiansoft.thirdpart.util.OmsUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.*;


/**
 * <AUTHOR>
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class OrderServiceImplTest {
    @Resource
    private OrderService orderService;
    @Resource
    private OmsUtil omsUtil;

    private static final String URL = "https://ai-api.betteryeah.com/v1/oapi/agent/chat";
    private static final String ACCESS_KEY = "ZDZmYTRkNWNhYTUzNDA3M2EyMTEzZjhlMjAwYmQ2OTgsNTg5NywxNzM0MzU1Nzc5NjIy";
    private static final String WORKSPACE_ID = "d6fa4d5caa534073a2113f8e200bd698";
    private static final String ROBOT_ID = "bee9b32607ab41bca49cad0e4b39f0bc";
    private static final String CONVERSATION_ID = "33cb3e64e93140cba61f7abf0e8e51ad";

    @Test
    void export() {
        OrderSearchDTO dto = new OrderSearchDTO();
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        orderService.export(dto);
    }


    @Test
    void test() {
        // 创建线程池（保留原有配置）
        ExecutorService executor = new ThreadPoolExecutor(
                10, 10, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 使用CountDownLatch跟踪请求进度
        int totalRequests = 10 * 20; // 10线程*100次
        CountDownLatch latch = new CountDownLatch(totalRequests);

        long startTime = System.currentTimeMillis();

        try {
            // 提交任务
            for (int i = 0; i < 20; i++) {
                executor.submit(() -> {
                    for (int j = 0; j < 10; j++) {
                        sendRequestWithMap(latch);
                    }
                });
            }

            try {
                latch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("总耗时：{}ms，TPS：{}/s", totalTime, (totalRequests * 1000L)/totalTime);

        } finally {
            executor.shutdown();
        }
    }

    private void sendRequestWithMap(CountDownLatch latch) {
        try {
            // 使用HashMap构建请求参数
            HashMap<String, Object> paramMap = new HashMap<>(4);
            paramMap.put("robot_id", ROBOT_ID);
            paramMap.put("conversation_id", CONVERSATION_ID);
            paramMap.put("content", "牛奶推荐");
            paramMap.put("response_mode", "streaming");

            cn.hutool.http.HttpResponse response = cn.hutool.http.HttpRequest.post(URL)
                    .header("Access-Key", ACCESS_KEY)
                    .header("Workspace-Id", WORKSPACE_ID)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(paramMap)) // 自动序列化为JSON
                    .timeout(5000)
                    .execute();

            log.debug("状态码：{}", response.getStatus());
        } catch (Exception e) {
            log.error("请求异常：", e);
        } finally {
            latch.countDown();
        }
    }

    @Test
    void getAccessToken(){
        String accessToken = omsUtil.getAccessToken();
        System.out.println(accessToken);
    }
}