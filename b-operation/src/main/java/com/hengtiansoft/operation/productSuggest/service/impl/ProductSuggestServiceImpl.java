package com.hengtiansoft.operation.productSuggest.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.enumeration.CommonPeopleLimitEnum;
import com.hengtiansoft.common.util.CommonOptUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.item.dao.ProductSuggestItemDao;
import com.hengtiansoft.item.entity.dto.ProductSuggestDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestSortDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.ProductSuggest;
import com.hengtiansoft.item.entity.po.ProductSuggestItem;
import com.hengtiansoft.item.entity.vo.ProductSuggestExcelVO;
import com.hengtiansoft.item.entity.vo.ProductSuggestVO;
import com.hengtiansoft.item.manager.ProductSuggestManager;
import com.hengtiansoft.item.utils.ProductSuggestUtil;
import com.hengtiansoft.operation.productSuggest.service.ProductSuggestService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.github.pagehelper.page.PageMethod.startPage;

@Slf4j
@Service
public class ProductSuggestServiceImpl implements ProductSuggestService {

    @Resource
    private ProductSuggestManager productSuggestManager;
    @Resource
    private ProductSuggestItemDao productSuggestItemDao;

    @Override
    public PageVO<ProductSuggestVO> getList(ProductSuggestListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<ProductSuggest> list = productSuggestManager.findByCondition(dto);
        if (CollectionUtils.isEmpty(list)) {
            return PageUtils.emptyPage(dto);
        }

        return PageUtils.convert(list, data->{
            ProductSuggestVO vo = ProductSuggestUtil.convert2VO(data);
            return vo;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProductSuggestDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        //参数校验
        validParam(dto);
        ProductSuggest po = ProductSuggestUtil.buildPO(dto);
        po.setStatus(CommonOptUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
        po.setOperator(user.getUserName());

        ProductSuggest existPo = productSuggestManager.findBySort(dto.getSort(), dto.getPosition());

        if(Objects.isNull(dto.getId())){
            // 无id，新增
            if(null != existPo){
                productSuggestManager.updateGreaterSort(dto.getSort(), dto.getPosition());
            }
            productSuggestManager.insert(po);
            if(null != dto.getSourceId()){
                ProductSuggest sourcePo = productSuggestManager.findById(dto.getSourceId());
                Assert.notNull(sourcePo, "复制失败");
                List<ProductSuggestItem> itemList = productSuggestItemDao.findSelectedItemsBySuggestId(dto.getSourceId());
                if(CollectionUtils.isEmpty(itemList)){
                    return;
                }
                itemList.forEach(item-> {
                    item.setId(null);
                    item.setCreateTime(new Date());
                    item.setUpdateTime(new Date());
                    item.setProductSuggestId(po.getId());
                });
                productSuggestItemDao.batchInsert(itemList);
            }
        }else{
            ProductSuggest old = productSuggestManager.findById(dto.getId());
            Assert.notNull(old, "推荐位不存在");

            if(null != existPo && !Objects.equals(existPo.getId(), dto.getId())){
                productSuggestManager.updateGreaterSort(dto.getSort(), dto.getPosition());
            }
            BeanUtil.copyProperties(po, old, CopyOptions.create().setIgnoreNullValue(true));
            old.setLabelId(po.getLabelId());
            old.setUpdateTime(new Date());
            productSuggestManager.updateAll(old);
        }
    }

    @Override
    public ProductSuggestVO get(Long id) {
        ProductSuggest old = productSuggestManager.findById(id);
        Assert.notNull(old, "推荐位不存在");
        return ProductSuggestUtil.convert2VO(old);
    }

    @Override
    public void delete(Long id) {
        ProductSuggest old = productSuggestManager.findById(id);
        Assert.notNull(old, "推荐位不存在");
        if(!Objects.equals(CommonActivityStatusEnum.END.getCode(), old.getStatus())){
            throw new BusinessException("只有已结束状态下才可以删除");
        }
        productSuggestManager.delete(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(ProductSuggestSortDTO dto) {
        if(null == dto.getId() || null == dto.getSort()){
            throw new BusinessException("参数缺失");
        }
        ProductSuggest old = productSuggestManager.findById(dto.getId());
        Assert.notNull(old, "推荐位不存在");
        ProductSuggest existPo = productSuggestManager.findBySort(dto.getSort(), dto.getPosition());
        if(null != existPo && !Objects.equals(existPo.getId(), dto.getId())){
            productSuggestManager.updateGreaterSort(dto.getSort(), dto.getPosition());
        }
        ProductSuggest updatePo = new ProductSuggest();
        updatePo.setId(dto.getId());
        updatePo.setSort(dto.getSort());
        productSuggestManager.update(updatePo);
    }

    @Override
    public void end(Long id) {
        ProductSuggest old = productSuggestManager.findById(id);
        Assert.notNull(old, "推荐位不存在");
        if(Objects.equals(CommonActivityStatusEnum.END.getCode(), old.getStatus())){
            throw new BusinessException("该推荐位已经是已结束状态");
        }
        ProductSuggest updatePo = new ProductSuggest();
        updatePo.setId(id);
        updatePo.setStatus(CommonActivityStatusEnum.END.getCode());
        updatePo.setEndTime(new Date());
        productSuggestManager.update(updatePo);
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(ProductSuggestExcelVO.class).build();
        ProductSuggestListDTO queryDTO = new ProductSuggestListDTO();
        queryDTO.setId(dto.getProductSuggestId());
        queryDTO.setStatus(dto.getStatus());
        queryDTO.setName(dto.getName());
        queryDTO.setPosition(dto.getPosition());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<ProductSuggestVO> productSuggestPageVO = this.getList(queryDTO);
            Pagination pagination = productSuggestPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<ProductSuggestExcelVO> productSuggestExcelVOList = ProductSuggestUtil.convert2ExcelVO(productSuggestPageVO.getList());
            excelWriter.write(productSuggestExcelVOList, writeSheet);
        }while (pageNum <= pages);
    }


    private void validParam(ProductSuggestDTO dto) {
        if(Objects.equals(CommonPeopleLimitEnum.LABEL.getCode(), dto.getPeopleLimit())){
            if(CollectionUtils.isEmpty(dto.getLabelIds())){
                throw new BusinessException("人群包不能为空");
            }
        }
        if(dto.getEndTime().before(dto.getStartTime())){
            throw new BusinessException("活动结束时间不能早于开始时间");
        }
    }
}
