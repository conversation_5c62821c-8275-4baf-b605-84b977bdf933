package com.hengtiansoft.operation.productSuggest.controller;


import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.ProductSuggestDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestSortDTO;
import com.hengtiansoft.item.entity.vo.ProductSuggestVO;
import com.hengtiansoft.operation.productSuggest.service.ProductSuggestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "商品推荐位B端")
@RequestMapping("/productSuggest")
public class ProductSuggestController {

    @Resource
    private ProductSuggestService productSuggestService;

    @ApiOperation("分页列表")
    @PostMapping("/list")
    public Response<PageVO<ProductSuggestVO>> getList(@RequestBody ProductSuggestListDTO dto) {
        return ResponseFactory.success(productSuggestService.getList(dto));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Object> save(@RequestBody @Validated ProductSuggestDTO dto) {
        productSuggestService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<ProductSuggestVO> get(@RequestParam Long id) {
        return ResponseFactory.success(productSuggestService.get(id));
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Object> delete(@RequestParam Long id) {
        productSuggestService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("排序")
    @PostMapping("/sort")
    public Response<Object> sort(@RequestBody @Validated ProductSuggestSortDTO dto) {
        productSuggestService.sort(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("结束")
    @GetMapping("/end")
    public Response<Object> end(@RequestParam Long id) {
        productSuggestService.end(id);
        return ResponseFactory.success();
    }
}
