package com.hengtiansoft.operation.productSuggest.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ProductSuggestDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestSortDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.vo.ProductSuggestVO;


public interface ProductSuggestService {

    PageVO<ProductSuggestVO> getList(ProductSuggestListDTO dto);

    void save(ProductSuggestDTO dto);

    ProductSuggestVO get(Long id);

    void delete(Long id);

    void sort(ProductSuggestSortDTO dto);

    void end(Long id);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);
}
