package com.hengtiansoft.operation.productSuggest.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSaveDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSortDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemListVO;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemSelectedVO;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-19 15:00
 **/
public interface ProductSuggestItemService {
    PageVO<ProductSuggestItemListVO> productSuggestItemList(ProductSuggestItemListDTO dto);

    String productSuggestItemSave(ProductSuggestItemSaveDTO saveDTO);

    void sortProductSuggestItem(ProductSuggestItemSortDTO sortDTO);

    void deleteProductSuggestItem(Long id);

    List<ProductSuggestItemSelectedVO> productSuggestItemSelected(Integer type, Long suggestId);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);
}
