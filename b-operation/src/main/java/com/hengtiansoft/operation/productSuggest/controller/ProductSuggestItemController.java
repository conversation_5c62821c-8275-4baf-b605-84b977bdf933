package com.hengtiansoft.operation.productSuggest.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSaveDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSortDTO;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemListVO;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemSelectedVO;
import com.hengtiansoft.item.entity.vo.ProductSuggestVO;
import com.hengtiansoft.operation.productSuggest.service.ProductSuggestItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-19 14:59
 **/

@RestController
@Api(tags = "商品推荐位明细B端")
@RequestMapping("/productSuggest/item")
public class ProductSuggestItemController {

    @Autowired
    private ProductSuggestItemService productSuggestItemService;

    @ApiOperation("商品推荐位明细列表")
    @PostMapping("/list")
    public Response<PageVO<ProductSuggestItemListVO>> productSuggestItemList(@Validated @RequestBody ProductSuggestItemListDTO param) {
        PageVO<ProductSuggestItemListVO> listPageVO = productSuggestItemService.productSuggestItemList(param);
        return ResponseFactory.success(listPageVO);
    }

    @ApiOperation("商品推荐位明细添加商品")
    @PostMapping("/save")
    public Response<String> productSuggestItemSave(@Validated @RequestBody ProductSuggestItemSaveDTO saveDTO) {
        String tips = productSuggestItemService.productSuggestItemSave(saveDTO);
        return ResponseFactory.success(tips);
    }

    @ApiOperation("已选择推荐位商品")
    @GetMapping("/selected")
    public Response<List<ProductSuggestItemSelectedVO>> productSuggestItemSelected(@RequestParam("type") Integer type, @RequestParam("productSuggestId") Long suggestId) {
        List<ProductSuggestItemSelectedVO> itemSelectedVOS = productSuggestItemService.productSuggestItemSelected(type, suggestId);
        return ResponseFactory.success(itemSelectedVOS);
    }

    @ApiOperation("排序")
    @PostMapping("/sort")
    public Response<Void> sortProductSuggestItem(@Validated @RequestBody ProductSuggestItemSortDTO sortDTO) {
        productSuggestItemService.sortProductSuggestItem(sortDTO);
        return ResponseFactory.success();
    }

    @ApiOperation("删除")
    @GetMapping("delete")
    public Response<Void> deleteProductSuggestItem(@RequestParam("id") Long id) {
        productSuggestItemService.deleteProductSuggestItem(id);
        return ResponseFactory.success();
    }








}
