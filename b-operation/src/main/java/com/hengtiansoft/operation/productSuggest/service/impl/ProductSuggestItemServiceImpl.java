package com.hengtiansoft.operation.productSuggest.service.impl;
import java.io.ByteArrayOutputStream;
import java.util.Date;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.item.dao.CategoryDao;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.po.ProductSuggestItem;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.entity.vo.*;
import com.hengtiansoft.item.enumeration.ItemCategoryLevelEnum;
import com.hengtiansoft.item.enumeration.ProductSuggestItemTypeEnum;
import com.hengtiansoft.item.manager.ProductSuggestItemManager;
import com.hengtiansoft.item.mapper.ProductMapper;
import com.hengtiansoft.item.mapper.SkuMapper;
import com.hengtiansoft.item.utils.CateNameContext;
import com.hengtiansoft.item.utils.ProductSuggestUtil;
import com.hengtiansoft.operation.productSuggest.service.ProductSuggestItemService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.util.TransactionUtils;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import com.nascent.ecrp.opensdk.domain.item.ItemSku;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-19 15:01
 **/
@Service
public class ProductSuggestItemServiceImpl implements ProductSuggestItemService {

    @Resource
    private ProductSuggestItemManager suggestItemManager;
    @Resource
    private SkuMapper skuMapper;
    @Resource
    private ProductMapper productMapper;

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private CateNameContext cateNameContext;

    @Resource
    private Executor msgPushTask;
    @Qualifier("reportTask")
    @Autowired
    private Executor reportTask;

    @Override
    public PageVO<ProductSuggestItemListVO> productSuggestItemList(ProductSuggestItemListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        if (CollectionUtils.isNotEmpty(dto.getCateIds())) {
            List<Long> cateIdsList = new ArrayList<>();
            if(Objects.isNull(dto.getCateLevel())){
                dto.getCateIds().forEach(cateId -> cateIdsList.addAll(categoryDao.selectAllSubIds(cateId)));
            }else{
                if(ItemCategoryLevelEnum.FIRSTCATE.getCode() ==  dto.getCateLevel()){
                    cateIdsList.addAll(categoryDao.selectByParId(dto.getCateIds()));
                }else{
                    cateIdsList.addAll(dto.getCateIds());
                }
            }
            if (CollectionUtils.isEmpty(cateIdsList)) {
                return PageUtils.emptyPage(dto);
            }
            dto.setCateIds(cateIdsList);
        }
        List<ProductSuggestItemResultVO> resultVOS = suggestItemManager.findPageByCondition(dto);
        if (CollectionUtils.isEmpty(resultVOS)) {
            return PageUtils.emptyPage(dto);
        }
        List<Long> productIds = resultVOS.stream().map(ProductSuggestItemResultVO::getProductId).distinct().collect(Collectors.toList());
        // skuInfo
        Condition condition = new Condition(Sku.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("productId", productIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<Sku> skus = skuMapper.selectByExample(condition);
        Map<Long, List<Sku>> skuMap = skus.stream().collect(Collectors.groupingBy(Sku::getProductId));
        // 类目信息
//        List<Long> packageCateIds = resultVOS.stream().filter(e -> e.getProductType().equals(ProductTypeEnum.PACKAGE.getCode())).map(ProductSuggestItemResultVO::getCateId).distinct().collect(Collectors.toList());
//        Condition cateCondition = new Condition(CardCategory.class);
//        Example.Criteria cateCriteria = cateCondition.createCriteria();
//        cateCriteria.andIn("id", packageCateIds);
//        cateCriteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
//        List<CardCategory> cardCategories = cardCategoryMapper.selectByExample(cateCondition);
//        Map<Long, String> packageCateMap = cardCategories.stream().collect(Collectors.toMap(CardCategory::getId, CardCategory::getCategoryName));
        resultVOS.forEach(e -> e.setCateName(cateNameContext.getName(e.getCateId())));
        return PageUtils.convert(resultVOS, data-> ProductSuggestUtil.convert2ItemVO(data, skuMap));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String productSuggestItemSave(ProductSuggestItemSaveDTO saveDTO) {
        String tips = "";
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        String userName = user.getUserName();
//        String userName = "test";
        Integer type = saveDTO.getType();
        Long suggestId = saveDTO.getProductSuggestId();
        List<Long> productIds = saveDTO.getProductIds();
        ProductSuggestItem suggestItem = suggestItemManager.selectExistByTypeAndSuggestId(type, suggestId);
        if (Objects.nonNull(suggestItem)) {
            if (CollectionUtils.isEmpty(productIds)) {
                suggestItemManager.deleteByTypeAndSuggestId(type, suggestId);
                return tips;
            }
            tips = validateRepeatOpposite(type, suggestId, productIds);
            if (CollectionUtils.isEmpty(productIds)) {
                return tips;
            }
            // 查询所有已选中的记录
            List<ProductSuggestItem> selectedSuggestItems = suggestItemManager.findSelectedItemsByTypeAndSuggestId(type, suggestId);
            List<Long> selectedProductIds = selectedSuggestItems.stream().map(ProductSuggestItem::getProductId).collect(Collectors.toList());
            // 对比传入的productIds 少增 多删
            // 删除 查询出ABC 传入BCD 增D 删A
            List<Long> addProductIds = productIds.stream().filter(e -> !selectedProductIds.contains(e)).collect(Collectors.toList());
            List<Long> deleteProductIds = selectedProductIds.stream().filter(e -> !productIds.contains(e)).collect(Collectors.toList());
//            ProductSuggestItem minSortSelectedItem = selectedSuggestItems.stream().sorted(Comparator.comparing(ProductSuggestItem::getSort)).collect(Collectors.toList()).get(0);
            doBatchInsert(addProductIds, 0, suggestId, type, userName);
            doUpdateSort(selectedSuggestItems, addProductIds.size());
            suggestItemManager.deleteByTypeAndSuggestIdAndProductIds(type, suggestId, deleteProductIds, userName);
        } else {
            tips = validateRepeatOpposite(type, suggestId, productIds);
            if (CollectionUtils.isEmpty(productIds)) {
                return tips;
            }
            doBatchInsert(productIds, 0, suggestId, type, userName);
        }
        TransactionUtils.afterCommitAsyncExecute(reportTask, () -> {
            suggestItemManager.autoRecommendProduct();
        });
        return tips;
    }

    private String validateRepeatOpposite(Integer type, Long productSuggestId, List<Long> productIds) {
        String tips = "";
        // 查询相反type的选中记录
        List<ProductSuggestItem> oppositeSuggestItems = suggestItemManager.findSelectedItemsByTypeAndSuggestId(type == 1 ? 2 : 1, productSuggestId);
        if (CollectionUtils.isNotEmpty(oppositeSuggestItems)) {
            List<Long> sameSelectedProductIds = oppositeSuggestItems.stream().filter(e -> productIds.contains(e.getProductId())).map(ProductSuggestItem::getProductId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sameSelectedProductIds)) {
                productIds.removeAll(sameSelectedProductIds);
                tips = "SPUID:" + sameSelectedProductIds+"已被" + (type == 1?"自动推荐":"指定推荐") + "选中，已过滤";
            }
        }
        return tips;
    }

    private void doUpdateSort(List<ProductSuggestItem> selectedSuggestItems, int offset) {
        if (offset == 0) return;
        selectedSuggestItems.forEach(e -> {
            e.setSort(e.getSort() + offset);
        });
        suggestItemManager.batchUpdate(selectedSuggestItems);
    }

    private void doBatchInsert(List<Long> productIds, Integer sort, Long suggestId, Integer type, String userName) {
        if (CollectionUtils.isEmpty(productIds)) return;
        List<ProductSuggestItem> addItems = Lists.newArrayList();
        for (Long addProductId : productIds) {
            sort++;
            ProductSuggestItem item = new ProductSuggestItem();
            item.setProductSuggestId(suggestId);
            item.setProductId(addProductId);
            item.setType(type);
            item.setSort(sort);
            item.setCreateTime(new Date());
            item.setUpdateTime(new Date());
            item.setDelflag(0);
            item.setOperator(userName);
            addItems.add(item);
        }
        suggestItemManager.batchInsert(addItems);
    }

    @Override
    public List<ProductSuggestItemSelectedVO> productSuggestItemSelected(Integer type, Long suggestId) {
        List<ProductSuggestItem> selectedSuggestItems = suggestItemManager.findSelectedItemsByTypeAndSuggestId(type, suggestId);
        if (CollectionUtils.isEmpty(selectedSuggestItems)) {
            return Collections.emptyList();
        }
        List<Long> productIds = selectedSuggestItems.stream().map(ProductSuggestItem::getProductId).distinct().collect(Collectors.toList());
        Condition condition = new Condition(Product.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", productIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<Product> products = productMapper.selectByExample(condition);
        Map<Long, String> productMap = products.stream().collect(Collectors.toMap(Product::getId, Product::getProductName));
        List<ProductSuggestItemSelectedVO> list = Lists.newArrayListWithCapacity(productIds.size());
        productIds.forEach(e -> {
            ProductSuggestItemSelectedVO suggestItemSelectedVO = new ProductSuggestItemSelectedVO();
            suggestItemSelectedVO.setProductId(e);
            suggestItemSelectedVO.setProductName(productMap.getOrDefault(e, ""));
            list.add(suggestItemSelectedVO);
        });
        return list;
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .head(ProductSuggestItemExcelVO.class)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(1, Lists.newArrayList(0,1,2,4,5,6,7,8,9, 12,13)))
                .build();
        ProductSuggestItemListDTO queryDTO = new ProductSuggestItemListDTO();
        queryDTO.setProductSuggestId(dto.getProductSuggestId());
        queryDTO.setProductId(dto.getProductId());
        queryDTO.setProductType(dto.getProductType());
        queryDTO.setProductName(dto.getProductName());
        queryDTO.setCateIds(dto.getCateIds());
        queryDTO.setCateLevel(dto.getCateLevel());
        queryDTO.setType(dto.getType());
        queryDTO.setSaleStatus(dto.getSaleStatus());

        if (dto.getType().equals(ProductSuggestItemTypeEnum.APPOINT_RECOMMEND.getCode())) {
            dto.setFileName(ProductSuggestItemTypeEnum.APPOINT_RECOMMEND.getDesc() + "_" + dto.getFileName());
        } else {
            dto.setFileName(ProductSuggestItemTypeEnum.AUTO_RECOMMEND.getDesc() + "_" + dto.getFileName());

        }
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<ProductSuggestItemListVO> listVOPageVO = this.productSuggestItemList(queryDTO);
            Pagination pagination = listVOPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<ProductSuggestItemExcelVO> excelVOS = ProductSuggestUtil.convert2ItemExcelVO(listVOPageVO.getList());
            excelWriter.write(excelVOS, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public void sortProductSuggestItem(ProductSuggestItemSortDTO sortDTO) {
        ProductSuggestItem updateItem = suggestItemManager.selectById(sortDTO.getId());
        if (Objects.isNull(updateItem)) {
            throw new BusinessException("该明细已被删除");
        }
        Integer newSort = sortDTO.getSort();
        Integer sort = updateItem.getSort();
        if (sort.equals(newSort)) return;
        List<ProductSuggestItem> greaterThanCurrentSortItems = suggestItemManager.selectGreaterThanSortList(updateItem.getType(), updateItem.getProductSuggestId(), newSort);
        suggestItemManager.updateSortById(sortDTO);
        greaterThanCurrentSortItems = greaterThanCurrentSortItems.stream().filter(e -> !e.getId().equals(updateItem.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(greaterThanCurrentSortItems)) {
            greaterThanCurrentSortItems.forEach(e -> e.setSort(e.getSort() + 1));
            suggestItemManager.batchUpdate(greaterThanCurrentSortItems);
        }
    }

    @Override
    public void deleteProductSuggestItem(Long id) {
        suggestItemManager.deleteById(id);
    }

}
