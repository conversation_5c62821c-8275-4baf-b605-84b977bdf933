package com.hengtiansoft.operation;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.dtflys.forest.springboot.annotation.ForestScan;
import tk.mybatis.spring.annotation.MapperScan;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

import com.hengtiansoft.security.announces.EnableSecuritySupport;

import java.util.TimeZone;


/**
 * 启动类
 */
@SpringBootApplication(scanBasePackages = "com.hengtiansoft",exclude = DruidDataSourceAutoConfigure.class)
@EnableAsync
@EnableCaching
@MapperScan("com.hengtiansoft.**.mapper")
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableSecuritySupport
@ForestScan(basePackages = "com.hengtiansoft")
public class Application {
    public static void main(String[] args) {
        final TimeZone timeZone = TimeZone.getTimeZone("Asia/Shanghai");
        TimeZone.setDefault(timeZone);
        SpringApplication.run(Application.class, args);
    }
}
