package com.hengtiansoft.operation.pintuan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-05-30 09:53
 **/
@Data
public class PtStatisticBoardVO {

    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 所有订阅消息提醒次数
     */
    @ApiModelProperty("所有订阅消息提醒次数")
    private Integer totalSubscribCnt;

    /**
     * 所有订阅消息提醒还未触达次数
     */
    @ApiModelProperty("所有订阅消息提醒还未触达次数")
    private Integer totalUnreachCnt;

    /**
     * 拼团成功实际人数，同一人参与多次活动算1不剔除退款
     */
    @ApiModelProperty("拼团成功实际人数，同一人参与多次活动算1不剔除退款")
    private Integer totalActualFormCnt;

    /**
     * 拼团成功实际支付金额不剔除退款
     */
    @ApiModelProperty("拼团成功实际支付金额不剔除退款")
    private BigDecimal totalActualFormAmount;

    /**
     * 拼团实际支付人数，同一人参与多次活动算1不剔除退款
     */
    @ApiModelProperty("拼团实际支付人数，同一人参与多次活动算1不剔除退款")
    private Integer totalActualJoinCnt;

    /**
     * 拼团邀请链接注册的新用户数
     */
    @ApiModelProperty("拼团邀请链接注册的新用户数")
    private Integer totalNewUserCnt;
}
