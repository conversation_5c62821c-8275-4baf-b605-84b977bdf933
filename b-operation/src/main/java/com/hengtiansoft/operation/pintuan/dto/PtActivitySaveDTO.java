package com.hengtiansoft.operation.pintuan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 17:44
 **/
@Data
public class PtActivitySaveDTO {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 活动名称
     */
    @NotEmpty(message = "活动名称不能为空")
    @ApiModelProperty("活动名称")
    private String name;

    /**
     * 活动开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty("活动结束时间")
    private Date endTime;

    /**
     * 人群限制1-不限制 2-新用户
     */
    @NotNull(message = "活动人群限制不能为空")
    @ApiModelProperty("人群限制1-不限制 2-新用户")
    @Min(value = 1,message = "非法值")
    private Integer peopleLimit;


    @ApiModelProperty("商品信息")
    @NotNull(message = "商品信息不能为空")
    private ProductInfo productInfo;


    /**
     * 有效时长，单位：分
     */
    @NotNull(message = "有效时长不能为空")
    @Min(value = 1, message = "有效时长不能小于1")
    @ApiModelProperty("有效时长，单位：分")
    private Integer validityPeriod;

    /**
     * 成团人数
     */
    @NotNull(message = "成团人数不能为空")
    @Min(value = 2,message = "成团人数不能小于2")
    @Max(value = 99, message = "成团人数不能大于99")
    @ApiModelProperty("成团人数")
    private Integer requiredNum;

    /**
     * 库存
     */
    @NotNull(message = "库存不能为空")
    @Min(value = 1, message = "库存不能小于1")
    @ApiModelProperty("库存")
    private Long stock;

    /**
     * 限购数量
     */
    @ApiModelProperty("限购数量")
    private Integer limitNum;

    /**
     * 虚拟成团0否1是
     */
    @NotNull(message = "虚拟成团不能为空")
    @Min(value = 0, message = "非法值，不能小于0")
    @Max(value = 1, message = "非法值，不能大于1")
    @ApiModelProperty("虚拟成团0否1是")
    private Integer enableMocker;

    /**
     * 立即预告 1-创建成功立即预告 2-活动开始前n小时
     */
    @NotNull(message = "预告不能为空")
    @Min(value = 1, message = "非法值，不能小于1")
    @Max(value = 2, message = "非法值，不能大于2")
    @ApiModelProperty("立即预告 1-创建成功立即预告 2-活动开始前n小时")
    private Integer advanceNotice;

    /**
     * 提前n小时
     */
    @ApiModelProperty("提前n小时")
    private Integer advanceHour;

    /**
     * 0 不同享 1 同享积分抵现 2 同享优惠券,多个逗号分割
     */
    @ApiModelProperty("0 不同享 1 同享积分抵现 2 同享优惠券,多个逗号分割")
    private String shareDiscount;

    /**
     * 客户打标
     */
    @ApiModelProperty("客户打标")
    private List<String> customerMarkList;

    @Data
    public static class ProductInfo {

        /**
         * skuId
         */
        @NotNull(message = "skuId不能为空")
        @ApiModelProperty("skuId")
        private Long skuId;

        /**
         * 商品id
         */
        @NotNull(message = "商品id不能为空")
        @ApiModelProperty("商品id")
        private Long productId;

        @ApiModelProperty("拼团价")
        @NotNull(message = "拼团价不能为空")
        private BigDecimal ptPrice;
    }
}
