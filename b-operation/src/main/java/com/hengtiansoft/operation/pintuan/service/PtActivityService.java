package com.hengtiansoft.operation.pintuan.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivityDelDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivitySaveDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivityStartOrEndDTO;
import com.hengtiansoft.operation.pintuan.vo.*;
import com.hengtiansoft.privilege.entity.dto.PtActivityListDTO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 18:36
 **/
public interface PtActivityService {
    void save(PtActivitySaveDTO saveDTO);

    PtActivityVO get(Long id);

    PageVO<PtActivityListVO> pageList(PtActivityListDTO listDTO);

    void startOrEnd(PtActivityStartOrEndDTO startOrEndDTO);

    void delete(PtActivityDelDTO delDTO);

    List<PtActivityHomeVO> home();

    PromoVO promotion(Long id);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    PtActivityExtraStatisticVO getExtraStatistic(Long ptActivityId);

    PtStatisticBoardVO statisticBoard();
}
