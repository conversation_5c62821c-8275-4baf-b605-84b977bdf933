package com.hengtiansoft.operation.pintuan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-05-30 09:38
 **/
@Data
public class PtActivityExtraStatisticVO {

    /**
     * 拼团活动id
     */
    @ApiModelProperty("拼团活动id")
    private Long ptActivityId;

    /**
     * 实际成团金额，不剔除退款
     */
    @ApiModelProperty("实际成团金额，不剔除退款")
    private BigDecimal formedAmount;

    /**
     * 实际拼团优惠金额（不区分是否成团）
     */
    @ApiModelProperty("实际拼团优惠金额（不区分是否成团）")
    private BigDecimal ptDiscountAmount;

    /**
     * 优惠总金额 / 订单实付金额（不区分是否成团）
     */
    @ApiModelProperty("优惠总金额 / 订单实付金额（不区分是否成团）")
    private BigDecimal costBenefitRatio;

    /**
     * 实际成团老客数，不剔除退款
     */
    @ApiModelProperty("实际成团老客数，不剔除退款")
    private Integer formedOldUserCnt;

    /**
     * 实际成团新客数，不剔除退款
     */
    @ApiModelProperty("实际成团新客数，不剔除退款")
    private Integer formedNewUserCnt;

    /**
     * 客单价
     */
    @ApiModelProperty("客单价")
    private BigDecimal perCustomerTransaction;
}
