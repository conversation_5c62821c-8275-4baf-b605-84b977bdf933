package com.hengtiansoft.operation.pintuan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 17:55
 **/
@Data
public class PtActivityVO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String name;

    /**
     * 活动开始时间
     */
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty("活动结束时间")
    private Date endTime;

    /**
     * 人群限制1-不限制 2-新用户
     */
    @ApiModelProperty("人群限制1-不限制 2-新用户")
    private Integer peopleLimit;

    @ApiModelProperty("商品信息")
    private ProductInfo productInfo;

    /**
     * 有效时长，单位：分
     */
    @ApiModelProperty("有效时长，单位：分")
    private Integer validityPeriod;

    /**
     * 成团人数
     */
    @ApiModelProperty("成团人数")
    private Integer requiredNum;

    /**
     * 库存
     */
    @ApiModelProperty("库存")
    private Long stock;

    /**
     * 限购数量
     */
    @ApiModelProperty("限购数量")
    private Integer limitNum;

    /**
     * 虚拟成团0否1是
     */
    @NotNull(message = "虚拟成团不能为空")
    @ApiModelProperty("虚拟成团0否1是")
    private Integer enableMocker;

    /**
     * 立即预告 1-创建成功立即预告 2-活动开始前n小时
     */
    @NotNull(message = "预告不能为空")
    @ApiModelProperty("立即预告 1-创建成功立即预告 2-活动开始前n小时")
    private Integer advanceNotice;

    /**
     * 提前n小时
     */
    @ApiModelProperty("提前n小时")
    private Integer advanceHour;

    /**
     * 0 不同享 1 同享积分抵现 2 同享优惠券,3同享满减满送，多个逗号分割
     */
    @ApiModelProperty("0 不同享 1 同享积分抵现 2 同享优惠券,3同享满减满送，多个逗号分割")
    private String shareDiscount;

    /**
     * 客户打标
     */
    @ApiModelProperty("客户打标")
    private List<String> customerMarkList;


    @Data
    public static class ProductInfo {

        /**
         * skuId
         */
        @ApiModelProperty("skuId")
        private Long skuId;

        @ApiModelProperty("sku名称")
        private String skuName;

        /**
         * 商品id
         */
        @ApiModelProperty("商品id")
        private Long productId;

        @ApiModelProperty("商品名称")
        private String productName;

        @ApiModelProperty("商品图片")
        private String picUrl;

        @ApiModelProperty(value = "商品规格")
        private String specValueList;

        @ApiModelProperty("标价")
        private BigDecimal listPrice;

        @ApiModelProperty("售价")
        private BigDecimal salePrice;

        @ApiModelProperty("拼团价")
        private BigDecimal ptPrice;

        @ApiModelProperty("库存")
        private Long stock;
    }
}
