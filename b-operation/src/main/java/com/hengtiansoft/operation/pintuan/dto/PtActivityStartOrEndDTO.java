package com.hengtiansoft.operation.pintuan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 18:27
 **/
@Data
public class PtActivityStartOrEndDTO {

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("操作类型，1-开始 2-结束")
    @NotNull(message = "操作类型不能为空")
    @Min(value = 1, message = "非法值，操作类型不能小于1")
    @Max(value = 2, message = "非法值，操作类型不能大于2")
    private Integer operateType;
}
