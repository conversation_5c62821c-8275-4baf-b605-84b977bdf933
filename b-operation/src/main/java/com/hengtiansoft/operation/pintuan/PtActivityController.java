package com.hengtiansoft.operation.pintuan;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.pintuan.dto.PtActivityDelDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivitySaveDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivityStartOrEndDTO;
import com.hengtiansoft.operation.pintuan.service.PtActivityService;
import com.hengtiansoft.operation.pintuan.vo.*;
import com.hengtiansoft.privilege.entity.dto.PtActivityListDTO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 17:35
 **/
@RestController
@Api(tags = "拼团活动管理")
@RequestMapping("/pt/activity")
public class PtActivityController {

    @Autowired
    private PtActivityService ptActivityService;

    @ApiOperation("保存拼团活动")
    @PostMapping("/save")
    public Response<Void> save(@RequestBody @Validated PtActivitySaveDTO saveDTO) {
        ptActivityService.save(saveDTO);
        return ResponseFactory.success();
    }

    @ApiOperation("拼团活动详情")
    @GetMapping("/get")
    public Response<PtActivityVO> get(@RequestParam("id") Long id) {
        PtActivityVO ptActivityVO = ptActivityService.get(id);
        return ResponseFactory.success(ptActivityVO);
    }

    @ApiOperation("拼团活动列表")
    @PostMapping("/page/list")
    public Response<PageVO<PtActivityListVO>> pageList(@RequestBody PtActivityListDTO listDTO) {
        PageVO<PtActivityListVO> pageVO = ptActivityService.pageList(listDTO);
        return ResponseFactory.success(pageVO);
    }

    @ApiOperation("开始/结束活动")
    @PostMapping("/startOrEnd")
    public Response<Void> startOrEnd(@RequestBody @Validated PtActivityStartOrEndDTO startOrEndDTO) {
        ptActivityService.startOrEnd(startOrEndDTO);
        return ResponseFactory.success();
    }

    @ApiOperation("删除活动")
    @PostMapping("/delete")
    public Response<Void> delete(@RequestBody @Validated PtActivityDelDTO delDTO) {
        ptActivityService.delete(delDTO);
        return ResponseFactory.success();
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        PromoVO promotion = ptActivityService.promotion(id);
        return ResponseFactory.success(promotion);
    }


    @ApiOperation("拼团首页")
    @PostMapping("/home/<USER>")
    public Response<List<PtActivityHomeVO>> home() {
        return ResponseFactory.success(ptActivityService.home());
    }

    @ApiOperation("拼团活动数据查看")
    @GetMapping("/extra/statistic")
    public Response<PtActivityExtraStatisticVO> extraStatistic(@RequestParam("ptActivityId") Long ptActivityId) {
        PtActivityExtraStatisticVO statisticVO = ptActivityService.getExtraStatistic(ptActivityId);
        return ResponseFactory.success(statisticVO);
    }

    @ApiOperation("拼团数据看板")
    @GetMapping("/statistic/board")
    public Response<PtStatisticBoardVO> statisticBoard() {
        PtStatisticBoardVO boardVO = ptActivityService.statisticBoard();
        return ResponseFactory.success(boardVO);
    }

}
