package com.hengtiansoft.operation.pintuan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-29 11:13
 **/
@Data
public class PtDataStatisticsDTO {

    @ApiModelProperty("拼团活动id")
    private Long ptActivityId;

    @ApiModelProperty("参与人数")
    private Integer joinedNum;

    @ApiModelProperty("成团总人数")
    private Integer formedNum;

    @ApiModelProperty("成团总金额")
    private BigDecimal formedAmount;

    @ApiModelProperty("拼团商品名称")
    private String productName;
}
