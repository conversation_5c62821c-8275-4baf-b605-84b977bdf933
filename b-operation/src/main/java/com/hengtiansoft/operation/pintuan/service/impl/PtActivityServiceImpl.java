package com.hengtiansoft.operation.pintuan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.*;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.content.dao.BaseDataDao;
import com.hengtiansoft.content.entity.po.BaseData;
import com.hengtiansoft.content.enums.BaseDataTypeEnum;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.*;
import com.hengtiansoft.item.entity.vo.PtActivityExcelVO;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.item.manager.DiscountActivityManager;
import com.hengtiansoft.item.manager.DiscountActivityRangeManager;
import com.hengtiansoft.operation.pintuan.dto.PtActivityDelDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivitySaveDTO;
import com.hengtiansoft.operation.pintuan.dto.PtActivityStartOrEndDTO;
import com.hengtiansoft.operation.pintuan.dto.PtDataStatisticsDTO;
import com.hengtiansoft.operation.pintuan.service.PtActivityService;
import com.hengtiansoft.operation.pintuan.vo.*;
import com.hengtiansoft.operation.pintuan.vo.PtActivityVO.ProductInfo;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.dao.PtStatisticDao;
import com.hengtiansoft.order.entity.dto.PtOrderListDTO;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.order.manager.PtActivityExtraStatisticManager;
import com.hengtiansoft.order.manager.PtOrderManager;
import com.hengtiansoft.order.manager.PtSubOrderManager;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityDao;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityProductDao;
import com.hengtiansoft.privilege.entity.dto.MonitorPageDTO;
import com.hengtiansoft.privilege.entity.dto.PtActivityListDTO;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import com.hengtiansoft.privilege.enums.FreeTrialSubActivityTypeEnum;
import com.hengtiansoft.privilege.enums.MoTypeEnum;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 18:38
 **/
@Slf4j
@Service
public class PtActivityServiceImpl implements PtActivityService {

    @Resource
    private PtActivityManager ptActivityManager;
    @Resource
    private PtGoodsManager ptGoodsManager;
    @Resource
    private BaseDataDao baseDataDao;
    @Resource
    private ProductManager productManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private MonitorPageManager monitorPageManager;
    @Resource
    private FullReduceProductRangeManager fullReduceProductRangeManager;
    @Resource
    private FullReduceManager fullReduceManager;
    @Resource
    private DiscountActivityRangeManager discountActivityRangeManager;
    @Resource
    private DiscountActivityManager discountActivityManager;
    @Resource
    private PtSubOrderManager ptSubOrderManager;
    @Resource
    private PtOrderManager ptOrderManager;
    @Resource
    private OrderManager orderManager;
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private PeopleLabelManager peopleLabelManager;
    @Resource
    private StockManager stockManager;
    @Resource
    private PtActivityExtraStatisticManager ptActivityExtraStatisticManager;
    @Resource
    private PtStatisticDao ptStatisticDao;
    @Resource
    private FreeTrialSubActivityProductDao freeTrialSubActivityProductDao;
    @Resource
    private FreeTrialSubActivityDao freeTrialSubActivityDao;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(PtActivitySaveDTO saveDTO) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        String userName = user.getUserName();
        Long id = saveDTO.getId();
        validateSelf(saveDTO);
        validateActivityStock(saveDTO);
        validateMarketingProduct(saveDTO.getProductInfo());
        PtActivity activity = assemblePtActivity(saveDTO, userName);
        String oldCustomerMark = "";
        if (Objects.isNull(id)) {
            ptActivityManager.insert(activity);
            PtActivityExtraStatistic extraStatistic = new PtActivityExtraStatistic();
            extraStatistic.setPtActivityId(activity.getId());
            extraStatistic.setFormedAmount(new BigDecimal("0"));
            extraStatistic.setPtDiscountAmount(new BigDecimal("0"));
            extraStatistic.setCostBenefitRatio(new BigDecimal("0"));
            extraStatistic.setFormedOldUserCnt(0);
            extraStatistic.setFormedNewUserCnt(0);
            extraStatistic.setPerCustomerTransaction(new BigDecimal("0"));
            extraStatistic.setCreateTime(new Date());
            extraStatistic.setUpdateTime(new Date());
            extraStatistic.setOperator(userName);
            extraStatistic.setDelflag(0);
            ptActivityExtraStatisticManager.insert(extraStatistic);
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(saveDTO.getName());
            monitorPage.setTargetId(activity.getId());
            monitorPage.setType(MoTypeEnum.PT_ACTIVITY.getCode());
            monitorPage.setTargetName(saveDTO.getName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
        } else {
            PtActivity ptActivity = ptActivityManager.getById(id);
            oldCustomerMark = ptActivity.getCustomerMark();
            Asserts.notNull(ptActivity, "该拼团活动不存在");
            validateUnModifyItem(saveDTO);
            ptGoodsManager.deleteByPtActivityId(id, userName);
            ptActivityManager.manualUpdateById(activity);
        }
        PtGoods ptGoods = assemblePtGoodsInfo(activity.getId(), saveDTO.getProductInfo(), userName);
        ptGoodsManager.insert(ptGoods);
        handlePeopleLabel(activity.getId(), saveDTO.getCustomerMarkList(), oldCustomerMark);
    }


    @Override
    public PtActivityVO get(Long id) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PtActivity ptActivity = ptActivityManager.getById(id);
        Asserts.notNull(ptActivity, "该拼团活动不存在");
        PtGoods ptGoods = ptGoodsManager.getByActivityId(id);
        Asserts.notNull(ptGoods, "拼团活动商品不存在");
        Sku sku = skuManager.selectById(ptGoods.getSkuId());
        Asserts.notNull(ptGoods, "sku信息不存在");
        return assemblePtActivityVO(ptActivity, ptGoods, sku);
    }

    @Override
    public PageVO<PtActivityListVO> pageList(PtActivityListDTO listDTO) {
        PageHelper.startPage(listDTO.getPageNum(), listDTO.getPageSize());
        List<PtActivity> ptActivityList = ptActivityManager.findByCondition(listDTO);
        if (CollectionUtils.isEmpty(ptActivityList)) {
            return PageUtils.emptyPage();
        }
        Map<Long, PtDataStatisticsDTO> statisticsDTOMap =  calculatePtStatistics(ptActivityList);
        return PageUtils.convert(ptActivityList, e -> assemblePtActivityListVO(e, statisticsDTOMap));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void startOrEnd(PtActivityStartOrEndDTO startOrEndDTO) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        Long id = startOrEndDTO.getId();
        Integer operateType = startOrEndDTO.getOperateType();
        PtActivity ptActivity = ptActivityManager.getById(id);
        Asserts.notNull(ptActivity, "该拼团活动不存在");
        PtActivity updatePtActivity = new PtActivity();
        updatePtActivity.setId(ptActivity.getId());
        updatePtActivity.setOperator(user.getUserName());
        Date now = new Date();
        Date startTime = ptActivity.getStartTime();
        Date endTime = ptActivity.getEndTime();
        PtGoods ptGoods = ptGoodsManager.getByActivityId(id);
        Asserts.notNull(ptGoods, "拼团活动商品不存在");
        if (operateType == 1) {
            Long productId = ptGoods.getProductId();
            // 查询该商品已存在的进行中的活动
            PtActivity onGoingActivity = ptActivityManager.getOnGoingActivityByProductId(productId);
            if (Objects.nonNull(onGoingActivity)) {
                throw new BusinessException("本活动关联商品存在进行中的活动，无法开始");
            }
            // 查询时间线重叠的数据
            List<PtActivity> timelineCoincideActivities = ptActivityManager.getTimelineCoincideByProductId(now, endTime, productId);
            List<PtActivity> existTimelineCoincideActivities = timelineCoincideActivities.stream().filter(e -> !e.getId().equals(ptActivity.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existTimelineCoincideActivities)) {
                throw new BusinessException("该活动开始后会与活动:{}"+ existTimelineCoincideActivities.get(0).getName() + "时间重合，请修改时间");
            }
            if (startTime.before(now)) {
                throw new BusinessException("活动已经开始");
            }
            if (endTime.before(now)) {
                throw new BusinessException("结束的活动无法开始");
            }
            updatePtActivity.setStartTime(now);
        } else if (operateType == 2) {
            if (endTime.before(now)) {
                throw new BusinessException("活动已经结束");
            }
            updatePtActivity.setEndTime(now);
        }
        updatePtActivity.setUpdateTime(now);
        ptActivityManager.update(updatePtActivity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PtActivityDelDTO delDTO) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        Long id = delDTO.getId();
        PtActivity ptActivity = ptActivityManager.getById(id);
        Asserts.notNull(ptActivity, "该拼团活动不存在");
        Integer status = ptActivity.getStatus();
        if (Objects.equals(status, CommonActivityStatusEnum.IN_PROGRESS.getCode()) || Objects.equals(status, CommonActivityStatusEnum.END.getCode())) {
            throw new BusinessException("进行中和已结束的活动无法删除");
        }
        String userName = user.getUserName();
        PtActivity updatePtActivity = new PtActivity();
        updatePtActivity.setId(ptActivity.getId());
        updatePtActivity.setDelflag(1);
        updatePtActivity.setUpdateTime(new Date());
        updatePtActivity.setOperator(userName);
        ptActivityManager.update(updatePtActivity);
        ptGoodsManager.deleteByPtActivityId(id, userName);
    }

    @Override
    public List<PtActivityHomeVO> home() {
        BaseData baseData = baseDataDao.selectByType(BaseDataTypeEnum.PT_HOME.getCode());
        if (baseData != null) {
            String value = baseData.getValue();
            PtActivityHomeVO homeVO = JSON.parseObject(value, PtActivityHomeVO.class);
            return Arrays.asList(homeVO);
        }
        return null;
    }

    @Override
    public PromoVO promotion(Long id) {
        PtActivity freeTrial = ptActivityManager.getById(id);
        Assert.notNull(freeTrial, "该拼团活动不存在");
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.PT_ACTIVITY.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        PromoVO promoVO = new PromoVO();
        promoVO.setId(id);

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {

        WriteSheet writeSheet = EasyExcel.writerSheet().head(PtActivityExcelVO.class).build();
        PtActivityListDTO queryDTO = new PtActivityListDTO();
        queryDTO.setStatus(dto.getStatus());
        queryDTO.setName(dto.getName());
        int pageNum = 1;
        int pageSize = 500;
        int pages = 1;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<PtActivityListVO> pageVO = this.pageList(queryDTO);
            Pagination pagination = pageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<PtActivityExcelVO> ptActivityExcelVOS = BeanUtil.copyToList(pageVO.getList(), PtActivityExcelVO.class);
            excelWriter.write(ptActivityExcelVOS, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public PtActivityExtraStatisticVO getExtraStatistic(Long ptActivityId) {
        PtActivityExtraStatistic extraStatistic = ptActivityExtraStatisticManager.getByActivityId(ptActivityId);
        if (Objects.isNull(extraStatistic)) return new PtActivityExtraStatisticVO();
        return BeanUtil.copyProperties(extraStatistic, PtActivityExtraStatisticVO.class);
    }

    @Override
    public PtStatisticBoardVO statisticBoard() {
        PtStatistic ptStatistic = ptStatisticDao.getLatestOne();
        if (Objects.isNull(ptStatistic)) return new PtStatisticBoardVO();
        return BeanUtil.copyProperties(ptStatistic, PtStatisticBoardVO.class);
    }

    private void handlePeopleLabel(Long targetId, List<String> customerMarkList, String oldCustomerMark) {
        //更新手动标签信息
        if(CollectionUtils.isNotEmpty(customerMarkList)) {
            List<Long> labelIds = StreamUtils.convert(customerMarkList, Long::valueOf);
            peopleLabelManager.updateActivity(labelIds, targetId, LaActivityTypeEnum.PT_ACTIVITY.getCode());
        }
        if(StringUtils.isNotEmpty(oldCustomerMark)) {
            List<Long> labelIds = Arrays.stream(oldCustomerMark.split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerMarkList)) {
                labelIds.removeAll(StreamUtils.convert(customerMarkList, Long::valueOf));
            }
            if (CollectionUtils.isNotEmpty(labelIds)) {
                peopleLabelManager.updateActivity(labelIds, 0L, LaActivityTypeEnum.DEFAULT.getCode());
            }
        }
    }


    private Map<Long, PtDataStatisticsDTO> calculatePtStatistics(List<PtActivity> ptActivityList) {
        List<Long> ptActivityIds = ptActivityList.stream().map(PtActivity::getId).collect(Collectors.toList());
        List<PtGoods> ptGoodsList = ptGoodsManager.getByActivityIds(ptActivityIds);
        Map<Long, String> ptGoodsNameMap = StreamUtils.toMap(ptGoodsList, PtGoods::getPtActivityId, PtGoods::getProductName);

        List<PtSubOrder> ptSubOrders = ptSubOrderManager.getByPtActivityIds(ptActivityIds);
        // 参与人数  = 该活动下除去机器人并且状态为已支付的订单根据userid去重
        Map<Long, List<PtSubOrder>> ptActivityIdSubOrderGroup = ptSubOrders.stream().collect(Collectors.groupingBy(PtSubOrder::getPtActivityId));
        // 成团总人数 = 拼团成功的订单下 已支付成功的子订单数量去除机器人
        PtOrderListDTO ptOrderListDTO = new PtOrderListDTO();
        ptOrderListDTO.setPtActivityIds(ptActivityIds);
        ptOrderListDTO.setPtOrderStatusList(Lists.newArrayList(PtOrderStatusEnum.FORMED.getCode()));
        List<PtOrder> formedPtOrders = ptOrderManager.findByCondition(ptOrderListDTO);
        List<String> ptOrderNos = formedPtOrders.stream().map(PtOrder::getPtOrderNo).collect(Collectors.toList());
        // 拼团成功子订单
        List<PtSubOrder> formedSubOrders = ptSubOrderManager.getByPtOrderNos(ptOrderNos);
        // 剔除机器人和未支付还有已取消订单
        List<PtSubOrder> noRobotAndPaidSubOrders = formedSubOrders.stream().filter(e -> e.getHasCaptain() != 2 && e.getPtSubOrderStatus().equals(PtSubOrderStatusEnum.PAID.getCode())).collect(Collectors.toList());
        Map<Long, List<PtSubOrder>> paidSubOrderGroup = noRobotAndPaidSubOrders.stream().collect(Collectors.groupingBy(PtSubOrder::getPtActivityId));
        // 成团总金额 = 拼团已经成功的订单下 已支付成功的子订单金额（取orderInfo的realAmount）去除机器人
        List<String> orderNos = noRobotAndPaidSubOrders.stream().map(PtSubOrder::getOrderNo).collect(Collectors.toList());
        List<OrderInfo> orderInfos = orderManager.selectByOrderNoList(orderNos);
        Map<String, BigDecimal> realAmountMap = orderInfos.stream().collect(Collectors.toMap(OrderInfo::getOrderNo, OrderInfo::getRealAmount));
        Map<Long, PtDataStatisticsDTO> resultMap = Maps.newHashMap();
        ptActivityIds.forEach(activityId -> {
            PtDataStatisticsDTO statisticsDTO = new PtDataStatisticsDTO();
            statisticsDTO.setPtActivityId(activityId);
            List<PtSubOrder> joinedNumSubOrderList = ptActivityIdSubOrderGroup.getOrDefault(activityId, Collections.emptyList());
            long joinedNum = joinedNumSubOrderList.stream()
                    .filter(e -> e.getHasCaptain() != 2 &&
                            (e.getPtSubOrderStatus().equals(PtSubOrderStatusEnum.PAID.getCode()) || e.getPtSubOrderStatus().equals(PtSubOrderStatusEnum.CLOSED.getCode())))
                    .map(PtSubOrder::getUserId).distinct().count();
            statisticsDTO.setJoinedNum((int) joinedNum);

            List<PtSubOrder> paidSubOrderList = paidSubOrderGroup.getOrDefault(activityId, Collections.emptyList());
            long formedNum = paidSubOrderList.stream().map(PtSubOrder::getUserId).distinct().count();
            statisticsDTO.setFormedNum((int) formedNum);

            BigDecimal formedAmount = BigDecimal.ZERO;
            for (PtSubOrder subOrder : paidSubOrderList) {
                formedAmount = formedAmount.add(realAmountMap.getOrDefault(subOrder.getOrderNo(), new BigDecimal(0)));
            }
            statisticsDTO.setFormedAmount(formedAmount);
            statisticsDTO.setProductName(ptGoodsNameMap.getOrDefault(activityId, ""));
            resultMap.put(activityId, statisticsDTO);
        });

        return resultMap;
    }


    private void validateUnModifyItem(PtActivitySaveDTO saveDTO) {
        Long id = saveDTO.getId();
        PtActivity ptActivity = ptActivityManager.getById(id);
        Date startTime = ptActivity.getStartTime();
        Date endTime = ptActivity.getEndTime();
        Date now = new Date();
        if (endTime.before(now)) {
            throw new BusinessException("已结束拼团活动不支持修改");
        }
        if (startTime.before(now)) {
            PtGoods dbPtGoods = ptGoodsManager.getByActivityId(id);
            Long productId = saveDTO.getProductInfo().getProductId();
            if (!dbPtGoods.getProductId().equals(productId)) {
                throw new BusinessException("进行中的活动不支持修改商品信息");
            }
            if (!saveDTO.getRequiredNum().equals(ptActivity.getRequiredNum())) {
                throw new BusinessException("进行中的活动成团人数不能变更");
            }
        }

    }

    private void validateSelf(PtActivitySaveDTO saveDTO) {
        Long productId = saveDTO.getProductInfo().getProductId();
        Date startTime = saveDTO.getStartTime();
        Date endTime = saveDTO.getEndTime();
        if (startTime.after(endTime)) {
            throw new BusinessException("开始时间不能大于结束时间");
        }
        PtActivity ptActivity = ptActivityManager.getByProductIdAndTime(productId, startTime, endTime);
        if ((Objects.isNull(saveDTO.getId()) && Objects.nonNull(ptActivity)) ||
                (Objects.nonNull(saveDTO.getId()) && Objects.nonNull(ptActivity) && !ptActivity.getId().equals(saveDTO.getId()))) {
            throw new BusinessException("该商品在开始结束时间内与拼团活动:" + ptActivity.getName() + "重合");
        }
    }


    private void validateMarketingProduct(PtActivitySaveDTO.ProductInfo productInfo) {
        Long productId = productInfo.getProductId();
        List<FullReduceProductRange> fullReduceProductRanges = fullReduceProductRangeManager.getFullReduceByProductId(productId);
        if (CollectionUtils.isNotEmpty(fullReduceProductRanges)) {
            List<Long> fullReduceIds = fullReduceProductRanges.stream().map(FullReduceProductRange::getFullReduceId).distinct().collect(Collectors.toList());
            List<FullReduce> fullReduces = fullReduceManager.findByIds(fullReduceIds);
            List<FullReduce> conflictFullReduces = fullReduces.stream().filter(e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode())
                    .contains(e.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(conflictFullReduces)) {
                List<String> conflictNames = conflictFullReduces.stream().map(FullReduce::getName).distinct().collect(Collectors.toList());
                throw new BusinessException("商品id" + productId + "在满减活动：" + conflictNames + " 中已存在");
            }
        }
        List<DiscountActivityRange> discountRanges = discountActivityRangeManager.findByProductId(productId);
        if (CollectionUtils.isNotEmpty(discountRanges)) {
            List<Long> discountActivityIds = discountRanges.stream().map(DiscountActivityRange::getDiscountActivityId).distinct().collect(Collectors.toList());
            List<DiscountActivity> discountActivities = discountActivityManager.getByIds(discountActivityIds);
            List<DiscountActivity> conflictDiscountActivities = discountActivities.stream()
                    .filter(e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()).contains(e.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(conflictDiscountActivities)) {
                List<String> conflictNames = conflictDiscountActivities.stream().map(DiscountActivity::getName).distinct().collect(Collectors.toList());
                throw new BusinessException("商品id" + productId + "在限时折扣活动：" + conflictNames + " 中已存在");
            }
        }
        //判断参与满额试用/0元尝鲜
        List<FreeTrialSubActivityProduct> subActivityProductList = freeTrialSubActivityProductDao.selectMainByProductIds(Arrays.asList(productId));
        List<Long> activityIds = StreamUtils.toList(subActivityProductList, FreeTrialSubActivityProduct::getFreeTrialSubActivityId);
        List<FreeTrialSubActivity> freeTrialSubActivityList = freeTrialSubActivityDao.findByIds(activityIds, null);
        List<FreeTrialSubActivity> conflictFullTrialActivities = StreamUtils.filter(freeTrialSubActivityList,
                e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()).contains(e.getStatus()));
        if(CollectionUtils.isNotEmpty(conflictFullTrialActivities)){
            FreeTrialSubActivity freeTrialSubActivity = StreamUtils.getFirst(conflictFullTrialActivities);
            throw new BusinessException("商品id" + productId +"在" + FreeTrialSubActivityTypeEnum.getDescByCode(freeTrialSubActivity.getActivityType())+ "活动：" + freeTrialSubActivity.getName() + " 中已存在");
        }
    }


    private void validateActivityStock(PtActivitySaveDTO saveDTO) {
        Long stock = saveDTO.getStock();
        Long skuId = saveDTO.getProductInfo().getSkuId();
        SkuStock skuStock = stockManager.findSaleStockBySkuId(skuId);
        Asserts.notNull(skuStock,"库存信息为空");
        if (skuStock.getStockNum() < stock) {
            throw new BusinessException("活动库存不得大于实际库存");
        }
        Long id = saveDTO.getId();
        if (Objects.nonNull(id)) {
            PtActivity ptActivity = ptActivityManager.getById(id);
            Asserts.notNull(ptActivity, "活动不存在");
            Date now = new Date();
            if (ptActivity.getStartTime().before(now) && ptActivity.getEndTime().after(now)) {
                PtOrderListDTO listDTO = new PtOrderListDTO();
                listDTO.setPtActivityIds(Lists.newArrayList(id));
                listDTO.setPtOrderStatusList(Lists.newArrayList(PtOrderStatusEnum.FORMED.getCode(), PtOrderStatusEnum.WAIT.getCode(), PtOrderStatusEnum.NO_START.getCode()));
                List<PtOrder> ptOrders = ptOrderManager.findByCondition(listDTO);
                long occupyCount = ptOrders.stream().mapToLong(PtOrder::getOccupyNum).sum();
                // 该活动下所有团的预占库存 + 拼团子订单的(已支付 + 未支付)的buyCount[剔除机器人]
                List<String> ptOrderNos = ptOrders.stream().map(PtOrder::getPtOrderNo).collect(Collectors.toList());
                List<PtSubOrder> subOrders = ptSubOrderManager.getByPtOrderNos(ptOrderNos);
                long buyCount = subOrders.stream().filter(e ->e.getHasCaptain() != 2).mapToLong(PtSubOrder::getBuyCount).sum();
                long minStock = new BigDecimal(occupyCount).add(new BigDecimal(buyCount)).longValue();
                if (stock < minStock) {
                    throw new BusinessException("活动库存不得小于" + minStock);
                }
            }
        }
    }


    private PtActivityVO assemblePtActivityVO(PtActivity ptActivity, PtGoods ptGoods, Sku sku) {
        PtActivityVO vo = new PtActivityVO();
        vo.setId(ptActivity.getId());
        vo.setName(ptActivity.getName());
        vo.setStartTime(ptActivity.getStartTime());
        vo.setEndTime(ptActivity.getEndTime());
        vo.setPeopleLimit(ptActivity.getPeopleLimit());

        ProductInfo productInfo = new ProductInfo();
        productInfo.setSkuId(ptGoods.getSkuId());
        productInfo.setSkuName(ptGoods.getSkuName());
        productInfo.setProductId(ptGoods.getProductId());
        productInfo.setProductName(ptGoods.getProductName());
        productInfo.setPicUrl(ptGoods.getPicUrl());
        productInfo.setSpecValueList(ptGoods.getSpecs());
        productInfo.setListPrice(sku.getListPrice());
        productInfo.setSalePrice(sku.getSalePrice());
        productInfo.setPtPrice(ptGoods.getPtPrice());
        productInfo.setStock(ptActivity.getStock());

        vo.setProductInfo(productInfo);
        vo.setValidityPeriod(ptActivity.getValidityPeriod());
        vo.setRequiredNum(ptActivity.getRequiredNum());
        vo.setStock(ptActivity.getStock());
        vo.setLimitNum(ptActivity.getLimitNum());
        vo.setEnableMocker(ptActivity.getEnableMocker());
        vo.setAdvanceNotice(ptActivity.getAdvanceNotice());
        vo.setAdvanceHour(ptActivity.getAdvanceHour());
        vo.setShareDiscount(ptActivity.getShareDiscount());
        String customerMark = ptActivity.getCustomerMark();
        if (StringUtils.isNotEmpty(customerMark)) {
            vo.setCustomerMarkList(Arrays.asList(customerMark.split(",")));
        }

        return vo;
    }

    private PtGoods assemblePtGoodsInfo(Long ptActivityId, PtActivitySaveDTO.ProductInfo productInfo, String userName) {
        Long productId = productInfo.getProductId();
        Long skuId = productInfo.getSkuId();
        Product product = productManager.getOne(productId);
        Asserts.notNull(product, "商品信息不存在");
        Sku sku = skuManager.selectById(skuId);
        Asserts.notNull(sku, "sku不存在");
        PtGoods ptGoods = new PtGoods();
        ptGoods.setPtActivityId(ptActivityId);
        ptGoods.setProductId(productId);
        ptGoods.setProductName(product.getProductName());
        ptGoods.setPicUrl(product.getPicUrl());
        ptGoods.setSkuId(skuId);
        ptGoods.setSkuName(sku.getSkuName());
        ptGoods.setPtPrice(productInfo.getPtPrice());
        ptGoods.setListPrice(sku.getListPrice());
        ptGoods.setSpecs(sku.getSpecValueList());
        ptGoods.setCreateTime(new Date());
        ptGoods.setUpdateTime(new Date());
        ptGoods.setOperator(userName);
        ptGoods.setDelflag(0);

        return ptGoods;
    }

    private PtActivity assemblePtActivity(PtActivitySaveDTO saveDTO, String userName) {
        PtActivity activity = new PtActivity();
        activity.setId(saveDTO.getId());
        activity.setName(saveDTO.getName());
        activity.setStartTime(saveDTO.getStartTime());
        activity.setEndTime(saveDTO.getEndTime());
        activity.setPeopleLimit(saveDTO.getPeopleLimit());
        activity.setValidityPeriod(saveDTO.getValidityPeriod());
        activity.setRequiredNum(saveDTO.getRequiredNum());
        activity.setStock(saveDTO.getStock());
        activity.setLimitNum(saveDTO.getLimitNum());
        activity.setEnableMocker(saveDTO.getEnableMocker());
        activity.setAdvanceNotice(saveDTO.getAdvanceNotice());
        activity.setAdvanceHour(saveDTO.getAdvanceHour());
        if (StringUtils.isEmpty(saveDTO.getShareDiscount())) {
            activity.setShareDiscount("0");
        } else {
            activity.setShareDiscount(saveDTO.getShareDiscount());
        }
        if(CollectionUtils.isNotEmpty(saveDTO.getCustomerMarkList())){
            activity.setCustomerMark(String.join(",", saveDTO.getCustomerMarkList()));
        }
        if (Objects.isNull(saveDTO.getId())) {
            activity.setCreateTime(new Date());
            activity.setUsed(0L);
        }
        activity.setUpdateTime(new Date());
        activity.setOperator(userName);
        activity.setDelflag(0);
        return activity;
    }

    private PtActivityListVO assemblePtActivityListVO(PtActivity ptActivity, Map<Long, PtDataStatisticsDTO> statisticsDTOMap) {
        PtActivityListVO listVO = new PtActivityListVO();
        Long activityId = ptActivity.getId();
        listVO.setId(activityId);
        listVO.setName(ptActivity.getName());
        listVO.setStartTime(ptActivity.getStartTime());
        listVO.setEndTime(ptActivity.getEndTime());
        listVO.setRequiredNum(ptActivity.getRequiredNum());
        listVO.setStock(ptActivity.getStock());
        PtDataStatisticsDTO statisticsDTO = statisticsDTOMap.getOrDefault(activityId, new PtDataStatisticsDTO());
        listVO.setJoinedNum(statisticsDTO.getJoinedNum());
        listVO.setFormedNum(statisticsDTO.getFormedNum());
        listVO.setFormedAmount(statisticsDTO.getFormedAmount());
        listVO.setProductName(statisticsDTO.getProductName());
        return listVO;
    }


}
