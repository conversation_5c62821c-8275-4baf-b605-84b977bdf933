package com.hengtiansoft.operation.pintuan.vo;

import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 18:07
 **/
@Data
public class PtActivityListVO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 活动开始时间
     */
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty("活动结束时间")
    private Date endTime;


    /**
     * 成团人数
     */
    @ApiModelProperty("成团人数")
    private Integer requiredNum;

    /**
     * 库存
     */
    @ApiModelProperty("库存")
    private Long stock;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("参与人数")
    private Integer joinedNum;

    @ApiModelProperty("成团总人数")
    private Integer formedNum;

    @ApiModelProperty("成团总金额")
    private BigDecimal formedAmount;

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
        if (this.startTime == null && this.endTime == null) {
            this.status = CommonActivityStatusEnum.IN_PROGRESS.getCode();
            this.statusDesc = CommonActivityStatusEnum.IN_PROGRESS.getDesc();
        } else if (this.endTime.before(new Date())) {
            this.status = CommonActivityStatusEnum.END.getCode();
            this.statusDesc = CommonActivityStatusEnum.END.getDesc();
        } else if (this.startTime.after(new Date())) {
            this.status = CommonActivityStatusEnum.NOT_STARTED.getCode();
            this.statusDesc = CommonActivityStatusEnum.NOT_STARTED.getDesc();
        } else {
            this.status = CommonActivityStatusEnum.IN_PROGRESS.getCode();
            this.statusDesc = CommonActivityStatusEnum.IN_PROGRESS.getDesc();
        }
    }

}
