package com.hengtiansoft.operation.comment.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.CommentTagListDTO;
import com.hengtiansoft.item.entity.dto.CommentTagSaveDTO;
import com.hengtiansoft.item.entity.vo.CommentTagListVO;

import java.util.List;

public interface CommentTagService {

    /**
     * 评价标签分页
     * @param dto
     * @return
     */
    PageVO<CommentTagListVO> page(CommentTagListDTO dto);

    /**
     * 评价标签列表
     * @param dto
     * @return
     */
    List<CommentTagListVO> list(CommentTagListDTO dto);

    /**
     * 评价标签保存
     * @param dto
     * @return
     */
    void save(CommentTagSaveDTO dto);

    /**
     * 评价标签删除
     * @param id
     * @return
     */
    void delete(Long id);
}
