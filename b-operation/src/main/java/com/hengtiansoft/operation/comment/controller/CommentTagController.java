package com.hengtiansoft.operation.comment.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.CommentTagListDTO;
import com.hengtiansoft.item.entity.dto.CommentTagSaveDTO;
import com.hengtiansoft.item.entity.vo.CommentTagListVO;
import com.hengtiansoft.operation.comment.service.CommentTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "评价标签管理")
@RequestMapping("/comment/tag")
public class CommentTagController {

    @Resource
    private CommentTagService commentTagService;

    @ApiOperation("分页")
    @PostMapping("/page")
    public Response<PageVO<CommentTagListVO>> page(@RequestBody CommentTagListDTO dto) {
        return ResponseFactory.success(commentTagService.page(dto));
    }


    @ApiOperation("列表")
    @PostMapping("/list")
    public Response<List<CommentTagListVO>> list(@RequestBody CommentTagListDTO dto) {
        return ResponseFactory.success(commentTagService.list(dto));
    }


    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<String> save(@RequestBody CommentTagSaveDTO dto) {
        commentTagService.save(dto);
        return ResponseFactory.success();
    }


    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<String> delete(@RequestParam Long id) {
        commentTagService.delete(id);
        return ResponseFactory.success();
    }

}
