package com.hengtiansoft.operation.comment.service.impl;

import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CommentTagJoinDao;
import com.hengtiansoft.item.entity.dto.CommentTagJoinCategoryDTO;
import com.hengtiansoft.item.entity.dto.CommentTagListDTO;
import com.hengtiansoft.item.entity.dto.CommentTagSaveDTO;
import com.hengtiansoft.item.entity.po.CommentTag;
import com.hengtiansoft.item.entity.vo.CommentTagListVO;
import com.hengtiansoft.item.enumeration.CommentTagJoinTypeEnum;
import com.hengtiansoft.item.enumeration.CommentTagTypeEnum;
import com.hengtiansoft.item.manager.CommentTagJoinManager;
import com.hengtiansoft.item.manager.CommentTagManager;
import com.hengtiansoft.operation.comment.service.CommentTagService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.github.pagehelper.page.PageMethod.startPage;

@Service
public class CommentTagServiceImpl implements CommentTagService {

    @Autowired
    private CommentTagManager commentTagManager;

    @Autowired
    private CommentTagJoinManager commentTagJoinManager;

    @Autowired
    private CommentTagJoinDao commentTagJoinDao;

    @Override
    public PageVO<CommentTagListVO> page(CommentTagListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        dto.setTagType(CommentTagTypeEnum.CUSTOM.getCode());
        List<CommentTag> commentTagList = commentTagManager.search(dto);
        if(CollectionUtils.isEmpty(commentTagList)){
            return PageUtils.emptyPage(dto);
        }

        List<Long> tagIds = StreamUtils.toList(commentTagList, CommentTag::getId);
        List<CommentTagJoinCategoryDTO> categoryDTOS = commentTagJoinDao.findTagJoinCategoryByTagIds(tagIds);
        Map<Long, List<CommentTagJoinCategoryDTO>> tagJoinCateMap = StreamUtils.group(categoryDTOS, CommentTagJoinCategoryDTO::getTagId);

        return PageUtils.convert(commentTagList, tag -> {
            CommentTagListVO vo = new CommentTagListVO();
            List<CommentTagJoinCategoryDTO> tagJoinCateList= tagJoinCateMap.get(tag.getId());
            BeanUtils.copy(tag, vo);
            vo.setCategoryName(StreamUtils.joinStringFilter(tagJoinCateList,",", CommentTagJoinCategoryDTO::getCateName, StringUtils::isNotBlank));
            return vo;
        });
    }

    @Override
    public List<CommentTagListVO> list(CommentTagListDTO dto) {
        dto.setTagType(CommentTagTypeEnum.CUSTOM.getCode());
        List<CommentTag> commentTagList = commentTagManager.search(dto);
        return BeanUtils.copyList(commentTagList, CommentTagListVO::new);
    }

    @Override
    public void save(CommentTagSaveDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        if (Objects.isNull(detailDTO)) {
            throw new BusinessException("请登录后重试！");
        }

        //校验标签名字是否存在
        commentTagManager.checkTagName(dto);
        //保存or更新
        commentTagManager.save(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        if (Objects.isNull(detailDTO)) {
            throw new BusinessException("请登录后重试！");
        }
        //删除评论标签表
        commentTagManager.deleteById(id);
        //删除评论标签关联表（类目）
        commentTagJoinManager.deleteByTagIdWithType(id, CommentTagJoinTypeEnum.CATEGORY.getCode());
    }
}
