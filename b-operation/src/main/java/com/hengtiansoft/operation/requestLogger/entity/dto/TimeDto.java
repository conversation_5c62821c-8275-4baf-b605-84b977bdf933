/*
 * Project Name: fsc
 * File Name: TimeDto.java
 * Class Name: TimeDto
 *
 * Copyright 2018 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.operation.requestLogger.entity.dto;

import java.util.Date;

/**
 * Description: 请求时间Dto
 *
 * <AUTHOR>
 * @since 24.07.2018
 */
public class TimeDto {
    private Long operatorId;
    private Date operationTime;
    private long startTime;

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    @Override
    public String toString() {
        return "TimeDto{" +
                "operatorId=" + operatorId +
                ", operationTime=" + operationTime +
                ", startTime=" + startTime +
                '}';
    }
}
