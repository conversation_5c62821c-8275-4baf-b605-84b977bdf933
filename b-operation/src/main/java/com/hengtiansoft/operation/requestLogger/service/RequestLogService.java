package com.hengtiansoft.operation.requestLogger.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.requestLogger.entity.dto.RequestLogSearchDTO;
import com.hengtiansoft.operation.requestLogger.entity.vo.RequestLogVO;

/**
 * Description: 通知模板service
 *
 * <AUTHOR>
 * @since 11.10.2019
 */
public interface RequestLogService {

    PageVO<RequestLogVO> getList(RequestLogSearchDTO searchDTO);

}