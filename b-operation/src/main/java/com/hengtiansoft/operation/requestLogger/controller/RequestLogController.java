package com.hengtiansoft.operation.requestLogger.controller;


import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.requestLogger.entity.dto.RequestLogSearchDTO;
import com.hengtiansoft.operation.requestLogger.entity.vo.RequestLogVO;
import com.hengtiansoft.operation.requestLogger.service.RequestLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 日志记录
 *
 */
@RestController
@RequestMapping("/requestLog")
@Api(tags = "日志管理")
public class RequestLogController {

    @Autowired
    private RequestLogService requestLogService;

    @ApiOperation(value = "列表", response = RequestLogVO.class)
    @PostMapping("/list")
    public Response<PageVO<RequestLogVO>> list(@RequestBody RequestLogSearchDTO searchDTO) {
        return ResponseFactory.success(requestLogService.getList(searchDTO));
    }

}