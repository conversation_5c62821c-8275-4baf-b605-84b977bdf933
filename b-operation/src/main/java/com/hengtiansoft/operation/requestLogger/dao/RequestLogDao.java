package com.hengtiansoft.operation.requestLogger.dao;

import com.hengtiansoft.common.enumeration.RequestModuleTypeEnum;
import com.hengtiansoft.operation.requestLogger.entity.dto.RequestLogSearchDTO;
import com.hengtiansoft.operation.requestLogger.entity.po.RequestLog;
import com.hengtiansoft.operation.requestLogger.mapper.RequestLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;

@Repository
public class RequestLogDao {

    @Autowired
    private RequestLogMapper requestLogMapper;

    /**
     * 新增日志记录
     * @param requestLog
     */
    public void insertRequestLog(RequestLog requestLog) {
        requestLogMapper.insertSelective(requestLog);
    }

    /**
     * 查询列表
     * @param searchDTO
     * @return
     */
    public List<RequestLog> getList(RequestLogSearchDTO searchDTO){
        Example example = new Example(RequestLog.class);
        Example.Criteria criteria = example.createCriteria();
        if(!StringUtils.isEmpty(searchDTO.getOperator())){
            criteria.andLike("operatorUserName", "%" + searchDTO.getOperator() + "%");
        }
        if(Objects.nonNull(searchDTO.getOperationStartTime())){
            criteria.andGreaterThanOrEqualTo("operationTime", searchDTO.getOperationStartTime());
        }
        if(Objects.nonNull(searchDTO.getOperationEndTime())){
            criteria.andLessThanOrEqualTo("operationTime", searchDTO.getOperationEndTime());
        }
        example.setOrderByClause("create_time DESC");
        return requestLogMapper.selectByExample(example);
    }

    public List<RequestLog> queryOperation(Long id, RequestModuleTypeEnum typeEnum) {
        Example example = new Example(RequestLog.class);
        Example.Criteria criteria = example.createCriteria();
        if(Objects.nonNull(id)){
            criteria.andEqualTo("keyId", id);
        }
        if(Objects.nonNull(typeEnum)){
            criteria.andEqualTo("moduleType", typeEnum.getCode());
        }
        example.setOrderByClause("create_time DESC");
        return requestLogMapper.selectByExample(example);
    }
}