package com.hengtiansoft.operation.requestLogger.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 **/
@Data
@ApiModel
public class RequestLogVO {

    private Long id;

    @ApiModelProperty(value = "模块id，0-未知，1-后台管理 2-小程序")
    private Integer moduleId;

    @ApiModelProperty(value = "操作员id")
    private Long operatorId;

    @ApiModelProperty(value = "操作员账号")
    private String operatorUserName;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "ip地址")
    private String ipAddress;

    @ApiModelProperty(value = "请求地址")
    private String uri;

    @ApiModelProperty(value = "controller注解")
    private String controllerNote;

    @ApiModelProperty(value = "方法注解")
    private String methodNote;

    @ApiModelProperty(value = "执行时长（毫秒）")
    private Long executionTime;

    @ApiModelProperty(value = "操作员名称")
    private String operatorName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "请求结果")
    private String requestStatus;

}