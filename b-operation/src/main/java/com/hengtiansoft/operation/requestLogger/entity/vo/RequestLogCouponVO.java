package com.hengtiansoft.operation.requestLogger.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("优惠券操作日志VO")
public class RequestLogCouponVO {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "操作员id")
    private Long operatorId;

    @ApiModelProperty(value = "操作员账号")
    private String operatorUserName;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}