package com.hengtiansoft.operation.requestLogger.service.impl;

import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.operation.requestLogger.dao.RequestLogDao;
import com.hengtiansoft.operation.requestLogger.entity.dto.RequestLogSearchDTO;
import com.hengtiansoft.operation.requestLogger.entity.po.RequestLog;
import com.hengtiansoft.operation.requestLogger.entity.vo.RequestLogVO;
import com.hengtiansoft.operation.requestLogger.service.RequestLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: 操作日志service实现
 */
@Service
public class RequestLogServiceImpl implements RequestLogService {

    @Resource
    private RequestLogDao requestLogDao;

    @Override
    public PageVO<RequestLogVO> getList(RequestLogSearchDTO searchDTO) {

        PageHelper.startPage(searchDTO.getPageNum(), searchDTO.getPageSize(), true);
        List<RequestLog> logs = requestLogDao.getList(searchDTO);
        if (CollectionUtils.isEmpty(logs)) {
            return PageUtils.emptyPage();
        }

        return PageUtils.convert(logs, requestLog -> BeanUtils.deepCopy(requestLog, RequestLogVO.class));
    }

}
