package com.hengtiansoft.operation.requestLogger.entity.dto;

import com.hengtiansoft.common.entity.dto.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "日志管理")
@EqualsAndHashCode(callSuper = true)
public class RequestLogSearchDTO extends PageParams implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员账号")
    private String operator;

    @ApiModelProperty("操作开始时间")
    private Date operationStartTime;

    @ApiModelProperty("操作结束时间")
    private Date operationEndTime;

}