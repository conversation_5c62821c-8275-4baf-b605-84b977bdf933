package com.hengtiansoft.operation.requestLogger.entity.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("记录用户请求日志 ")
@Table(name = "`request_log`")
public class RequestLog implements Serializable {
    @Id
    @Column(name = "`id`")
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 模块id，0-未知，1-后台管理 2-小程序
     */
    @Column(name = "`module_id`")
    @ApiModelProperty("模块id，0-未知，1-后台管理 2-小程序")
    private Integer moduleId;

    /**
     * 操作员id
     */
    @Column(name = "`operator_id`")
    @ApiModelProperty("操作员id")
    private Long operatorId;

    /**
     * 操作员账号
     */
    @Column(name = "`operator_user_name`")
    @ApiModelProperty("操作员账号")
    private String operatorUserName;

    /**
     * 操作员名称
     */
    @Column(name = "`operator_name`")
    @ApiModelProperty("操作员名称")
    private String operatorName;

    /**
     * 操作时间
     */
    @Column(name = "`operation_time`")
    @ApiModelProperty("操作时间")
    private Date operationTime;

    /**
     * ip地址
     */
    @Column(name = "`ip_address`")
    @ApiModelProperty("ip地址")
    private String ipAddress;

    /**
     * 请求地址
     */
    @Column(name = "`uri`")
    @ApiModelProperty("请求地址")
    private String uri;

    /**
     * controller注解
     */
    @Column(name = "`controller_note`")
    @ApiModelProperty("controller注解")
    private String controllerNote;

    /**
     * 方法注解
     */
    @Column(name = "`method_note`")
    @ApiModelProperty("方法注解")
    private String methodNote;

    /**
     * 执行时长（毫秒）
     */
    @Column(name = "`execution_time`")
    @ApiModelProperty("执行时长（毫秒）")
    private Long executionTime;

    /**
     * 请求结果
     */
    @Column(name = "`request_status`")
    @ApiModelProperty("请求结果")
    private String requestStatus;

    /**
     * 创建时间
     */
    @Column(name = "`create_time`", insertable = false, updatable = false)
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 请求参数
     */
    @Column(name = "`params`")
    @ApiModelProperty("请求参数")
    private String params;

    /**
     * 模块类型：1优惠券
     */
    @Column(name = "`module_type`")
    @ApiModelProperty("模块类型：1优惠券")
    private Integer moduleType;

    /**
     * 关键id
     */
    @Column(name = "`key_id`")
    @ApiModelProperty("关键id")
    private Long keyId;

    private static final long serialVersionUID = 1L;
}