package com.hengtiansoft.operation.fullReduce.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.dto.FullReduceSaveDTO;
import com.hengtiansoft.privilege.entity.dto.FullReduceSearchDTO;
import com.hengtiansoft.privilege.entity.vo.FullReduceDetailVO;
import com.hengtiansoft.privilege.entity.vo.FullReduceVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import com.hengtiansoft.privilege.enums.FullReduceStatusEnum;

public interface FullReduceService {

    /**
     * 满减活动分页
     * @param dto
     * @return
     */
    PageVO<FullReduceVO> getPage(FullReduceSearchDTO dto);

    /**
     * 满减活动保存
     * @param dto
     */
    void save(FullReduceSaveDTO dto);

    /**
     * 满减活动删除
     * @param id
     */
    void delete(Long id);

    /**
     * 满减活动推广
     * @param id
     */
    PromoVO promotion(Long id);

    /**
     * 满减活动详情
     * @param id
     */
    FullReduceDetailVO get(Long id);

    /**
     * 满减活动导出
     * @param dto
     */
    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    /**
     * 满减活动上下线
     * @param id,reduceStatusEnum
     */
    void setFullReduceStatus(Long id, FullReduceStatusEnum reduceStatusEnum);
}
