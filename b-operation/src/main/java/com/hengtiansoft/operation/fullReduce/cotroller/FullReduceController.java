package com.hengtiansoft.operation.fullReduce.cotroller;


import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.fullReduce.service.FullReduceService;
import com.hengtiansoft.privilege.entity.dto.FullReduceSaveDTO;
import com.hengtiansoft.privilege.entity.dto.FullReduceSearchDTO;
import com.hengtiansoft.privilege.entity.vo.FullReduceDetailVO;
import com.hengtiansoft.privilege.entity.vo.FullReduceVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import com.hengtiansoft.privilege.enums.FullReduceStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "满减满送")
@RequestMapping("/fullReduce")
public class FullReduceController {

    @Autowired
    private FullReduceService fullReduceService;

    @ApiOperation("分页列表")
    @PostMapping("/getPage")
    public Response<PageVO<FullReduceVO>> getPage(@RequestBody FullReduceSearchDTO dto) {
        return ResponseFactory.success(fullReduceService.getPage(dto));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Object> save(@RequestBody FullReduceSaveDTO dto) {
        fullReduceService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Object> delete(@RequestParam Long id) {
        fullReduceService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<FullReduceDetailVO> get(@RequestParam Long id) {
        return ResponseFactory.success(fullReduceService.get(id));
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        return ResponseFactory.success(fullReduceService.promotion(id));
    }

    @ApiOperation("开始活动")
    @GetMapping("/start")
    public Response<Object> start(@RequestParam Long id) {
        fullReduceService.setFullReduceStatus(id, FullReduceStatusEnum.IN_PROGRESS);
        return ResponseFactory.success();
    }

    @ApiOperation("结束活动")
    @GetMapping("/end")
    public Response<Object> end(@RequestParam Long id) {
        fullReduceService.setFullReduceStatus(id, FullReduceStatusEnum.END);
        return ResponseFactory.success();
    }

}
