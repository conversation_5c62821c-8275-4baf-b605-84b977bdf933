package com.hengtiansoft.operation.fullReduce.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.LaActivityTypeEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.dao.SkuStockDao;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.dto.SkuBaseDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.entity.vo.SkuSaleStockVO;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.operation.fullReduce.service.FullReduceService;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.dao.CouponRuleDao;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.entity.vo.CouponRuleListVO;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.privilege.dao.FullReduceDao;
import com.hengtiansoft.privilege.dao.FullReduceProductRangeDao;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.*;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.privilege.util.FullReduceUtil;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.github.pagehelper.page.PageMethod.startPage;

@Slf4j
@Service
public class FullReduceServiceImpl implements FullReduceService {

    @Resource
    private FullReduceManager fullReduceManager;
    @Resource
    private FullReduceDao fullReduceDao;
    @Resource
    private FullReduceRuleManager fullReduceRuleManager;
    @Resource
    private FullReduceRuleRangeManager fullReduceRuleRangeManager;
    @Resource
    private FullReduceProductRangeManager fullReduceProductRangeManager;
    @Resource
    private FullReduceProductRangeDao fullReduceProductRangeDao;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private MilkProducerService milkProducerService;
    @Resource
    private MonitorPageManager monitorPageManager;
    @Resource
    private ProductDao productDao;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private SkuStockDao skuStockDao;
    @Resource
    private CouponRuleDao couponRuleDao;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private PeopleLabelManager peopleLabelManager;
    @Resource
    private PtActivityManager ptActivityManager;
    @Resource
    private PtGoodsManager ptGoodsManager;
    @Resource
    private ProductManager productManager;

    @Override
    public PageVO<FullReduceVO> getPage(FullReduceSearchDTO dto) {


        if (Objects.nonNull(dto.getProductId()) || StringUtils.isNotEmpty(dto.getSkuCode())) {
            List<Long> fullReduceIds = fullReduceProductRangeDao.findByProductOrSkuCode(dto.getProductId(), dto.getSkuCode());
            if (CollectionUtils.isEmpty(fullReduceIds)) {
                return PageUtils.emptyPage(dto);
            }
            dto.setIds(fullReduceIds);
        }

        startPage(dto.getPageNum(), dto.getPageSize());
        List<FullReduce> fullReduceList = fullReduceManager.findByCondition(dto);

        if (CollectionUtils.isEmpty(fullReduceList)) {
            return PageUtils.emptyPage(dto);
        }
        List<Long> ids = StreamUtils.toList(fullReduceList, FullReduce::getId);
        List<FullReduceRule> fullReduceRuleList = fullReduceRuleManager.findByFullIds(ids);
        Map<Long, List<FullReduceRule>> fullReduceRuleMap = StreamUtils.group(fullReduceRuleList, FullReduceRule::getFullReduceId);

        List<FullReduceRuleRange> reduceRuleRangeList = fullReduceRuleRangeManager.findByFullReduceIds(ids);
        Map<Long, List<FullReduceRuleRange>> ruleRangeMap = StreamUtils.group(reduceRuleRangeList, FullReduceRuleRange::getFullReduceRuleId);

        return PageUtils.convert(fullReduceList, data ->{
            FullReduceVO fullReduceVO = FullReduceUtil.buildVO(data);
            List<FullReduceRule> reduceRuleList = fullReduceRuleMap.get(data.getId());
            StringBuilder sb = new StringBuilder();
            if(CollectionUtils.isNotEmpty(reduceRuleList)){
                int i = 0;
                for (FullReduceRule reduceRule: reduceRuleList) {
                    i ++;
                    List<FullReduceRuleRange> ruleRangeList = ruleRangeMap.get(reduceRule.getId());
                    List<FullReduceRuleRange> giftRangeList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
                    List<FullReduceRuleRange> couponRangeList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));
                    if(FullReduceDiscountRuleEnum.LOOP.getCode().equals(data.getDiscountRule())){
                        sb.append("每满");
                    }else if(FullReduceDiscountRuleEnum.STEP.getCode().equals(data.getDiscountRule())){
                        sb.append("满");
                    }
                    if(FullReduceTypeEnum.FULL_COUNT.getCode().equals(data.getType())){
                        sb.append(reduceRule.getFullCount()).append("件");
                    }else{
                        sb.append(reduceRule.getFullAmount());
                    }
                    if(FullRuleReduceTypeEnum.REDUCE.getCode().equals(reduceRule.getReduceType())){
                        sb.append("减").append(reduceRule.getReduceAmount());
                    }else if(FullRuleReduceTypeEnum.DISCOUNT.getCode().equals(reduceRule.getReduceType())){
                        sb.append("打").append(reduceRule.getDiscount()).append("折");
                    }
                    if(CollectionUtils.isNotEmpty(giftRangeList)){
                        sb.append("送1件赠品");
                    }
                    if(CollectionUtils.isNotEmpty(couponRangeList)){
                        // 根据couponType区分优惠券和赠品券
                        long couponCount = couponRangeList.stream()
                                .filter(range -> FullRuleRangeCouponTypeEnum.COUNPON.getCode().equals(range.getCouponType()))
                                .count();
                        long giftCouponCount = couponRangeList.stream()
                                .filter(range -> FullRuleRangeCouponTypeEnum.GIFT_COUNPON.getCode().equals(range.getCouponType()))
                                .count();
                        
                        if (couponCount > 0) {
                            sb.append("送").append(couponCount).append("张优惠券");
                        }
                        if (giftCouponCount > 0) {
                            sb.append("送").append(giftCouponCount).append("张赠品券");
                        }
                    }
                    if(i < reduceRuleList.size()){
                        sb.append("<br>");
                    }
                }
            }
            fullReduceVO.setContent(sb.toString());
            return fullReduceVO;
        });
    }


    @Override
    public void save(FullReduceSaveDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        //参数校验
        this.checkParam(dto);
        dto.setOperator(user.getUserName());
        //编辑标记
        boolean updateFlag = false;
        if(null != dto.getId()){
            updateFlag = true;
        }
        //活动状态
        this.setStatusByTime(dto);
        List<FullReduceProductRange> delProductRange = new ArrayList<>();
        if(updateFlag){
            delProductRange = fullReduceProductRangeManager.findByFullReduceId(dto.getId());
        }
        boolean executeStatus = transactionTemplate.execute(status -> {
            //满减满送活动主表
            fullReduceManager.save(dto);
            //活动优惠规则
            fullReduceRuleManager.save(dto);
            //活动商品范围
            fullReduceProductRangeManager.save(dto);
            //更新手动标签信息
            if(CollectionUtils.isNotEmpty(dto.getCustomerMarkList())){
                List<Long> labelIds = StreamUtils.convert(dto.getCustomerMarkList(), Long::valueOf);
                peopleLabelManager.updateActivity(labelIds, dto.getId(), LaActivityTypeEnum.FULL_REDUCE.getCode());
            }
            if(CollectionUtils.isNotEmpty(dto.getOldCustomerMarkList())){
                List<Long> labelIds = StreamUtils.convert(dto.getOldCustomerMarkList(), Long::valueOf);
                peopleLabelManager.updateActivity(labelIds, 0L, LaActivityTypeEnum.DEFAULT.getCode());
            }
            return true;
        });
        if(executeStatus){
            //删除redis
            if(CollectionUtils.isNotEmpty(delProductRange)){
                List<String> keys = StreamUtils.convert(delProductRange, x -> FullReduceUtil.FULL_REDUCE_KEY + x.getSpuId());
                redisOperation.del(keys);
            }
            //新增redis
            if(FullReduceStatusEnum.IN_PROGRESS.getCode().equals(dto.getStatus())){
                fullReduceProductRangeManager.addRedisByFullId(dto.getId());
            }

            if(!updateFlag){
                //推广
                MonitorPageDTO monitorPage = new MonitorPageDTO();
                monitorPage.setName(dto.getName()+"_满减满送");
                monitorPage.setTargetId(dto.getId());
                monitorPage.setType(MoTypeEnum.FULL_REDUCE.getCode());
                monitorPage.setTargetName(dto.getName());
                monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
                monitorPageService.insertOrUpdate(monitorPage);
            }
            if(updateFlag){
                milkProducerService.fullReduceUpdate(dto.getId());
            }
        }
    }

    private void setStatusByTime(FullReduceSaveDTO dto) {
        Date now = new Date();
        if(dto.getStartTime().before(now)){
            dto.setStatus(FullReduceStatusEnum.IN_PROGRESS.getCode());
        }
        if(dto.getEndTime().before(now)){
            dto.setStatus(FullReduceStatusEnum.END.getCode());
        }
    }

    private void checkParam(FullReduceSaveDTO dto) {
        //活动主信息
        verifyBaseParam(dto);
        //活动商品
        verifyProduct(dto);
        //优惠规则校验
        verifyReduceRule(dto);
    }

    private void verifyProduct(FullReduceSaveDTO dto) {
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(dto.getType());
        if (null == reduceTypeEnum) {
            throw new BusinessException("活动类型不存在！");
        }
        if(CollectionUtils.isEmpty(dto.getFullReduceProductRangeList())){
            throw new BusinessException("商品范围不能为空！");
        }
        if(dto.getFullReduceProductRangeList().size() > 50){
            throw new BusinessException("商品范围不能超过50个！");
        }
        List<Long> fullReduceProductIds = dto.getFullReduceProductRangeList();
        Map<Long, Product> productMap = StreamUtils.toMap(productDao.findByIds(fullReduceProductIds), Product::getId);
        List<FullReduceProductRange> productRangeList = fullReduceProductRangeDao.findBySpuIds(dto.getFullReduceProductRangeList());
        List<Long> fullReduceIds = StreamUtils.toList(productRangeList, FullReduceProductRange::getFullReduceId);
        Map<Long, FullReduce> fullReduceMap = StreamUtils.toMap(fullReduceDao.findByIdsAndStatus(fullReduceIds, Arrays.asList(FullReduceStatusEnum.NOT_STARTED.getCode(), FullReduceStatusEnum.IN_PROGRESS.getCode())), FullReduce::getId);

        //判断与拼团活动
        List<PtActivity> ptActivityList = ptActivityManager.findNotStartedAndInProgress();
        List<PtGoods> ptGoodsList = ptGoodsManager.getByActivityIds(StreamUtils.toList(ptActivityList, PtActivity::getId));
        List<Long> ptSpuIds = StreamUtils.toList(ptGoodsList, PtGoods::getProductId);
        dto.getFullReduceProductRangeList().forEach(spuId -> {
            if(ptSpuIds.contains(spuId)){
                throw new BusinessException("商品【" + productMap.get(spuId).getProductName()+"】已参加未结束的拼团活动，请修改");
            }
        });

        for (FullReduceProductRange productRange: productRangeList) {
            FullReduce fullReduce = fullReduceMap.get(productRange.getFullReduceId());
            if(null == fullReduce || Objects.equals(fullReduce.getId(), dto.getId())){
                continue;
            }
            if (DateUtil.isOverlap(dto.getStartTime(), dto.getEndTime(), fullReduce.getStartTime(), fullReduce.getEndTime())) {
                throw new BusinessException("该商品【" + productMap.get(productRange.getSpuId()).getProductName()+"】的时间段与名称为【" + fullReduce.getName() + "】的活动有重复，请修改");
            }
        }
    }

    private void verifyBaseParam(FullReduceSaveDTO dto) {
        if (StringUtils.isBlank(dto.getName())) {
            throw new BusinessException("活动名称不能为空！");
        }
        if (dto.getName().length() > 15) {
            throw new BusinessException("活动名称不能超过15个字！");
        }
        FullReduceSearchDTO searchDTO = new FullReduceSearchDTO();
        searchDTO.setName(dto.getName());
        searchDTO.setNotId(dto.getId());
        if (CollectionUtils.isNotEmpty(fullReduceManager.findByCondition(searchDTO))) {
            throw new BusinessException("活动名称已存在！");
        }
        if (null == dto.getStartTime() || null == dto.getEndTime()) {
            throw new BusinessException("活动时间不能为空！");
        }
        if (dto.getStartTime().after(dto.getEndTime())) {
            throw new BusinessException("活动开始时间不能大于结束时间！");
        }
        if (null == FullReducePeopleLimitEnum.getEnumByCode(dto.getPeopleLimit())) {
            throw new BusinessException("参与人限制不能为空！");
        }
        if (null == FullReduceDiscountRuleEnum.getEnumByCode(dto.getDiscountRule())) {
            throw new BusinessException("优惠规则类型不能为空！");
        }
    }

    private void verifyReduceRule(FullReduceSaveDTO dto) {
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(dto.getType());
        if (CollectionUtils.isEmpty(dto.getFullReduceRuleList())) {
            throw new BusinessException("优惠规则不能为空！");
        }
        if (dto.getFullReduceRuleList().size() > 5) {
            throw new BusinessException("阶梯最多5级！");
        }
        //校验每一阶梯规则
        for (int i = 0; i < dto.getFullReduceRuleList().size(); i++) {
            FullReduceRuleSaveDTO reduceRule = dto.getFullReduceRuleList().get(i);
            reduceRule.setLevel(i + 1);
            verifyCount(i, reduceRule);
            //减价/折扣范围校验
            verifyReduceRange(i, reduceRule);
            //阶梯门槛校验
            verifyPreRule(i, dto, reduceTypeEnum, reduceRule);
            //校验库存
            verifyStock(i, reduceRule);
        }
        //校验折扣与减价
        verifyReduce(dto);
    }

    private void verifyReduce(FullReduceSaveDTO dto) {
        List<FullReduceRuleSaveDTO> reduceAmountList = StreamUtils.filter(dto.getFullReduceRuleList(), x -> null != x.getReduceAmount());
        for (int i = 0; i < reduceAmountList.size(); i++) {
            if(i > 0){
                FullReduceRuleSaveDTO reduceAmount = reduceAmountList.get(i);
                FullReduceRuleSaveDTO preReduceAmount = reduceAmountList.get(i - 1);
                if(preReduceAmount.getLevel() < reduceAmount.getLevel()
                        && preReduceAmount.getReduceAmount().compareTo(reduceAmount.getReduceAmount()) >= 0 ){
                    throw new BusinessException("第" + preReduceAmount.getLevel() + "级门槛的减价大于了第" + reduceAmount.getLevel() + "级门槛的减价！");
                }
            }
        }
        List<FullReduceRuleSaveDTO> discountList = StreamUtils.filter(dto.getFullReduceRuleList(), x -> null != x.getDiscount());
        for (int i = 0; i < discountList.size(); i++) {
            if(i > 0){
                FullReduceRuleSaveDTO discount = discountList.get(i);
                FullReduceRuleSaveDTO preDiscount = discountList.get(i - 1);
                if(preDiscount.getLevel() < discount.getLevel()
                        && preDiscount.getDiscount().compareTo(discount.getDiscount()) <= 0 ){
                    throw new BusinessException("第" + preDiscount.getLevel() + "级门槛的折扣力度大于了第" + discount.getLevel() + "级门槛的折扣力度！");
                }
            }
        }
    }

    private void verifyStock(int i, FullReduceRuleSaveDTO reduceRule) {
        if(CollectionUtils.isNotEmpty(reduceRule.getGiftRangeList())){
            List<SkuSaleStockVO> saleStockVOList = skuStockDao.findSaleStock(reduceRule.getGiftRangeList());
            SkuSaleStockVO skuSaleStockVO = StreamUtils.findFirst(saleStockVOList, x-> x.getStockNum() <= 0);
            if(null != skuSaleStockVO){
                SkuBaseDTO skuBaseDTO = skuManager.detail(skuSaleStockVO.getSkuId(), false);
                if(null != skuBaseDTO){
                    throw new BusinessException("第" + (i + 1) + "级门槛,赠品【" + skuBaseDTO.getSkuName() + "】库存不足！");
                }
            }
        }

        if(CollectionUtils.isNotEmpty(reduceRule.getCouponRangeList())){
            List<CouponRuleListVO> couponRuleList = couponRuleDao.selectCouponRemainingNum(StreamUtils.toList(reduceRule.getCouponRangeList(), x-> x.getId()));
            CouponRuleListVO couponRuleListVO = StreamUtils.findFirst(couponRuleList, x-> x.getRemainingNum() <= 0);
            if(null != couponRuleListVO){
                throw new BusinessException("第" + (i + 1) + "级门槛,优惠券【" + couponRuleListVO.getCouponName() + "】库存不足！");
            }
        }
    }

    private void verifyCount(int i, FullReduceRuleSaveDTO reduceRule) {
        if (null == reduceRule.getFullAmount() && null == reduceRule.getFullCount()) {
            throw new BusinessException("第" + (i + 1) + "级门槛,缺少门槛条件！");
        }
        if (CollectionUtils.isNotEmpty(reduceRule.getCouponRangeList()) && reduceRule.getCouponRangeList().size() > 3) {
            throw new BusinessException("第" + (i + 1) + "级门槛,优惠券张数超过3张！");
        }
        if (CollectionUtils.isNotEmpty(reduceRule.getGiftRangeList()) && reduceRule.getGiftRangeList().size() > 3) {
            throw new BusinessException("第" + (i + 1) + "级门槛,赠品数超过3个！");
        }
        if (null == reduceRule.getReduceAmount() && null == reduceRule.getDiscount()
                && CollectionUtils.isEmpty(reduceRule.getCouponRangeList()) && CollectionUtils.isEmpty(reduceRule.getGiftRangeList())) {
            throw new BusinessException("第" + (i + 1) + "级门槛, 减价/折扣/赠品/优惠券至少有1个！");
        }
    }

    private void verifyReduceRange(int i, FullReduceRuleSaveDTO reduceRule) {
        if(FullRuleReduceTypeEnum.REDUCE.getCode().equals(reduceRule.getReduceType())){
            if(reduceRule.getReduceAmount().compareTo(BigDecimal.valueOf(0.01)) < 0
                    || reduceRule.getReduceAmount().compareTo(BigDecimal.valueOf(99999)) > 0){
                throw new BusinessException("第" + (i + 1) + "级门槛, 减价不在0.01-99999元范围内！");
            }
        }else if(FullRuleReduceTypeEnum.DISCOUNT.getCode().equals(reduceRule.getReduceType())){
            if(reduceRule.getDiscount().compareTo(BigDecimal.valueOf(0.1)) < 0
                    || reduceRule.getDiscount().compareTo(BigDecimal.valueOf(9.9)) > 0){
                throw new BusinessException("第" + (i + 1) + "级门槛, 打折不在0.1-9.9折范围内！");
            }
        }
    }

    private void verifyPreRule(int i, FullReduceSaveDTO dto, FullReduceTypeEnum reduceTypeEnum, FullReduceRuleSaveDTO reduceRule) {
        if (FullReduceDiscountRuleEnum.STEP.getCode().equals(dto.getDiscountRule())) {
            if (i > 0) {
                FullReduceRuleSaveDTO preReduceRule = dto.getFullReduceRuleList().get(i - 1);
                switch (reduceTypeEnum) {
                    case FULL_COUNT:
                        if(null == reduceRule.getFullCount()){
                            throw new BusinessException("第" + (i + 1) + "级缺失门槛!");
                        }
                        if(preReduceRule.getFullCount() >= reduceRule.getFullCount()){
                            throw new BusinessException("第" + (i + 1) + "级门槛需比第" + i + "级门槛高!");
                        }
                        break;
                    case FULL_MONEY:
                    case FULL_REAL_AMOUNT:
                        if(null == reduceRule.getFullAmount()){
                            throw new BusinessException("第" + (i + 1) + "级缺失门槛!");
                        }
                        if(null != reduceRule.getReduceAmount()){
                            if(reduceRule.getReduceAmount().compareTo(reduceRule.getFullAmount()) >= 0 ){
                                throw new BusinessException("第" + (i + 1) + "级门槛, 减价必须小于门槛！");
                            }
                        }
                        if(preReduceRule.getFullAmount().compareTo(reduceRule.getFullAmount()) >= 0){
                            throw new BusinessException("第" + (i + 1) + "级门槛需比第" + i + "级门槛高!");
                        }
                        break;
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        FullReduce fullReduce = fullReduceManager.findById(id);
        if(null == fullReduce){
            throw new BusinessException("满减活动不存在");
        }
        //满减活动id
        fullReduceManager.deleteById(id);
        //优惠规则
        fullReduceRuleManager.deleteByReduceId(id);
        //优惠规则商品/赠品范围
        fullReduceRuleRangeManager.deleteByReduceId(id);
        //满减活动商品范围
        fullReduceProductRangeManager.deleteByReduceId(id);
        //清空redis
        fullReduceProductRangeManager.delRedisByFullId(id);
    }

    @Override
    public PromoVO promotion(Long id) {
        FullReduce fullReduce = fullReduceManager.findById(id);
        if(null == fullReduce){
            throw new BusinessException("该满减活动不存在！");
        }
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.FULL_REDUCE.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        PromoVO promoVO = new LuckyDrawPromoVO();
        promoVO.setId(id);

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    @Override
    public FullReduceDetailVO get(Long id) {
        FullReduce fullReduce = fullReduceManager.findById(id);
        if(null == fullReduce){
            throw new BusinessException("该满减活动不存在！");
        }
        //优惠规则
        List<FullReduceRule> reduceRuleList = fullReduceRuleManager.findByFullId(id);
        //优惠规则赠品/优惠券
        List<FullReduceRuleRange> ruleRangeList = fullReduceRuleRangeManager.findByFullReduceId(id);
        //赠品
        List<FullReduceRuleRange> giftRangeList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
        Map<Long, List<FullReduceRuleRange>> ruleGiftMap = StreamUtils.group(giftRangeList, FullReduceRuleRange::getFullReduceRuleId);

        List<Long> giftSkuIds = StreamUtils.toList(giftRangeList, FullReduceRuleRange::getTargetId);
        List<Sku> skuList = skuManager.selectByIds(giftSkuIds);
        Map<Long, Sku> skuMap = StreamUtils.toMap(skuList, Sku::getId);

        //优惠券
        List<FullReduceRuleRange> couponRangeList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));
        Map<Long, List<FullReduceRuleRange>> ruleCouponMap = StreamUtils.group(couponRangeList, FullReduceRuleRange::getFullReduceRuleId);

        List<Long> couponRuleIds = StreamUtils.toList(couponRangeList, FullReduceRuleRange::getTargetId);
        List<CouponRule> couponRuleList = couponRuleManager.findByIds(couponRuleIds);
        Map<Long, CouponRule> couponMap = StreamUtils.toMap(couponRuleList, CouponRule::getId);

        //组装优惠规则的赠品/优惠券
        List<FullReduceRuleVO> fullReduceRuleList = this.buildRuleVO(reduceRuleList, ruleGiftMap, skuMap, ruleCouponMap, couponMap);

        //商品范围
        List<FullReduceProductRange> fullReduceProductRangeList = fullReduceProductRangeManager.findByFullReduceId(id);
        List<Long> spuIds = StreamUtils.toList(fullReduceProductRangeList, FullReduceProductRange::getSpuId);
        List<ProductBaseDTO> productList = productManager.detailList(spuIds);
        FullReduceDetailVO fullReduceDetailVO = FullReduceUtil.buildDetailVO(fullReduce, productList, fullReduceRuleList);
        return fullReduceDetailVO;
    }

    private List<FullReduceRuleVO> buildRuleVO(List<FullReduceRule> reduceRuleList,
                             Map<Long, List<FullReduceRuleRange>> ruleGiftMap,
                             Map<Long, Sku> skuMap,
                             Map<Long, List<FullReduceRuleRange>> ruleCouponMap,
                             Map<Long, CouponRule> couponMap) {
        List<FullReduceRuleVO> reduceRuleVOList = new ArrayList<>();
        for (FullReduceRule fullReduceRule: reduceRuleList) {
            FullReduceRuleVO reduceRuleVO = new FullReduceRuleVO();
            reduceRuleVO.setId(fullReduceRule.getId());
            reduceRuleVO.setFullReduceId(fullReduceRule.getFullReduceId());
            reduceRuleVO.setFullAmount(fullReduceRule.getFullAmount());
            reduceRuleVO.setFullCount(fullReduceRule.getFullCount());
            reduceRuleVO.setReduceType(fullReduceRule.getReduceType());
            reduceRuleVO.setReduceAmount(fullReduceRule.getReduceAmount());
            reduceRuleVO.setDiscount(fullReduceRule.getDiscount());
            reduceRuleVO.setLevel(fullReduceRule.getLevel());

            List<FullReduceRuleRange> giftRangeList = ruleGiftMap.get(fullReduceRule.getId());
            List<FullReduceRuleRange> couponList = ruleCouponMap.get(fullReduceRule.getId());

            List<FullReduceRuleRangeVO> giftRangeVOList = StreamUtils.convert(giftRangeList, data ->{
                FullReduceRuleRangeVO reduceRuleRangeVO = new FullReduceRuleRangeVO();
                Sku sku = skuMap.get(data.getTargetId());
                if(null != sku){
                    reduceRuleRangeVO.setTargetId(sku.getId());
                    reduceRuleRangeVO.setTargetName(sku.getSkuName());
                }
                return reduceRuleRangeVO;
            });

            List<FullReduceRuleRangeVO> couponRangeVOList = StreamUtils.convert(couponList, data ->{
                FullReduceRuleRangeVO reduceRuleRangeVO = new FullReduceRuleRangeVO();
                CouponRule couponRule = couponMap.get(data.getTargetId());
                if(null != couponRule){
                    reduceRuleRangeVO.setTargetId(couponRule.getId());
                    reduceRuleRangeVO.setTargetName(couponRule.getCouponName());
                    reduceRuleRangeVO.setCouponType(couponRule.getCouponType());
                }
                return reduceRuleRangeVO;
            });
            reduceRuleVO.setCouponRangeList(couponRangeVOList);
            reduceRuleVO.setGiftRangeList(giftRangeVOList);
            reduceRuleVOList.add(reduceRuleVO);
        }
        return reduceRuleVOList;
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet("满减满送").head(FullReduceExportVO.class).build();
        FullReduceSearchDTO searchDTO = new FullReduceSearchDTO();
        searchDTO.setStatus(dto.getStatus());
        searchDTO.setNameFuzzy(dto.getNameFuzzy());
        searchDTO.setType(dto.getType());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            //活动信息分页
            PageHelper.startPage(pageNum, pageSize);
            List<FullReduce> fullReduceList = fullReduceManager.findByCondition(searchDTO);
            Pagination pagination = PageUtils.extract(fullReduceList);
            pages = pagination.getPages();
            pageNum ++;
            //活动规则
            List<Long> ids = StreamUtils.toList(fullReduceList, FullReduce::getId);
            List<FullReduceRule> fullReduceRuleList = fullReduceRuleManager.findByFullIds(ids);
            Map<Long, List<FullReduceRule>> fullReduceRuleMap = StreamUtils.group(fullReduceRuleList, FullReduceRule::getFullReduceId);
            //商品范围
            List<FullReduceRuleRange> reduceRuleRangeList = fullReduceRuleRangeManager.findByFullReduceIds(ids);
            Map<Long, List<FullReduceRuleRange>> ruleRangeMap = StreamUtils.group(reduceRuleRangeList, FullReduceRuleRange::getFullReduceRuleId);
            List<FullReduceExportVO> exportVOList = buildExportVO(fullReduceList, fullReduceRuleMap, ruleRangeMap);
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    private List<FullReduceExportVO> buildExportVO(List<FullReduce> fullReduceList, Map<Long, List<FullReduceRule>> fullReduceRuleMap, Map<Long, List<FullReduceRuleRange>> ruleRangeMap) {
        return StreamUtils.convert(fullReduceList, data ->{
            FullReduceExportVO fullReduceVO = FullReduceUtil.convert2ExportVO(data);
            List<FullReduceRule> reduceRuleList = fullReduceRuleMap.get(data.getId());
            StringBuilder sb = new StringBuilder();
            if(CollectionUtils.isNotEmpty(reduceRuleList)){
                int i = 0;
                for (FullReduceRule reduceRule: reduceRuleList) {
                    i ++;
                    List<FullReduceRuleRange> ruleRangeList = ruleRangeMap.get(reduceRule.getId());
                    List<FullReduceRuleRange> giftRangeList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
                    List<FullReduceRuleRange> couponRangeList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));
                    if(FullReduceDiscountRuleEnum.LOOP.getCode().equals(data.getDiscountRule())){
                        sb.append("每满");
                    }else if(FullReduceDiscountRuleEnum.STEP.getCode().equals(data.getDiscountRule())){
                        sb.append("满");
                    }
                    if(FullReduceTypeEnum.FULL_COUNT.getCode().equals(data.getType())){
                        sb.append(reduceRule.getFullCount()).append("件");
                    }else{
                        sb.append(reduceRule.getFullAmount());
                    }
                    if(FullRuleReduceTypeEnum.REDUCE.getCode().equals(reduceRule.getReduceType())){
                        sb.append("减").append(reduceRule.getReduceAmount());
                    }else if(FullRuleReduceTypeEnum.DISCOUNT.getCode().equals(reduceRule.getReduceType())){
                        sb.append("打").append(reduceRule.getDiscount()).append("折");
                    }
                    if(CollectionUtils.isNotEmpty(giftRangeList)){
                        sb.append("送1件赠品");
                    }
                    if(CollectionUtils.isNotEmpty(couponRangeList)){
                        // 根据couponType区分优惠券和赠品券
                        long couponCount = couponRangeList.stream()
                                .filter(range -> FullRuleRangeCouponTypeEnum.COUNPON.getCode().equals(range.getCouponType()))
                                .count();
                        long giftCouponCount = couponRangeList.stream()
                                .filter(range -> FullRuleRangeCouponTypeEnum.GIFT_COUNPON.getCode().equals(range.getCouponType()))
                                .count();
                        
                        if (couponCount > 0) {
                            sb.append("送").append(couponCount).append("张优惠券");
                        }
                        if (giftCouponCount > 0) {
                            if (couponCount > 0) {
                                sb.append("、");
                            }
                            sb.append("送").append(giftCouponCount).append("张赠品券");
                        }
                    }
                    if(i < reduceRuleList.size()){
                        sb.append("\n");
                    }
                }
            }
            fullReduceVO.setContent(sb.toString());
            return fullReduceVO;
        });
    }

    @Override
    public void setFullReduceStatus(Long id, FullReduceStatusEnum statusEnum) {
        FullReduce fullReduce = fullReduceManager.findById(id);
        if(null == fullReduce){
            throw new BusinessException("该满减活动不存在！");
        }
        FullReduce reduce = new FullReduce();
        reduce.setId(id);
        if(statusEnum.equals(FullReduceStatusEnum.IN_PROGRESS)){
            if(!FullReduceStatusEnum.NOT_STARTED.getCode().equals(fullReduce.getStatus())){
                throw new BusinessException("该活动不处于未上线状态不允许开始，请勿操作！");
            }
            reduce.setStatus(FullReduceStatusEnum.IN_PROGRESS.getCode());
            reduce.setStartTime(new Date());
            //校验商品区间
            FullReduceSaveDTO dto = new FullReduceSaveDTO();
            dto.setStartTime(new Date());
            dto.setEndTime(fullReduce.getEndTime());
            dto.setId(fullReduce.getId());
            List<FullReduceProductRange> productRangeList = fullReduceProductRangeDao.findByFullReduceId(fullReduce.getId());
            List<Long> ids = StreamUtils.toList(productRangeList, FullReduceProductRange::getSpuId);
            dto.setFullReduceProductRangeList(ids);
            verifyProduct(dto);
            //更新活动
            fullReduceManager.updateById(reduce);
            //新增redis
            fullReduceProductRangeManager.addRedisByFullId(id);
        }else if(statusEnum.equals(FullReduceStatusEnum.END)){
            if(!FullReduceStatusEnum.IN_PROGRESS.getCode().equals(fullReduce.getStatus())){
                throw new BusinessException("该活动不处于进行中状态不允许结束，请勿操作！");
            }
            reduce.setStatus(FullReduceStatusEnum.END.getCode());
            reduce.setEndTime(new Date());
            //更新活动
            fullReduceManager.updateById(reduce);
            //清空redis
            fullReduceProductRangeManager.delRedisByFullId(id);
        }
    }
}
