package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialService;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "试用活动B端")
@RequestMapping("/freeTrial")
public class FreeTrialController {

    @Autowired
    private FreeTrialService freeTrialService;

    @ApiOperation("分页列表")
    @PostMapping("/getList")
    public Response<PageVO<FreeTrialVO>> getList(@RequestBody FreeTrialDTO dto) {
        PageVO<FreeTrialVO> list = freeTrialService.getList(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation("待审核分页列表")
    @PostMapping("/getWaitList")
    public Response<PageVO<FreeTrialWaitVO>> getWaitList(@RequestBody FreeTrialWaitDTO dto) {
        PageVO<FreeTrialWaitVO> list = freeTrialService.getWaitList(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Object> save(@RequestBody @Validated FreeTrialDTO dto) {
        freeTrialService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<FreeTrialVO> get(@RequestParam Long id) {
        return ResponseFactory.success(freeTrialService.get(id));
    }

    @ApiOperation("待审核详情")
    @GetMapping("/getWait")
    public Response<FreeTrialVoteDetailVO> getWait(@RequestParam Long freeTrialVoteId, @RequestParam Long freeTrialVoteFreeProductId) {
        return ResponseFactory.success(freeTrialService.getWait(freeTrialVoteId, freeTrialVoteFreeProductId));
    }

    @ApiOperation("统计数量")
    @GetMapping("/getCnt")
    public Response<FreeTrialCntVO> getCnt() {
        return ResponseFactory.success(freeTrialService.getCnt());
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        return ResponseFactory.success(freeTrialService.promotion(id));
    }

    @ApiOperation("开始活动")
    @GetMapping("/start")
    public Response<Object> start(@RequestParam Long id) {
        freeTrialService.start(id);
        return ResponseFactory.success();
    }

    @ApiOperation("结束活动")
    @GetMapping("/end")
    public Response<Object> end(@RequestParam Long id) {
        freeTrialService.end(id);
        return ResponseFactory.success();
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Void> delete(@RequestParam Long id) {
        freeTrialService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "/export")
    public Response<Object> export(@RequestBody FreeTrialDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        dto.setExportType(FileExportCenterEnum.free_trial.getCode());
        // dto.setExportFlag(true);
        freeTrialService.export(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "活动规则-保存")
    @PostMapping("/config/save")
    public Response<Void> saveConfig(@RequestBody @Validated FreeTrialConfigDTO dto) {
        freeTrialService.saveConfig(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "活动规则-详情")
    @GetMapping("/config/get")
    public Response<FreeTrialConfigVO> getConfig() {
        return ResponseFactory.success(freeTrialService.getConfig());
    }

    @ApiOperation(value = "查询活动订阅次数")
    @GetMapping("/subscribeCnt")
    public Response<FreeSubscribeCntVO> subscribeCnt() {
        FreeSubscribeCntVO vo = freeTrialService.subscribeCnt();
        return ResponseFactory.success(vo);
    }

    @ApiOperation(value = "获取统计数据")
    @GetMapping("/stats/get")
    public Response<FreeTrialStatsVO> statsGet(Long freeTrialId) {
        FreeTrialStatsVO vo = freeTrialService.statsGet(freeTrialId);
        return ResponseFactory.success(vo);
    }

    @ApiOperation(value = "获取统计条目数据")
    @PostMapping("/statsItem/getList")
    public Response<PageVO<FreeTrialStatsItemVO>> statsItemGetList(@RequestBody FreeTrialStatsItemDTO dto) {
        PageVO<FreeTrialStatsItemVO> result = freeTrialService.statsItemGetList(dto);
        return ResponseFactory.success(result);
    }

    @ApiOperation("审核")
    @PostMapping("/audit")
    public Response<Void> audit(@RequestBody FreeTrialAuditDTO dto) {
        freeTrialService.audit(dto);
        return ResponseFactory.success();
    }

}
