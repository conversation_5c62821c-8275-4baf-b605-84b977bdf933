package com.hengtiansoft.operation.freeTrial.service;


import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialFissionUserCntVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.user.entity.dto.UserQueryDTO;

public interface FreeTrialFissionUserService {


    PageVO<FreeTrialUserVO> page(UserQueryDTO dto);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    FreeTrialFissionUserCntVO cnt();
}
