package com.hengtiansoft.operation.freeTrial.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialCommentService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.privilege.dao.FreeTrialPrizeDao;
import com.hengtiansoft.privilege.entity.dto.FreeTrialCommentPrizeDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialCommentSearchDTO;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.FreeTrialCommentExportVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialCommentHotExportVO;
import com.hengtiansoft.operation.freeTrial.entity.FreeTrialCommentPrizeVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialCommentVO;
import com.hengtiansoft.privilege.enums.FreeProductTypeEnum;
import com.hengtiansoft.privilege.enums.FreeTrialPrizeTypeEnum;
import com.hengtiansoft.privilege.enums.FreeTrialPrizeUseTypeEnum;
import com.hengtiansoft.privilege.manager.FreeTrialCommentManager;
import com.hengtiansoft.privilege.manager.FreeTrialManager;
import com.hengtiansoft.privilege.manager.FreeTrialProductManager;
import com.hengtiansoft.privilege.manager.FreeTrialUserManager;
import com.hengtiansoft.privilege.util.FreeTrialCommentUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.entity.po.CustomerUser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.github.pagehelper.page.PageMethod.startPage;


@Service
public class FreeTrialCommentServiceImpl implements FreeTrialCommentService {

    @Resource
    private FreeTrialCommentManager freeTrialCommentManager;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private FreeTrialUserManager freeTrialUserManager;
    @Resource
    private FreeTrialManager freeTrialManager;
    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private OrderManager orderManager;
    @Resource
    private FreeTrialPrizeDao freeTrialPrizeDao;
    @Resource
    private CouponRuleManager couponRuleManager;

    @Override
    public PageVO<FreeTrialCommentVO> page(FreeTrialCommentSearchDTO dto) {
        Assert.notNull(dto.getFreeTrialId(), "活动id参数缺失!");
        return getFreeTrialCommentVOPage(dto);
    }

    private PageVO<FreeTrialCommentVO> getFreeTrialCommentVOPage(FreeTrialCommentSearchDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialComment> freeTrialCommentList = freeTrialCommentManager.findByConditionSql(dto);
        if (CollectionUtils.isEmpty(freeTrialCommentList)) {
            return PageUtils.emptyPage(dto);
        }

        List<Long> freeTrialUserIds = StreamUtils.toList(freeTrialCommentList, FreeTrialComment::getFreeTrialUserId);
        List<FreeTrialUser> freeTrialUserList = freeTrialUserManager.findByIds(freeTrialUserIds);
        Map<Long, FreeTrialUser> freeTrialUserMap = StreamUtils.toMap(freeTrialUserList, FreeTrialUser::getId);

        List<Long> userIds = StreamUtils.toList(freeTrialCommentList, FreeTrialComment::getUserId);
        List<Long> freeTrialIds = new ArrayList<>();
        if(Objects.isNull(dto.getFreeTrialId())){
            freeTrialIds = StreamUtils.toList(freeTrialCommentList, FreeTrialComment::getFreeTrialId);
        }else{
            freeTrialIds = Collections.singletonList(dto.getFreeTrialId());
        }
        List<FreeTrial> freeTrialList = freeTrialManager.findByIds(freeTrialIds);
        Map<Long, FreeTrial> freeTrialMap = StreamUtils.toMap(freeTrialList, FreeTrial::getId);

        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(userIds, freeTrialIds);
        Map<Long, List<OrderInfo>> freeTrialOrderMap = StreamUtils.group(freeTrialOrderList, OrderInfo::getUserId);

        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIdsWithInfo(freeTrialIds, FreeProductTypeEnum.FREE.getCode());
        Map<Long, List<String>> productNameMap = StreamUtils.group(freeTrialProductList, FreeTrialProduct::getFreeTrialId , FreeTrialProduct::getProductName);

        return PageUtils.convert(freeTrialCommentList, data -> {
            FreeTrialCommentVO commentVO = FreeTrialCommentUtil.convert2VO(data);
            FreeTrialUser freeTrialUser = freeTrialUserMap.get(data.getFreeTrialUserId());
            if (null != freeTrialUser) {
                commentVO.setApplyTime(freeTrialUser.getApplyTime());
                commentVO.setDrawTime(freeTrialUser.getDrawTime());
            }
            FreeTrial freeTrial = freeTrialMap.get(data.getFreeTrialId());
            if(null != freeTrial){
                commentVO.setFreeTrialName(freeTrial.getName());
            }
            List<String> productNames = productNameMap.get(data.getFreeTrialId());
            commentVO.setFreeProductName(String.join("、", productNames));
            commentVO.setGmvBindProduct(BigDecimal.ZERO);
            commentVO.setOrderBindProduct(0);
            List<OrderInfo> orderInfoList = freeTrialOrderMap.get(data.getUserId());
            if (CollectionUtils.isNotEmpty(orderInfoList)) {
                BigDecimal gmvBindProduct = orderInfoList.stream()
                        .map(OrderInfo::getRealAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer orderBindProduct = orderInfoList.size();
                commentVO.setGmvBindProduct(gmvBindProduct);
                commentVO.setOrderBindProduct(orderBindProduct);
            }
            freeTrialCommentManager.buildCommentCnt(commentVO, null);
            return commentVO;
        });
    }

    @Override
    public FreeTrialCommentVO get(Long id) {
        FreeTrialComment comment = freeTrialCommentManager.get(id);
        Assert.notNull(comment, "活动不存在!");
        CustomerUser user = customerUserDao.findById(comment.getUserId());
        FreeTrialCommentVO commentVO = FreeTrialCommentUtil.convert2VO(comment);
        if(null != user){
            commentVO.setNickName(user.getOrderNickName());
            commentVO.setGrade(user.getGrade());
        }
        FreeTrial freeTrial = freeTrialManager.findById(comment.getFreeTrialId());
        if(null != freeTrial){
            commentVO.setFreeTrialName(freeTrial.getName());
        }
        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIdsWithInfo(Arrays.asList(freeTrial.getId()), FreeProductTypeEnum.FREE.getCode());
        if(CollectionUtils.isNotEmpty(freeTrialProductList)){
            List<String> productNames = StreamUtils.toList(freeTrialProductList, FreeTrialProduct::getProductName);
            commentVO.setFreeProductName(String.join("、", productNames));
        }
        FreeTrialUser freeTrialUser = freeTrialUserManager.findById(comment.getFreeTrialUserId());
        if(null != freeTrialUser){
            commentVO.setOrderNo(freeTrialUser.getOrderNo());
        }
        freeTrialCommentManager.buildCommentCnt(commentVO, null);
        return commentVO;
    }

    @Override
    public void cancel(Long id) {
        FreeTrialComment comment = freeTrialCommentManager.get(id);
        Assert.notNull(comment, "测评不存在!");
        if(BasicFlagEnum.NO.getKey().equals(comment.getTop())){
            throw new BusinessException("当前测评没有置顶，无需取消置顶!");
        }
        FreeTrialComment update = new FreeTrialComment();
        update.setId(id);
        update.setTop(BasicFlagEnum.NO.getKey());
        freeTrialCommentManager.update(update);
    }

    @Override
    public void top(Long id) {
        FreeTrialComment comment = freeTrialCommentManager.get(id);
        Assert.notNull(comment, "测评不存在!");
        if(BasicFlagEnum.YES.getKey().equals(comment.getTop())){
            throw new BusinessException("当前测评已置顶，无需置顶!");
        }
        FreeTrialCommentSearchDTO dto = new FreeTrialCommentSearchDTO();
        dto.setTop(BasicFlagEnum.YES.getKey());
        dto.setFreeTrialId(comment.getFreeTrialId());
        FreeTrialComment topComment = StreamUtils.getFirst(freeTrialCommentManager.findByCondition(dto));
        if(null != topComment){
            throw new BusinessException("已存在置顶测评,id:" + topComment.getId());
        }
        FreeTrialComment update = new FreeTrialComment();
        update.setId(id);
        update.setTop(BasicFlagEnum.YES.getKey());
        freeTrialCommentManager.update(update);
    }

    @Override
    public void show(Long id) {
        FreeTrialComment comment = freeTrialCommentManager.get(id);
        Assert.notNull(comment, "测评不存在!");
        if(BasicFlagEnum.NO.getKey().equals(comment.getHide())){
            throw new BusinessException("当前测评已展示，无需展示!");
        }
        FreeTrialComment update = new FreeTrialComment();
        update.setId(id);
        update.setHide(BasicFlagEnum.NO.getKey());
        freeTrialCommentManager.update(update);
    }

    @Override
    public void hide(Long id) {
        FreeTrialComment comment = freeTrialCommentManager.get(id);
        Assert.notNull(comment, "活动不存在!");
        if(BasicFlagEnum.YES.getKey().equals(comment.getHide())){
            throw new BusinessException("当前测评已隐藏，无需隐藏!");
        }
        FreeTrialComment update = new FreeTrialComment();
        update.setId(id);
        update.setHide(BasicFlagEnum.YES.getKey());
        freeTrialCommentManager.update(update);
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialCommentExportVO.class).build();
        FreeTrialCommentSearchDTO searchDTO = new FreeTrialCommentSearchDTO();
        searchDTO.setUserId(dto.getUserId());
        searchDTO.setNickName(dto.getNickName());
        searchDTO.setPhone(dto.getPhone());
        searchDTO.setFreeTrialId(dto.getFreeTrialId());
        searchDTO.setId(dto.getFreeTrialCommentId());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            //活动信息分页
            searchDTO.setPageNum(pageNum);
            searchDTO.setPageSize(pageSize);
            PageVO<FreeTrialCommentVO> commentPageVO = this.page(searchDTO);
            Pagination pagination = commentPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTrialCommentExportVO> exportVOList = FreeTrialCommentUtil.convert2ExportVO(commentPageVO.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public void exportHot(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialCommentHotExportVO.class).build();
        FreeTrialCommentSearchDTO searchDTO = new FreeTrialCommentSearchDTO();
        searchDTO.setUserId(dto.getUserId());
        searchDTO.setFreeTrialName(dto.getFreeTrialName());
        searchDTO.setPhone(dto.getPhone());
        searchDTO.setFreeTrialId(dto.getFreeTrialId());
        searchDTO.setId(dto.getRecordId());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            //活动信息分页
            searchDTO.setPageNum(pageNum);
            searchDTO.setPageSize(pageSize);
            PageVO<FreeTrialCommentVO> commentPageVO = this.hot(searchDTO);
            Pagination pagination = commentPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTrialCommentHotExportVO> exportVOList = FreeTrialCommentUtil.convert2HotExportVO(commentPageVO.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public PageVO<FreeTrialCommentVO> hot(FreeTrialCommentSearchDTO dto) {
        dto.setSort("ftc.weight desc");
        return getFreeTrialCommentVOPage(dto);
    }

    @Override
    public void prizeSave(FreeTrialCommentPrizeDTO dto) {
        UserDetailDTO detailDTO = Objects.requireNonNull(UserUtil.getDetails(),"登录后重试");
        this.validatePrizeSettings(dto);
        FreeTrialPrize freeTrialPrize = freeTrialPrizeDao.getHotCommentPrize();
        String rule = JSON.toJSONString(dto);
        if(Objects.isNull(freeTrialPrize)){
            FreeTrialPrize insertPo = new FreeTrialPrize();
            insertPo.setType(FreeTrialPrizeUseTypeEnum.HOT_COMMENT_PRIZE.getCode());
            insertPo.setOperator(detailDTO.getUserName());
            insertPo.setRule(rule);
            insertPo.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            freeTrialPrizeDao.insert(insertPo);
        }else{
            FreeTrialPrize updatePo = new FreeTrialPrize();
            updatePo.setId(freeTrialPrize.getId());
            updatePo.setOperator(detailDTO.getUserName());
            updatePo.setRule(rule);
            freeTrialPrizeDao.update(updatePo);
        }
    }

    @Override
    public FreeTrialCommentPrizeVO prizeGet() {
        FreeTrialPrize freeTrialPrize = freeTrialPrizeDao.getHotCommentPrize();
        if(null == freeTrialPrize || StringUtils.isBlank(freeTrialPrize.getRule())){
            return new FreeTrialCommentPrizeVO();
        }

        FreeTrialCommentPrizeVO vo = JSON.parseObject(freeTrialPrize.getRule(), FreeTrialCommentPrizeVO.class);
        List<CouponRule> couponRuleList = couponRuleManager.findByIds(vo.getCouponRuleIds());
        List<FreeTrialCommentPrizeVO.CouponRule> couponRuleListVOList = StreamUtils.convert(couponRuleList, this::convert2CouponRuleVO);
        vo.setCouponRuleList(couponRuleListVOList);
        return vo;
    }


    private FreeTrialCommentPrizeVO.CouponRule convert2CouponRuleVO(CouponRule couponRule) {
        FreeTrialCommentPrizeVO.CouponRule vo = new FreeTrialCommentPrizeVO.CouponRule();
        vo.setId(couponRule.getId());
        vo.setCouponName(couponRule.getCouponName());
        vo.setChannel(couponRule.getChannel());
        vo.setIsPublic(couponRule.getIsPublic());
        vo.setStartTime(couponRule.getStartTime());
        vo.setEndTime(couponRule.getEndTime());
        vo.setStatus(couponRule.getStatus());
        vo.setOnlineTime(couponRule.getOnlineTime());
        vo.setOfflineTime(couponRule.getOfflineTime());
        vo.setAmountFull(couponRule.getAmountFull());
        vo.setAmountReduce(couponRule.getAmountReduce());
        vo.setAmountLimit(couponRule.getAmountLimit());
        vo.setCouponWay(couponRule.getCouponWay());
        vo.setDiscount(couponRule.getDiscount());
        return vo;
    }


    private void validatePrizeSettings(FreeTrialCommentPrizeDTO dto) {
        if(Objects.equals(dto.getType(), FreeTrialPrizeTypeEnum.COUPON.getCode())){
            this.checkCouponSettings(dto);
        }else if(Objects.equals(dto.getType(), FreeTrialPrizeTypeEnum.POINT.getCode())){
            this.checkPointSettings(dto);
        }else{
            this.checkPointSettings(dto);
            this.checkCouponSettings(dto);
        }
    }

    private void checkPointSettings(FreeTrialCommentPrizeDTO dto) {
        if (Objects.isNull(dto.getPoint()) || dto.getPoint() <= 0) {
            throw new BusinessException("请配置奖励积分!");
        }
        if (Objects.isNull(dto.getPointThreshold())) {
            throw new BusinessException("请设置积分奖励门槛!");
        }
    }

    private void checkCouponSettings(FreeTrialCommentPrizeDTO dto) {
        if (CollectionUtils.isEmpty(dto.getCouponRuleIds())) {
            throw new BusinessException("请选择优惠券!");
        }
        if (Objects.isNull(dto.getCouponThreshold())) {
            throw new BusinessException("请设置优惠券奖励门槛!");
        }
    }




}
