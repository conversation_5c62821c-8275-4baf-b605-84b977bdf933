package com.hengtiansoft.operation.freeTrial.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.UserGradeEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialHelpService;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.privilege.dao.UserShareDao;
import com.hengtiansoft.privilege.entity.po.FreeTrialProduct;
import com.hengtiansoft.privilege.entity.vo.FreeTrialHelpUserExcelVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.privilege.enums.FreeProductTypeEnum;
import com.hengtiansoft.privilege.manager.FreeTrialProductManager;
import com.hengtiansoft.user.entity.dto.UserQueryDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.github.pagehelper.page.PageMethod.startPage;


@Service
public class FreeTrialHelpServiceImpl implements FreeTrialHelpService {

    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private UserShareDao userShareDao;
    @Resource
    private OrderManager orderManager;


    @Override
    public PageVO<FreeTrialUserVO> page(UserQueryDTO dto) {
        Long freeTrialId = dto.getFreeTrialId();
        Assert.notNull(freeTrialId, "活动id参数缺失!");
        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIdsWithInfo(Lists.newArrayList(freeTrialId),
                FreeProductTypeEnum.FREE.getCode());
        FreeTrialProduct freeTrialProduct = StreamUtils.getFirst(freeTrialProductList);

        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialUserVO> userList = userShareDao.selectHelpUserList(freeTrialId, dto.getNickName(), dto.getUserId(), dto.getPhone());
        if (CollectionUtils.isEmpty(userList)) {
            return PageUtils.emptyPage(dto);
        }

        List<Long> userIds = StreamUtils.toList(userList, FreeTrialUserVO::getUserId);
        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(userIds, freeTrialId);
        Map<Long, List<OrderInfo>> freeTrialOrderMap = StreamUtils.group(freeTrialOrderList, OrderInfo::getUserId);

        return PageUtils.convert(userList, data ->{
            FreeTrialUserVO vo = new FreeTrialUserVO();
            vo.setId(data.getId());
            vo.setUserId(data.getUserId());
            vo.setPhone(data.getPhone());
            vo.setNickName(data.getNickName());
            vo.setGrade(data.getGrade());
            vo.setGradeStr(UserGradeEnum.getDescByCode(data.getGrade()));
            vo.setHelpTime(data.getHelpTime());
            vo.setFreeTrialId(freeTrialId);
            vo.setRegisterTime(data.getRegisterTime());

            if(null != freeTrialProduct){
                vo.setProductName(freeTrialProduct.getProductName());
            }
            vo.setGmvBindProduct(BigDecimal.ZERO);
            vo.setOrderBindProduct(0);
            List<OrderInfo> orderInfoList = freeTrialOrderMap.get(data.getUserId());
            if(CollectionUtils.isNotEmpty(orderInfoList)){
                BigDecimal gmvBindProduct = orderInfoList.stream()
                        .map(OrderInfo::getRealAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer orderBindProduct = orderInfoList.size();
                vo.setGmvBindProduct(gmvBindProduct);
                vo.setOrderBindProduct(orderBindProduct);
            }

            return vo;
        });
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialHelpUserExcelVO.class).build();
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setFreeTrialId(dto.getFreeTrialId());
        userQueryDTO.setUserId(dto.getUserId());
        userQueryDTO.setPhone(dto.getPhone());
        userQueryDTO.setNickName(dto.getNickName());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {

            userQueryDTO.setPageNum(pageNum);
            userQueryDTO.setPageSize(pageSize);

            PageVO<FreeTrialUserVO> helpUserPageVO = this.page(userQueryDTO);
            Pagination pagination = helpUserPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            excelWriter.write(BeanUtils.copyList(helpUserPageVO.getList(), FreeTrialHelpUserExcelVO::new), writeSheet);
        }while (pageNum <= pages);
    }
}
