package com.hengtiansoft.operation.freeTrial.service;


import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.user.entity.dto.UserQueryDTO;

public interface FreeTrialHelpService {


    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    PageVO<FreeTrialUserVO> page(UserQueryDTO dto);
}
