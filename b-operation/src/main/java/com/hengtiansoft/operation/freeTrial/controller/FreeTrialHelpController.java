package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialHelpService;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.user.entity.dto.UserQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "零元试用助力用户")
@RequestMapping("/freeTrial/help")
public class FreeTrialHelpController {

    @Autowired
    private FreeTrialHelpService freeTrialHelpService;

    @ApiOperation("分页")
    @PostMapping("/page")
    public Response<PageVO<FreeTrialUserVO>> page(@RequestBody UserQueryDTO dto) {
        PageVO<FreeTrialUserVO> list = freeTrialHelpService.page(dto);
        return ResponseFactory.success(list);
    }
}
