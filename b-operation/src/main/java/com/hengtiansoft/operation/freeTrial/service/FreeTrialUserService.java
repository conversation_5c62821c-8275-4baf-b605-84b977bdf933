package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.fastjson.JSONObject;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.privilege.entity.dto.FreeTrialUserDTO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface FreeTrialUserService {

    PageVO<FreeTrialUserVO> getList(FreeTrialUserDTO dto);

    @Transactional(rollbackFor = Exception.class)
    List<MutablePair<CustomerUser, OrderInfo>> doManual(FreeTrialUserDTO dto);

    FreeTrialUserVO get(Long id);

    void export(FreeTrialUserDTO dto);

    void export(FreeTrialUserDTO dto, DataExportTask dataExportTask);

    PageVO<FreeTrialUserVO> subscribeGetList(FreeTrialUserDTO dto);

    PageVO<FreeTrialUserVO> subscribeBackGetList(FreeTrialUserDTO dto);

    JSONObject getManualCnt(Long freeTrialId);

    void manual(FreeTrialUserDTO dto);

}
