package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.vo.*;

public interface FreeTasteService {

    PageVO<FreeTasteListVO> getList(FreeTasteQueryDTO dto);

    void save(FreeTasteSaveDTO dto);

    void exportWaitList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    FreeTasteDetailVO get(Long id);

    PromoVO promotion(Long id);
}
