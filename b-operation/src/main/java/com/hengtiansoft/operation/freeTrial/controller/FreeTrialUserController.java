package com.hengtiansoft.operation.freeTrial.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialUserService;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.privilege.entity.dto.FreeTrialUserDTO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.thirdpart.enumeration.MessageSubscribeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "试用活动用户信息B端")
@RequestMapping("/freeTrialUser")
public class FreeTrialUserController {

    @Autowired
    private FreeTrialUserService freeTrialUserService;

    @ApiOperation("活动用户列表")
    @PostMapping("/getList")
    public Response<PageVO<FreeTrialUserVO>> getList(@RequestBody FreeTrialUserDTO dto) {
        PageVO<FreeTrialUserVO> list = freeTrialUserService.getList(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation("活动用户详情")
    @GetMapping("/get")
    public Response<FreeTrialUserVO> get(@RequestParam Long id) {
        return ResponseFactory.success(freeTrialUserService.get(id));
    }

    @ApiOperation(value = "活动用户导出")
    @PostMapping(value = "/export")
    public Response<Object> export(@RequestBody FreeTrialUserDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        // dto.setExportType(FileExportCenterEnum.free_trial_apply_user.getCode());
        // dto.setExportFlag(true);
        freeTrialUserService.export(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("订阅用户列表")
    @PostMapping("/subscribe/getList")
    public Response<PageVO<FreeTrialUserVO>> subscribeGetList(@RequestBody FreeTrialUserDTO dto) {
        Assert.notNull(dto.getFreeTrialId(), "试用活动id不能为空");
        dto.setMsgType(MessageSubscribeEnum.FREE_START_NOTICE.getCode());
        PageVO<FreeTrialUserVO> list = freeTrialUserService.subscribeGetList(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation("订阅用户列表")
    @PostMapping("/subscribeBack/getList")
    public Response<PageVO<FreeTrialUserVO>> subscribeBackGetList(@RequestBody FreeTrialUserDTO dto) {
        Assert.notNull(dto.getFreeTrialId(), "试用活动id不能为空");
        dto.setMsgType(MessageSubscribeEnum.FREE_BACK_NOTICE.getCode());
        PageVO<FreeTrialUserVO> list = freeTrialUserService.subscribeBackGetList(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation(value = "订阅用户列表导出")
    @PostMapping(value = "/subscribe/export")
    public Response<Object> subscribeExport(@RequestBody FreeTrialUserDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        // dto.setExportType(FileExportCenterEnum.free_trial_apply_user.getCode());
        // dto.setExportFlag(true);
        freeTrialUserService.export(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "返场订阅用户导出")
    @PostMapping(value = "/subscribeBack/export")
    public Response<Object> subscribeBackExport(@RequestBody FreeTrialUserDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        // dto.setExportType(FileExportCenterEnum.free_trial_apply_user.getCode());
        // dto.setExportFlag(true);
        freeTrialUserService.export(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("手动审核剩余名额")
    @GetMapping("/manualCnt")
    public Response<JSONObject> getManualCnt(@RequestParam Long freeTrialId) {
        return ResponseFactory.success(freeTrialUserService.getManualCnt(freeTrialId));
    }

    @ApiOperation(value = "手动审核")
    @PostMapping(value = "/manual")
    public Response<Object> manual(@RequestBody FreeTrialUserDTO dto) {
        freeTrialUserService.manual(dto);
        return ResponseFactory.success();
    }
}
