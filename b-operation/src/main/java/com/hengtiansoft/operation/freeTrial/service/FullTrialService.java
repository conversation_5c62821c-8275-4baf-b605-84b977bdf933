package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.dto.FullTrialQueryDTO;
import com.hengtiansoft.privilege.entity.dto.FullTrialSaveDTO;
import com.hengtiansoft.privilege.entity.vo.FullTrialDetailVO;
import com.hengtiansoft.privilege.entity.vo.FullTrialListVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-08-02 09:18
 **/
public interface FullTrialService {
    PageVO<FullTrialListVO> getList(FullTrialQueryDTO dto);

    void save(FullTrialSaveDTO dto);

    FullTrialDetailVO get(Long id);

    PromoVO promotion(Long id);

    void delete(Long id);

    void end(Long id);

    void exportWaitList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);
}
