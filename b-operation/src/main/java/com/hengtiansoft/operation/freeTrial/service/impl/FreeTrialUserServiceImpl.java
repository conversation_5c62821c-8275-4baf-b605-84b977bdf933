package com.hengtiansoft.operation.freeTrial.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.enumeration.UserGradeEnum;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialUserService;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.enums.OrderEventEnum;
import com.hengtiansoft.order.enums.OrderStatusEnum;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.order.service.CommonOrderOptService;
import com.hengtiansoft.privilege.dao.MessageSubscribeDao;
import com.hengtiansoft.privilege.entity.dto.FreeReviewCntDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialUserDTO;
import com.hengtiansoft.privilege.entity.po.FreeTrial;
import com.hengtiansoft.privilege.entity.po.FreeTrialProduct;
import com.hengtiansoft.privilege.entity.po.FreeTrialUser;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.*;
import com.hengtiansoft.privilege.manager.FreeTrialManager;
import com.hengtiansoft.privilege.manager.FreeTrialProductManager;
import com.hengtiansoft.privilege.manager.FreeTrialUserManager;
import com.hengtiansoft.privilege.util.FreeTrialUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.enumeration.MessageSubscribeEnum;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniSmsManager;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.CustomerUserMore;
import com.hengtiansoft.user.manager.CustomerUserManager;
import com.hengtiansoft.user.manager.CustomerUserMoreManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FreeTrialUserServiceImpl implements FreeTrialUserService {
    @Resource
    private FreeTrialUserManager freeTrialUserManager;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private FreeTrialManager freeTrialManager;
    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private OrderManager orderManager;
    @Resource
    private CustomerUserMoreManager customerUsermoremanager;
    @Resource
    private MessageSubscribeDao messageSubscribeDao;
    @Resource
    private CommonOrderOptService commonOrderOptService;
    @Resource
    private OrderSyncAdapter orderSyncAdapter;
    @Resource
    private WeChatMiniSmsManager weChatMiniSmsManager;
    @Resource
    private MilkProducerService milkProducerService;

    @Override
    public PageVO<FreeTrialUserVO> getList(FreeTrialUserDTO dto) {
        FreeTrial freeTrial = freeTrialManager.findById(dto.getFreeTrialId());
        Assert.notNull(freeTrial, "活动不存在");
        dto.setOrderByColumn("apply_time");
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialUser> freeTrialUserList = freeTrialUserManager.findByConditionSql(dto);
        // 用户信息
        List<Long> userIds = StreamUtils.toList(freeTrialUserList, FreeTrialUser::getUserId);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(customerUserManager.findByIdList(userIds), CustomerUser::getId);
        // 试用商品信息
        List<Long> freeTrialIds = freeTrialUserList.stream().map(FreeTrialUser::getFreeTrialId).distinct().collect(Collectors.toList());
        List<FreeTrialProduct> freeTrialProducts = freeTrialProductManager.findByFreeTrialIdsWithInfo(freeTrialIds, FreeProductTypeEnum.FREE.getCode());
        Map<Long, FreeTrialProduct> freeTrialProductMap = StreamUtils.toMap(freeTrialProducts, FreeTrialProduct::getFreeTrialId);
        // 关联订单信息
        List<String> orderNos = StreamUtils.filterConvert(freeTrialUserList, tmp -> Objects.nonNull(tmp.getOrderNo()), FreeTrialUser::getOrderNo);
        List<OrderInfo> orderInfoList = orderManager.selectByOrderNoList(orderNos);
        Map<String, OrderInfo> orderInfoMap = StreamUtils.toMap(orderInfoList, OrderInfo::getOrderNo);
        // 所有活动已完成测评用户
        List<FreeReviewCntDTO> competeReviewCnt = freeTrialUserManager.findCompeteReviewCnt(userIds);
        Map<Long, FreeReviewCntDTO> competeReviewMap = StreamUtils.toMap(competeReviewCnt, FreeReviewCntDTO::getUserId);
        // 所有活动未完成测评用户
        List<FreeReviewCntDTO> notCompeteReviewCnt = freeTrialUserManager.findNotCompeteReviewCnt(userIds);
        Map<Long, FreeReviewCntDTO> notCompeteReviewMap = (Map<Long, FreeReviewCntDTO>) StreamUtils.toMap(notCompeteReviewCnt, FreeReviewCntDTO::getUserId);
        // 用户额外信息
        List<CustomerUserMore> userMoreList = customerUsermoremanager.findByUserIds(userIds);
        Map<Long, CustomerUserMore> userMoreMap = StreamUtils.toMap(userMoreList, CustomerUserMore::getUserId);
        // 关联了所有用户，该活动的商品订单
        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(userIds, dto.getFreeTrialId());
        Map<Long, List<OrderInfo>> freeTrialOrderMap = StreamUtils.group(freeTrialOrderList, OrderInfo::getUserId);
        //活动期间订单
        List<OrderInfo> orderList = orderManager.findByUserIdsAndFreeTrialTime(userIds, freeTrial.getStartTime(), freeTrial.getEndTime());
        Map<Long, List<OrderInfo>> orderMap = StreamUtils.group(orderList, OrderInfo::getUserId);
        return PageUtils.convert(freeTrialUserList, data -> {
            FreeTrialUserVO vo = FreeTrialUtil.convert2UserVO(data);
            CustomerUser customerUser = userMap.get(data.getUserId());
            if(customerUser != null){
                vo.setNickName(customerUser.getNickName());
                vo.setPhone(customerUser.getPhone());
                vo.setGrade(customerUser.getGrade());
            }
            FreeTrialProduct freeTrialProduct = freeTrialProductMap.get(data.getFreeTrialId());
            if(freeTrialProduct != null){
                vo.setProductName(freeTrialProduct.getProductName());
            }
            // 关联订单信息
            if(Objects.nonNull(data.getOrderNo())){
                OrderInfo orderInfo = orderInfoMap.get(data.getOrderNo());
                vo.setOrderStatusStr(OrderStatusEnum.getEnum(orderInfo.getOrderStatus()).getDesc());
            }
            FreeReviewCntDTO compete = competeReviewMap.get(data.getUserId());
            if(Objects.nonNull(compete)){
                vo.setCompeteReviewCnt(compete.getCnt());
            }
            FreeReviewCntDTO notCompete = notCompeteReviewMap.get(data.getUserId());
            if(Objects.nonNull(notCompete)){
                vo.setNotCompeteReviewCnt(notCompete.getCnt());
            }
            if(Objects.equals(data.getCompeteReview(), FreeCompeteReviewEnum.INIT.getCode())){
                vo.setCompeteReview(null);
            }
            // 90天是否下单单品、90天订单GMV
            CustomerUserMore userMore = userMoreMap.get(data.getUserId());
            if(Objects.nonNull(userMore)){
                vo.setOrderSingle90(userMore.getOrderSingle90());
                vo.setOrderGmv90(userMore.getOrderGmv90());
            }
            // 活动期间是否支付过、gmv
            List<OrderInfo> orders = orderMap.get(data.getUserId());
            vo.setIsPaid(0);
            vo.setActivityGmv(BigDecimal.ZERO);
            if(CollectionUtils.isNotEmpty(orders)){
                vo.setIsPaid(1);
                vo.setActivityGmv(orders.stream().map(OrderInfo::getRealAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 绑定商品GMV、绑定商品订单数量、关联商品订单号
            List<OrderInfo> orderInfos = freeTrialOrderMap.get(data.getUserId());
            if(CollectionUtils.isNotEmpty(orderInfos)){
                vo.setGmvBindProduct(orderInfos.stream().map(OrderInfo::getRealAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                vo.setOrderBindProduct(orderInfos.size());
                vo.setOrderNos(Lists.newArrayList(StreamUtils.toSet(orderInfos, OrderInfo::getOrderParentNo)));
            }
            // 手动审核按钮
            // 不能审核：1.活动维度：只有自动类型的
            FreeAuditTypeEnum auditTypeEnum = FreeAuditTypeEnum.getEnum(freeTrial.getAuditType());
            if(auditTypeEnum == FreeAuditTypeEnum.AUTO){
                vo.setManualButton(FreeManualButtonEnum.NO.getCode());
            }else if (auditTypeEnum == FreeAuditTypeEnum.AUTO_AND_MANUAL){
                if (Objects.equals(FlagEnum.YES.getCode(), data.getIsDraw()) && Objects.equals(FreeDrawTypeEnum.MANUAL.getCode(), data.getDrawType())){
                    vo.setManualButton(FreeManualButtonEnum.MANUAL_PASS.getCode());
                }else if(FreeOpenDrawEnum.getEnum(freeTrial.getOpenDraw()) != FreeOpenDrawEnum.AUTO){
                    vo.setManualButton(FreeManualButtonEnum.NO.getCode());
                }else if(freeTrial.getManualDrawCnt() >= freeTrial.getManualCnt()){
                    vo.setManualButton(FreeManualButtonEnum.NO.getCode());
                }else if(Objects.equals(FlagEnum.NO.getCode(), data.getIsDraw())){
                    vo.setManualButton(FreeManualButtonEnum.MANUAL.getCode());
                }else {
                    vo.setManualButton(FreeManualButtonEnum.NO.getCode());
                }
            }
            return vo;
        });
    }

    @Override
    public PageVO<FreeTrialUserVO> subscribeGetList(FreeTrialUserDTO dto) {
        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIdsWithInfo(Lists.newArrayList(dto.getFreeTrialId()),
                FreeProductTypeEnum.FREE.getCode());
        // 根据活动id，活动类型，查询订阅用户, 根据订阅时间倒序排序
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialUserVO> vos = messageSubscribeDao.findByConditionSql(dto);
        // 关联了所有用户，该活动的商品订单
        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(StreamUtils.toList(vos, FreeTrialUserVO::getUserId), dto.getFreeTrialId());
        Map<Long, List<OrderInfo>> freeTrialOrderMap = StreamUtils.group(freeTrialOrderList, OrderInfo::getUserId);
        // 活动用户列表
        FreeTrialUserDTO queryDTO = new FreeTrialUserDTO();
        queryDTO.setFreeTrialId(dto.getFreeTrialId());
        queryDTO.setUserIdList(StreamUtils.toList(vos, FreeTrialUserVO::getUserId));
        List<FreeTrialUser> freeTrialUserList = freeTrialUserManager.findByCondition(queryDTO);
        Map<Long, FreeTrialUser> freeTrialUserMap = StreamUtils.toMap(freeTrialUserList, FreeTrialUser::getUserId);

        return PageUtils.convert(vos, vo -> {
            if(CollectionUtils.isNotEmpty(freeTrialProductList)){
                // 绑定商品GMV、绑定商品订单数量、关联商品订单号
                List<OrderInfo> orderInfos = freeTrialOrderMap.get(vo.getUserId());
                if(CollectionUtils.isNotEmpty(orderInfos)){
                    vo.setGmvBindProduct(orderInfos.stream().map(OrderInfo::getRealAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    vo.setOrderBindProduct(orderInfos.size());
                    vo.setOrderNos(Lists.newArrayList(StreamUtils.toSet(orderInfos, OrderInfo::getOrderParentNo)));
                }
                vo.setProductName(freeTrialProductList.get(0).getProductName());
                vo.setProductId(freeTrialProductList.get(0).getProductId());
            }
            FreeTrialUser freeTrialUser = freeTrialUserMap.get(vo.getUserId());
            if(Objects.nonNull(freeTrialUser)){
                vo.setApplyTime(freeTrialUser.getApplyTime());
            }
            return vo;
        });
    }

    @Override
    public PageVO<FreeTrialUserVO> subscribeBackGetList(FreeTrialUserDTO dto) {
        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIdsWithInfo(Lists.newArrayList(dto.getFreeTrialId()),
                FreeProductTypeEnum.FREE.getCode());
        // 根据活动id，活动类型，查询订阅用户, 根据订阅时间倒序排序
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialUserVO> vos = messageSubscribeDao.findByConditionSql(dto);
        // 关联了所有用户，该活动的商品订单
        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(StreamUtils.toList(vos, FreeTrialUserVO::getUserId), dto.getFreeTrialId());
        Map<Long, List<OrderInfo>> freeTrialOrderMap = StreamUtils.group(freeTrialOrderList, OrderInfo::getUserId);

        return PageUtils.convert(vos, vo -> {
            if(CollectionUtils.isNotEmpty(freeTrialProductList)){
                // 绑定商品GMV、绑定商品订单数量、关联商品订单号
                List<OrderInfo> orderInfos = freeTrialOrderMap.get(vo.getUserId());
                if(CollectionUtils.isNotEmpty(orderInfos)){
                    vo.setGmvBindProduct(orderInfos.stream().map(OrderInfo::getRealAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    vo.setOrderBindProduct(orderInfos.size());
                    vo.setOrderNos(Lists.newArrayList(StreamUtils.toSet(orderInfos, OrderInfo::getOrderParentNo)));
                }
                vo.setProductName(freeTrialProductList.get(0).getProductName());
                vo.setProductId(freeTrialProductList.get(0).getProductId());
            }
            return vo;
        });
    }

    @Override
    public JSONObject getManualCnt(Long freeTrialId) {
        FreeTrial po = freeTrialManager.findById(freeTrialId);
        Assert.notNull(po, "活动不存在");
        // 已中奖手动数量
        Integer manualCnt = freeTrialUserManager.getManualCnt(freeTrialId);
        manualCnt = NumberOptUtil.isNullOrZero(manualCnt) ? 0 : manualCnt;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("auditType", po.getAuditType());
        jsonObject.put("manualCnt", po.getManualCnt() - manualCnt >= 0 ? po.getManualCnt() - manualCnt : 0);
        return jsonObject;
    }

    @Override
    public void manual(FreeTrialUserDTO dto) {
        List<MutablePair<CustomerUser, OrderInfo>> pairList = doManual(dto);
        this.pushAll(pairList);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MutablePair<CustomerUser, OrderInfo>> doManual(FreeTrialUserDTO dto) {
        Assert.notNull(dto.getFreeTrialId(), "活动id不能为空");
        Assert.notNull(dto.getUserId(), "用户id不能为空");
        FreeTrial freeTrial = freeTrialManager.findById(dto.getFreeTrialId());
        Assert.notNull(freeTrial, "活动不存在");
        if(FreeOpenDrawEnum.isManual(freeTrial.getOpenDraw())){
            throw new BusinessException("该活动已手动开奖");
        }
        if(freeTrial.getManualDrawCnt() > freeTrial.getManualCnt()){
            throw new BusinessException("该活动手动开奖数量已达上限");
        }
        // 手动中奖
        List<FreeTrialUser> freeTrialUserList = freeTrialUserManager.findByUserIdAndFreeTrialId(dto.getUserId(), dto.getFreeTrialId(), null);
        if(CollectionUtils.isEmpty(freeTrialUserList)){
            throw new BusinessException("该用户未申请该活动");
        }
        if(freeTrialUserList.size() > 1){
            throw new BusinessException("该用户重复申请该活动");
        }
        FreeTrialUser freeTrialUser = freeTrialUserList.get(0);
        if(Objects.equals(freeTrialUser.getIsDraw(), FlagEnum.YES.getCode())){
            throw new BusinessException("该用户已中奖");
        }
        freeTrialUser.setIsDraw(FlagEnum.YES.getCode());
        freeTrialUser.setStatus(FreeTrialUserStatusEnum.WINNING.getCode());
        freeTrialUser.setDrawType(FreeDrawTypeEnum.MANUAL.getCode());
        freeTrialUser.setDrawTime(new Date());
        freeTrialUserManager.update(freeTrialUser);
        // 创建订单，
        List<MutablePair<CustomerUser, OrderInfo>> pairList = commonOrderOptService.doCreateOrder(freeTrial, Lists.newArrayList(freeTrialUser));
        // 增加手动中奖数量
        int count = freeTrialManager.addManualDrawCnt(freeTrial.getId(), 1);
        if(count == 0){
            throw new BusinessException("手动中奖失败");
        }
        return pairList;
    }

    private void pushAll(List<MutablePair<CustomerUser, OrderInfo>> pairList) {
        if(CollectionUtils.isEmpty(pairList)){
            return;
        }
        for (MutablePair<CustomerUser, OrderInfo> pair : pairList) {
            try {
                OrderInfo orderInfo = pair.right;
                CustomerUser customerUser = pair.left;
                // 推送父订单和单独订单
                orderSyncAdapter.orderPushAll(orderInfo, Lists.newArrayList(orderInfo));

                try {
                    if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderInfo.getCardStrs())){
                        weChatMiniSmsManager.startUsingCard(orderInfo.getCardStrs(), customerUser.getWechartOpenId());
                    }
                } catch (Exception e) {
                    log.error("推送微信奶卡信息失败", e);
                }
                //订单上报有数
                milkProducerService.syncOrderToYoushu(orderInfo.getOrderNo());
                //订单上报火山
                milkProducerService.syncOrderToVolcengine(orderInfo.getOrderNo(), OrderEventEnum.ORDER_CREATE.getCode());
            } catch (Exception e) {
                log.error("推送订单信息失败", e);
            }
        }
    }

    @Override
    public FreeTrialUserVO get(Long id) {
        FreeTrialUser po = freeTrialUserManager.findById(id);
        Assert.notNull(po, "活动不存在");
        FreeTrialUserVO vo = FreeTrialUtil.convert2UserVO(po);
        // 用户信息
        CustomerUser customerUser = customerUserManager.findById(po.getUserId());
        if(customerUser != null){
            vo.setNickName(customerUser.getNickName());
            vo.setPhone(customerUser.getPhone());
            vo.setGrade(customerUser.getGrade());
        }
        // 试用商品信息
        List<FreeTrialProduct> freeTrialProducts = freeTrialProductManager.findByFreeTrialIdsWithInfo(Lists.newArrayList(po.getFreeTrialId()), FreeProductTypeEnum.FREE.getCode());
        if(CollectionUtils.isNotEmpty(freeTrialProducts)){
            vo.setProductName(freeTrialProducts.get(0).getProductName());
        }
        // 关联订单信息
        if(Objects.nonNull(po.getOrderNo())){
            OrderInfo orderInfo = orderManager.selectByOrderNo(po.getOrderNo());
            vo.setOrderStatusStr(OrderStatusEnum.getEnum(orderInfo.getOrderStatus()).getDesc());
        }
        if(Objects.equals(po.getCompeteReview(), FreeCompeteReviewEnum.INIT.getCode())){
            vo.setCompeteReview(null);
        }
        // 活动信息
        FreeTrial freeTrial = freeTrialManager.findById(po.getFreeTrialId());
        if(freeTrial != null){
            vo.setFreeTrialName(freeTrial.getName());
        }
        // 关联了所有用户，该活动的商品订单
        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(Lists.newArrayList(customerUser.getId()), po.getFreeTrialId());
        if(CollectionUtils.isNotEmpty(freeTrialOrderList)){
            vo.setGmvBindProduct(freeTrialOrderList.stream().map(OrderInfo::getRealAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            vo.setOrderBindProduct(freeTrialOrderList.size());
            vo.setOrderNos(Lists.newArrayList(StreamUtils.toSet(freeTrialOrderList, OrderInfo::getOrderParentNo)));
        }
        return vo;
    }

    @Override
    public void export(FreeTrialUserDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        validTimeRange(dto);
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(dto.getExportType());
        if (null == enumByCode){
            throw new BusinessException("导出类型不存在");
        }
        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(dto.getExportType());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());
        dto.setFileName(enumByCode.getFileName() + date);
        dataExportTaskDao.saveOne(dataExportTask);
        // 异步执行
        FreeTrialUserService bean = SpringContextUtils.getApplicationContext().getBean(FreeTrialUserService.class);
        bean.export(dto, dataExportTask);
    }

    @Override
    @Async
    public void export(FreeTrialUserDTO dto, DataExportTask exportTask) {
        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            List excelVOS = null;
            Class head = null;
            Integer mergeRowIndex = null;
            List<Integer> mergeColumnRegion = Lists.newArrayList();
            if(enumByCode == FileExportCenterEnum.free_trial_apply_user){
                PageVO<FreeTrialUserVO> voPageVOS = this.getList(dto);
                List<FreeTrialUserVO> vos = voPageVOS.getList();
                excelVOS = this.convert2ExcelList(vos);
                head = FreeTrialApplyUserExcelVO.class;
                mergeRowIndex = null;
                mergeColumnRegion = Lists.newArrayList();
            }else if (enumByCode == FileExportCenterEnum.free_trial_draw_user){
                PageVO<FreeTrialUserVO> voPageVOS = this.getList(dto);
                List<FreeTrialUserVO> vos = voPageVOS.getList();
                excelVOS = this.convert2DrawExcelList(vos);
                head = FreeTrialDrawUserExcelVO.class;
                mergeRowIndex = null;
                mergeColumnRegion = Lists.newArrayList();
            }else if(enumByCode == FileExportCenterEnum.free_trial_subscribe_user){
                dto.setMsgType(MessageSubscribeEnum.FREE_START_NOTICE.getCode());
                PageVO<FreeTrialUserVO> voPageVOS = this.subscribeGetList(dto);
                List<FreeTrialUserVO> vos = voPageVOS.getList();
                excelVOS = this.convert2SubscribeExcelList(vos);
                head = FreeTrialSubscribeUserExcelVO.class;
                mergeRowIndex = null;
                mergeColumnRegion = Lists.newArrayList();
            }else if(enumByCode == FileExportCenterEnum.free_trial_subscribe_back_user){
                dto.setMsgType(MessageSubscribeEnum.FREE_BACK_NOTICE.getCode());
                PageVO<FreeTrialUserVO> voPageVOS = this.subscribeBackGetList(dto);
                List<FreeTrialUserVO> vos = voPageVOS.getList();
                excelVOS = this.convert2SubscribeBackExcelList(vos);
                head = FreeTrialSubscribeBackUserExcelVO.class;
                mergeRowIndex = null;
                mergeColumnRegion = Lists.newArrayList();
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, head)
                    .autoCloseStream(Boolean.TRUE)
                    .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnRegion))
                    .sheet().doWrite(excelVOS);

            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            String url = aliyunOssUtils.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()),
                    fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (RuntimeException e) {
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }

    private List convert2SubscribeBackExcelList(List<FreeTrialUserVO> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return Lists.newArrayList();
        }
        List<FreeTrialSubscribeBackUserExcelVO> excelVOS = Lists.newArrayList();
        for (FreeTrialUserVO vo : vos) {
            FreeTrialSubscribeBackUserExcelVO excelVO = BeanUtils.copy(vo, FreeTrialSubscribeBackUserExcelVO::new);
            excelVO.setGradeStr(UserGradeEnum.getEnum(vo.getGrade()) == null ? null : UserGradeEnum.getEnum(vo.getGrade()).getDesc());
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }

    private List convert2SubscribeExcelList(List<FreeTrialUserVO> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return Lists.newArrayList();
        }
        List<FreeTrialSubscribeUserExcelVO> excelVOS = Lists.newArrayList();
        for (FreeTrialUserVO vo : vos) {
            FreeTrialSubscribeUserExcelVO excelVO = BeanUtils.copy(vo, FreeTrialSubscribeUserExcelVO::new);
            excelVO.setGradeStr(UserGradeEnum.getEnum(vo.getGrade()) == null ? null : UserGradeEnum.getEnum(vo.getGrade()).getDesc());
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }


    private List convert2DrawExcelList(List<FreeTrialUserVO> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return Lists.newArrayList();
        }
        List<FreeTrialDrawUserExcelVO> excelVOS = Lists.newArrayList();
        for (FreeTrialUserVO vo : vos) {
            FreeTrialDrawUserExcelVO excelVO = BeanUtils.copy(vo, FreeTrialDrawUserExcelVO::new);
            excelVO.setCompeteReviewStr(FlagEnum.getEnumOrNull(vo.getCompeteReview()) == null ? null : FlagEnum.getEnumOrNull(vo.getCompeteReview()).getDesc());
            excelVO.setOrderStatusStr(vo.getOrderStatusStr());
            // 1自动，2手动
            excelVO.setDrawTypeStr(FreeDrawTypeEnum.getEnumOrNull(vo.getDrawType()) == null ? null : FreeDrawTypeEnum.getEnumOrNull(vo.getDrawType()).getDesc());
            excelVO.setGradeStr(UserGradeEnum.getEnum(vo.getGrade()) == null ? null : UserGradeEnum.getEnum(vo.getGrade()).getDesc());
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }

    private List convert2ExcelList(List<FreeTrialUserVO> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return Lists.newArrayList();
        }
        List<FreeTrialApplyUserExcelVO> excelVOS = Lists.newArrayList();
        for (FreeTrialUserVO vo : vos) {
            FreeTrialApplyUserExcelVO excelVO = BeanUtils.copy(vo, FreeTrialApplyUserExcelVO::new);
            excelVO.setIsDrawStr(FlagEnum.getEnumOrNull(vo.getIsDraw()) == null ? null : FlagEnum.getEnumOrNull(vo.getIsDraw()).getDesc());
            excelVO.setOrderSingle90Str(FlagEnum.getEnumOrNull(vo.getOrderSingle90()) == null ? null : FlagEnum.getEnumOrNull(vo.getOrderSingle90()).getDesc());
            excelVO.setGradeStr(UserGradeEnum.getEnum(vo.getGrade()) == null ? null : UserGradeEnum.getEnum(vo.getGrade()).getDesc());
            excelVO.setIsPaidStr(FlagEnum.getEnumOrNull(vo.getIsPaid()) == null ? null : FlagEnum.getEnumOrNull(vo.getIsPaid()).getDesc());
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }

    private void validTimeRange(FreeTrialUserDTO dto) {

    }


}
