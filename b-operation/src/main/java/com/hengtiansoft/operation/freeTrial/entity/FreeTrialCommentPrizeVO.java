package com.hengtiansoft.operation.freeTrial.entity;

import com.hengtiansoft.order.entity.vo.CouponRuleListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("试用测评奖励")
public class FreeTrialCommentPrizeVO {


    @ApiModelProperty("奖励类型 1-优惠券 2-积分 3-优惠券+积分")
    private Integer type;

    @ApiModelProperty("优惠券规则ids")
    private List<Long> couponRuleIds;

    @ApiModelProperty("优惠券列表")
    private List<CouponRule> couponRuleList;

    @ApiModelProperty("优惠券奖励门槛")
    private PrizeThreshold couponThreshold;

    @ApiModelProperty("积分")
    private Integer point;

    @ApiModelProperty("积分奖励门槛")
    private PrizeThreshold pointThreshold;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PrizeThreshold {

        @ApiModelProperty("权重排名")
        private Integer weightRank;

        @ApiModelProperty("点赞排名")
        private Integer likeRank;

        @ApiModelProperty("浏览排名")
        private Integer visitRank;

        @ApiModelProperty("转发排名")
        private Integer shareRank;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CouponRule {

        @ApiModelProperty("优惠券规则id")
        private Long id;

        @ApiModelProperty("优惠券名称")
        private String couponName;

        @ApiModelProperty("渠道 7- 0元试用")
        private Integer channel;

        @ApiModelProperty("适用商品 0-不限 1-单次购买 2-周期购")
        private Integer buyType;

        @ApiModelProperty("有效时间开始")
        private Date startTime;

        @ApiModelProperty("有效时间结束")
        private Date endTime;

        @ApiModelProperty("是否公开 0-不公开 1-公开")
        private int isPublic;

        @ApiModelProperty("状态 0-未上线 1-已上线 2-已下线")
        private int status;

        private Date onlineTime;

        private Date offlineTime;

        private BigDecimal amountFull;

        private BigDecimal amountReduce;

        private Integer amountLimit;

        private Integer couponWay;

        private Integer discount;
    }

}