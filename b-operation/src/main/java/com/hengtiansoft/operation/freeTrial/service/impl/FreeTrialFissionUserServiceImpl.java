package com.hengtiansoft.operation.freeTrial.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CustomerUserTypeEnum;
import com.hengtiansoft.common.enumeration.UserGradeEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialFissionUserService;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.privilege.entity.dto.FreeTrialUserDTO;
import com.hengtiansoft.privilege.entity.po.FreeTrial;
import com.hengtiansoft.privilege.entity.po.FreeTrialProduct;
import com.hengtiansoft.privilege.entity.po.FreeTrialUser;
import com.hengtiansoft.privilege.entity.vo.FreeTrialFissionUserCntVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.privilege.enums.FreeProductTypeEnum;
import com.hengtiansoft.privilege.manager.FreeTrialManager;
import com.hengtiansoft.privilege.manager.FreeTrialProductManager;
import com.hengtiansoft.privilege.manager.FreeTrialUserManager;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.entity.dto.UserQueryDTO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialFissionUserExcelVO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.github.pagehelper.page.PageMethod.startPage;


@Service
public class FreeTrialFissionUserServiceImpl implements FreeTrialFissionUserService {

    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private FreeTrialUserManager freeTrialUserManager;
    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private OrderManager orderManager;
    @Resource
    private FreeTrialManager freeTrialManager;

    @Override
    public PageVO<FreeTrialUserVO> page(UserQueryDTO dto) {
        Long freeTrialId = dto.getFreeTrialId();
        Assert.notNull(freeTrialId, "活动id参数缺失!");
        FreeTrial freeTrial = freeTrialManager.findById(dto.getFreeTrialId());
        Assert.notNull(freeTrial, "活动不存在");
        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIdsWithInfo(Lists.newArrayList(freeTrialId),
                FreeProductTypeEnum.FREE.getCode());
        FreeTrialProduct freeTrialProduct = StreamUtils.getFirst(freeTrialProductList);

        dto.setUserType(CustomerUserTypeEnum.freeTrial_user.getCode());
        startPage(dto.getPageNum(), dto.getPageSize());
        List<CustomerUser> userList = customerUserDao.findFissionUser(dto);
        if (CollectionUtils.isEmpty(userList)) {
            return PageUtils.emptyPage(dto);
        }

        List<Long> userIds = StreamUtils.toList(userList, CustomerUser::getId);
        FreeTrialUserDTO queryDTO = new FreeTrialUserDTO();
        queryDTO.setFreeTrialId(dto.getFreeTrialId());
        queryDTO.setUserIdList(userIds);
        List<FreeTrialUser> freeTrialUserList = freeTrialUserManager.findByCondition(queryDTO);
        Map<Long, FreeTrialUser> freeTrialUserMap = StreamUtils.toMap(freeTrialUserList, FreeTrialUser::getUserId);

        List<OrderInfo> freeTrialOrderList = orderManager.findByUserIdsAndFreeTrialId(userIds, freeTrialId);
        Map<Long, List<OrderInfo>> freeTrialOrderMap = StreamUtils.group(freeTrialOrderList, OrderInfo::getUserId);
        //活动期间订单
        List<OrderInfo> orderList = orderManager.findByUserIdsAndFreeTrialTime(userIds, freeTrial.getStartTime(), freeTrial.getEndTime());
        Map<Long, List<OrderInfo>> orderMap = StreamUtils.group(orderList, OrderInfo::getUserId);
        return PageUtils.convert(userList, data ->{
            FreeTrialUserVO vo = new FreeTrialUserVO();
            vo.setId(data.getId());
            vo.setUserId(data.getId());
            vo.setPhone(data.getPhone());
            vo.setNickName(data.getOrderNickName());
            vo.setGrade(data.getGrade());
            vo.setGradeStr(UserGradeEnum.getDescByCode(data.getGrade()));
            vo.setRegisterTime(data.getFirstLoginTime());

            FreeTrialUser freeTrialUser = freeTrialUserMap.get(data.getId());
            vo.setFreeTrialId(freeTrialId);
            if(null != freeTrialUser){
                vo.setApplyTime(freeTrialUser.getApplyTime());
            }
            if(null != freeTrialProduct){
                vo.setProductName(freeTrialProduct.getProductName());
            }
            vo.setGmvBindProduct(BigDecimal.ZERO);
            vo.setOrderBindProduct(0);
            List<OrderInfo> orderInfoList = freeTrialOrderMap.get(data.getId());
            if(CollectionUtils.isNotEmpty(orderInfoList)){
                BigDecimal gmvBindProduct = orderInfoList.stream()
                        .map(OrderInfo::getRealAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer orderBindProduct = orderInfoList.size();
                vo.setGmvBindProduct(gmvBindProduct);
                vo.setOrderBindProduct(orderBindProduct);
            }
            // 活动期间是否支付过、gmv
            List<OrderInfo> orders = orderMap.get(data.getId());
            vo.setIsPaid(0);
            vo.setActivityGmv(BigDecimal.ZERO);
            if(CollectionUtils.isNotEmpty(orders)){
                vo.setIsPaid(1);
                vo.setActivityGmv(orders.stream().map(OrderInfo::getRealAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            return vo;
        });
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialFissionUserExcelVO.class).build();
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setFreeTrialId(dto.getFreeTrialId());
        userQueryDTO.setUserId(dto.getUserId());
        userQueryDTO.setPhone(dto.getPhone());
        userQueryDTO.setNickName(dto.getNickName());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            userQueryDTO.setPageNum(pageNum);
            userQueryDTO.setPageSize(pageSize);

            PageVO<FreeTrialUserVO> fissionUserPageVO = this.page(userQueryDTO);
            Pagination pagination = fissionUserPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;

            excelWriter.write(BeanUtils.copyList(fissionUserPageVO.getList(), FreeTrialFissionUserExcelVO::new), writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public FreeTrialFissionUserCntVO cnt() {
        FreeTrialFissionUserCntVO vo = new FreeTrialFissionUserCntVO();
        vo.setAllFissiUserCnt(customerUserDao.cntFissionUser(CustomerUserTypeEnum.freeTrial_user.getCode(), null));
        vo.setTodayFissiUserCnt(customerUserDao.cntFissionUser(CustomerUserTypeEnum.freeTrial_user.getCode(), DateUtil.getDayOfStart(LocalDate.now())));
        return vo;
    }
}
