package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTasteService;
import com.hengtiansoft.privilege.entity.dto.FreeTasteQueryDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTasteSaveDTO;
import com.hengtiansoft.privilege.entity.vo.FreeTasteDetailVO;
import com.hengtiansoft.privilege.entity.vo.FreeTasteListVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "0元尝鲜B端")
@RequestMapping("/freeTaste")
public class FreeTasteController {

    @Resource
    private FreeTasteService freeTasteService;

    @ApiOperation("分页列表")
    @PostMapping("/getList")
    public Response<PageVO<FreeTasteListVO>> getList(@RequestBody FreeTasteQueryDTO dto) {
        return ResponseFactory.success(freeTasteService.getList(dto));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Void> save(@RequestBody @Validated FreeTasteSaveDTO dto) {
        freeTasteService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<FreeTasteDetailVO> get(@RequestParam Long id) {
        return ResponseFactory.success(freeTasteService.get(id));
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        return ResponseFactory.success(freeTasteService.promotion(id));
    }

}
