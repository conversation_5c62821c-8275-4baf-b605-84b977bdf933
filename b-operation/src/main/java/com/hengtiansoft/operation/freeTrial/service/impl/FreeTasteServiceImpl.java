package com.hengtiansoft.operation.freeTrial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.util.CommonOptUtil;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.DiscountActivityRangeDao;
import com.hengtiansoft.item.dao.SkuDao;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.DiscountActivity;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.vo.ProductSkuListVO;
import com.hengtiansoft.item.manager.DiscountActivityManager;
import com.hengtiansoft.operation.freeTrial.service.FreeTasteService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.dao.CouponRuleDao;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityDao;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityProductDao;
import com.hengtiansoft.privilege.dao.PtGoodsDao;
import com.hengtiansoft.privilege.entity.dto.FreeTasteQueryDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTasteSaveDTO;
import com.hengtiansoft.privilege.entity.dto.MonitorPageDTO;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.entity.vo.FreeTasteDetailVO.Coupon;
import com.hengtiansoft.privilege.entity.vo.FreeTasteDetailVO.Product;
import com.hengtiansoft.privilege.enums.FreeTrialSubActivityProductTargetTypeEnum;
import com.hengtiansoft.privilege.enums.FreeTrialSubActivityTypeEnum;
import com.hengtiansoft.privilege.enums.MoTypeEnum;
import com.hengtiansoft.privilege.manager.FreeTasteManager;
import com.hengtiansoft.privilege.manager.MonitorPageManager;
import com.hengtiansoft.privilege.manager.PtActivityManager;
import com.hengtiansoft.privilege.util.FreeTasteUtil;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

@Slf4j
@Service
public class FreeTasteServiceImpl implements FreeTasteService {

    @Resource
    private FreeTasteManager freeTasteManager;
    @Resource
    private FreeTrialSubActivityProductDao freeTrialSubActivityProductDao;
    @Resource
    private SkuDao skuDao;
    @Resource
    private CouponRuleDao couponRuleDao;
    @Resource
    private FreeTrialSubActivityDao freeTrialSubActivityDao;
    @Resource
    private MonitorPageManager monitorPageManager;
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private OrderManager orderManager;
    @Resource
    private DiscountActivityRangeDao discountActivityRangeDao;
    @Resource
    private DiscountActivityManager discountActivityManager;
    @Resource
    private PtGoodsDao ptGoodsDao;
    @Resource
    private PtActivityManager ptActivityManager;

    @Override
    public PageVO<FreeTasteListVO> getList(FreeTasteQueryDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        dto.setActivityType(FreeTrialSubActivityTypeEnum.FREE_TASTE.getCode());
        List<FreeTrialSubActivity> list = freeTasteManager.findByCondition(dto);
        if (CollectionUtils.isEmpty(list)) {
            return PageUtils.emptyPage(dto);
        }
        List<Long> activityIds = StreamUtils.toList(list, FreeTrialSubActivity::getId);
        List<FreeTrialSubActivityProduct> products = freeTrialSubActivityProductDao.selectByActivityIds(activityIds);
        Map<Long, List<FreeTrialSubActivityProduct>> productMap = StreamUtils.group(products, FreeTrialSubActivityProduct::getFreeTrialSubActivityId);
        //订单相关
        List<FreeTrialSubCntVO> freeTrialSubCntList = orderManager.findFreeTasteOrder(activityIds);
        Map<Long, FreeTrialSubCntVO> cntMap = StreamUtils.toMap(freeTrialSubCntList, FreeTrialSubCntVO::getActivityId);
        //主品【商品】
        List<FreeTrialSubActivityProduct> mainProducts = StreamUtils.filter(products,
                x -> Objects.equals(x.getType(), 1) && Objects.equals(x.getTargetType(), FreeTrialSubActivityProductTargetTypeEnum.PRODUCT.getCode()));
        List<Long> mainSkuIds = StreamUtils.toList(mainProducts, FreeTrialSubActivityProduct::getSkuId);
        ProductBaseSearchDTO skuSearchDTO = new ProductBaseSearchDTO();
        skuSearchDTO.setSkuIds(mainSkuIds);
        skuSearchDTO.setPageNum(1);
        skuSearchDTO.setPageSize(Integer.MAX_VALUE);
        Map<Long, ProductSkuListVO> mainSkuMap = StreamUtils.toMap(skuDao.selectProductSkuList(skuSearchDTO), ProductSkuListVO::getSkuId);
        //赠品【优惠券】
        List<FreeTrialSubActivityProduct> giftCoupons = StreamUtils.filter(products,
                x -> Objects.equals(x.getType(), 2) && Objects.equals(x.getTargetType(), FreeTrialSubActivityProductTargetTypeEnum.COUPON.getCode()));
        List<Long> giftCouponRuleIds = StreamUtils.toList(giftCoupons, FreeTrialSubActivityProduct::getTargetId);
        Map<Long, CouponRule> giftCouponRuleMap = StreamUtils.toMap(couponRuleDao.findByIds(giftCouponRuleIds), CouponRule::getId);

        return PageUtils.convert(list, data->{
            FreeTasteListVO vo = new FreeTasteListVO();
            vo.setId(data.getId());
            vo.setName(data.getName());
            vo.setIsPublic(data.getIsPublic());
            vo.setNoticeTime(data.getNoticeTime());
            vo.setStartTime(data.getStartTime());
            vo.setEndTime(data.getEndTime());
            vo.setUpdateTime(data.getUpdateTime());
            vo.setStatus(data.getStatus());
            vo.setPayUserCnt(0L);//支付用户
            vo.setOrderCnt(0L);//订单量
            vo.setTotalGmv(BigDecimal.ZERO);//累计gmv
            FreeTrialSubCntVO freeTrialSubCntVO = cntMap.get(data.getId());
            if(null != freeTrialSubCntVO){
                vo.setPayUserCnt(freeTrialSubCntVO.getPayUserCnt());
                vo.setOrderCnt(freeTrialSubCntVO.getOrderCnt());
                vo.setTotalGmv(freeTrialSubCntVO.getTotalGmv());
            }
            List<FreeTrialSubActivityProduct> activityProductList = productMap.get(data.getId());
            activityProductList.stream().filter(l -> l.getType() == 1 && l.getTargetType() == 1).findFirst().ifPresent(target -> {
                ProductSkuListVO productSkuListVO = mainSkuMap.get(target.getSkuId());
                if(null != productSkuListVO){
                    vo.setFreePicUrl(StringUtils.isEmpty(productSkuListVO.getSkuPicUrl())? productSkuListVO.getPicUrl() : productSkuListVO.getSkuPicUrl());
                    vo.setFreeProductName(productSkuListVO.getProductName());
                    vo.setFreeSkuCode(productSkuListVO.getSkuCode());
                    vo.setFreeSalePrice(productSkuListVO.getSalePrice());
                    vo.setStock(productSkuListVO.getStock());
                    vo.setPoint(target.getPoint());
                    vo.setActivityPrice(target.getPrice());
                }
            });
            activityProductList.stream().filter(l -> l.getType() == 2 && l.getTargetType() == 2).findFirst().ifPresent(target -> {
                CouponRule couponRule = giftCouponRuleMap.get(target.getTargetId());
                if(null != couponRule){
                    vo.setCouponRuleId(couponRule.getId());
                    vo.setCouponRuleName(couponRule.getCouponName());
                }
            });
            return vo;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(FreeTasteSaveDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        // 数据校验
        validParam(dto);
        // 校验营销活动互斥
        validateMarketingProduct(dto.getTrialProduct());
        FreeTrialSubActivity po = FreeTasteUtil.buildPO(dto);
        List<FreeTrialSubActivityProduct> products = FreeTasteUtil.buildProductPO(dto);
        po.setActivityType(FreeTrialSubActivityTypeEnum.FREE_TASTE.getCode());
        po.setOperator(user.getUserName());
        po.setStatus(CommonOptUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
        if(Objects.isNull(dto.getId())){
            freeTrialSubActivityDao.insert(po);
            for (FreeTrialSubActivityProduct activityProduct : products) {
                activityProduct.setFreeTrialSubActivityId(po.getId());
            }
            freeTrialSubActivityProductDao.insertList(products);
            //推广
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(dto.getName()+"_0元尝鲜");
            monitorPage.setTargetId(po.getId());
            monitorPage.setType(MoTypeEnum.FREE_TASTE.getCode());
            monitorPage.setTargetName(dto.getName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
        }else{
            FreeTrialSubActivity old = freeTrialSubActivityDao.findById(dto.getId());
            Assert.notNull(old, "活动不存在");
            // 有id，更新
            // 更新换成了updateByPrimaryKey，就不能把null值赋给po了，不然会把数据库的值覆盖掉
            BeanUtil.copyProperties(po, old, CopyOptions.create().setIgnoreNullValue(true));
            old.setUpdateTime(new Date());
            old.setGrade(po.getGrade());
            old.setLabelId(po.getLabelId());
            freeTrialSubActivityDao.updateAll(old);
            freeTrialSubActivityProductDao.deleteByActivityId(dto.getId());
            freeTrialSubActivityProductDao.insertList(products);
        }
    }

    @Override
    public void exportWaitList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTasteExportVO.class).build();
        FreeTasteQueryDTO queryDTO = new FreeTasteQueryDTO();
        queryDTO.setId(dto.getRecordId());
        queryDTO.setName(dto.getName());
        queryDTO.setStatus(dto.getStatus());
        queryDTO.setUpdateTimeStart(dto.getUpdateTimeStart());
        queryDTO.setUpdateTimeEnd(dto.getUpdateTimeEnd());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<FreeTasteListVO> freeTasteListVO = this.getList(queryDTO);
            Pagination pagination = freeTasteListVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTasteExportVO> exportVOList = FreeTasteUtil.convert2ExportVO(freeTasteListVO.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);

    }

    @Override
    public FreeTasteDetailVO get(Long id) {
        FreeTrialSubActivity po = freeTrialSubActivityDao.findById(id);
        Assert.notNull(po, "活动不存在");
        FreeTasteDetailVO vo = FreeTasteUtil.convert2VO(po);
        List<FreeTrialSubActivityProduct> products = freeTrialSubActivityProductDao.selectByActivityIds(Arrays.asList(id));
        //主品【商品】
        products.stream().filter(l -> l.getType() == 1 && l.getTargetType() == 1).findFirst().ifPresent(target -> {
            vo.setActivityPrice(target.getPrice());
            vo.setPoint(target.getPoint());
            vo.setSingleLimitNum(target.getSingleLimitNum());
            vo.setLimitNum(target.getLimitNum());
            ProductBaseSearchDTO skuSearchDTO = new ProductBaseSearchDTO();
            skuSearchDTO.setSkuIds(Arrays.asList(target.getSkuId()));
            skuSearchDTO.setPageNum(1);
            skuSearchDTO.setPageSize(Integer.MAX_VALUE);
            ProductSkuListVO mainSku = StreamUtils.getFirst(skuDao.selectProductSkuList(skuSearchDTO));
            Product product = new Product();
            product.setProductId(mainSku.getId());
            product.setPicUrl(StringUtils.isEmpty(mainSku.getSkuPicUrl())? mainSku.getPicUrl() : mainSku.getSkuPicUrl());
            product.setProductName(mainSku.getProductName());
            product.setSpecValueList(mainSku.getSpecValueList());
            product.setSkuCode(mainSku.getSkuCode());
            product.setSkuId(mainSku.getSkuId());
            product.setStock(mainSku.getStock());
            product.setSaleStatus(mainSku.getSaleStatus());
            product.setSalePrice(mainSku.getSalePrice());
            product.setIsPublic(mainSku.getEnableShow());
            vo.setTrialProduct(product);
        });

        //赠品【优惠券】
        products.stream().filter(l -> l.getType() == 2 && l.getTargetType() == 2).findFirst().ifPresent(target -> {
            CouponRule giftCouponRule = StreamUtils.getFirst(couponRuleDao.findByIds(Arrays.asList(target.getTargetId())));
            if(null != giftCouponRule){
                Coupon coupon = new Coupon();
                coupon.setRuleId(giftCouponRule.getId());
                coupon.setCouponName(giftCouponRule.getCouponName());
                coupon.setChannel(giftCouponRule.getChannel());
                coupon.setBuyType(giftCouponRule.getBuyType());
                coupon.setStartTime(giftCouponRule.getStartTime());
                coupon.setEndTime(giftCouponRule.getEndTime());
                coupon.setIsPublic(giftCouponRule.getIsPublic());
                coupon.setStatus(giftCouponRule.getStatus());
                coupon.setOnlineTime(giftCouponRule.getOnlineTime());
                coupon.setOfflineTime(giftCouponRule.getOfflineTime());
                coupon.setAmountFull(giftCouponRule.getAmountFull());
                coupon.setAmountReduce(giftCouponRule.getAmountReduce());
                coupon.setAmountLimit(giftCouponRule.getAmountLimit());
                coupon.setCouponWay(giftCouponRule.getCouponWay());
                coupon.setCouponRange(giftCouponRule.getCouponRange());
                coupon.setRemainingNum(giftCouponRule.getIssuedQuantity().longValue() - giftCouponRule.getReceiveCount().longValue());
                vo.setCoupon(coupon);
            }
        });
        return vo;
    }

    @Override
    public PromoVO promotion(Long id) {
        FreeTrialSubActivity subActivity = freeTrialSubActivityDao.findById(id);
        Assert.notNull(subActivity, "活动不存在");
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.FREE_TASTE.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);
        PromoVO promoVO = new PromoVO();
        promoVO.setId(id);
        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    private void validParam(FreeTasteSaveDTO dto) {
        if(!dto.getNoticeTime().before(dto.getStartTime())){
            throw new BusinessException("预告时间需在活动开始时间之前");
        }
        if(dto.getEndTime().before(dto.getStartTime())){
            throw new BusinessException("活动结束时间不能早于开始时间");
        }
        if(Objects.nonNull(dto.getPoint())){
            if(dto.getPoint() < 1){
                throw new BusinessException("所需积分需大于1");
            }
        }
        if(Objects.nonNull(dto.getActivityPrice())){
            if(dto.getActivityPrice().compareTo(BigDecimal.ZERO) <= 0){
                throw new BusinessException("活动价需大于0");
            }
        }
        Integer status = CommonOptUtil.getStatus(dto.getStartTime(), dto.getEndTime()).getCode();
        if(!Objects.equals(status, CommonActivityStatusEnum.END.getCode())){
            // 校验活动时间重复
            List<FreeTrialSubActivity> existPos = freeTrialSubActivityDao.findBySkuId(dto.getTrialProduct().getSkuId(), 1, 1);
            for (FreeTrialSubActivity existPo : existPos) {
                if(Objects.equals(existPo.getId(), dto.getId())){
                    continue;
                }
                if (DateUtil.isOverlap(dto.getStartTime(), dto.getEndTime(), existPo.getStartTime(), existPo.getEndTime())) {
                    throw new BusinessException("该活动【"+dto.getName()+"】的时间段与名称为【" + existPo.getName() + "】的[" + FreeTrialSubActivityTypeEnum.getDescByCode(existPo.getActivityType())+ "]活动有重复，请修改");
                }
            }
        }
    }

    private void validateMarketingProduct(FreeTasteSaveDTO.Product freeProduct) {
        Long productId = freeProduct.getProductId();
        Long skuId = freeProduct.getSkuId();
        List<DiscountActivityRange> discountRanges = discountActivityRangeDao.findByProductIdAndSkuId(productId, skuId);
        if (CollectionUtils.isNotEmpty(discountRanges)) {
            List<Long> discountActivityIds = discountRanges.stream().map(DiscountActivityRange::getDiscountActivityId).distinct().collect(Collectors.toList());
            List<DiscountActivity> discountActivities = discountActivityManager.getByIds(discountActivityIds);
            List<DiscountActivity> conflictDiscountActivities = discountActivities.stream()
                    .filter(e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()).contains(e.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(conflictDiscountActivities)) {
                List<String> conflictNames = conflictDiscountActivities.stream().map(DiscountActivity::getName).distinct().collect(Collectors.toList());
                throw new BusinessException("商品SPUID:" + productId + ",SKUID:"+ skuId +" 在限时折扣活动：" + conflictNames + "中已存在");
            }
        }
        List<PtGoods> ptGoods = ptGoodsDao.findByProductId(productId);
        if (CollectionUtils.isNotEmpty(ptGoods)) {
            List<Long> ptActivityIds = ptGoods.stream().map(PtGoods::getPtActivityId).distinct().collect(Collectors.toList());
            List<PtActivity> ptActivities = ptActivityManager.getByIds(ptActivityIds);
            List<PtActivity> conflictPtActivities = ptActivities.stream()
                    .filter(e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()).contains(e.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(conflictPtActivities)) {
                List<String> conflictNames = conflictPtActivities.stream().map(PtActivity::getName).distinct().collect(Collectors.toList());
                throw new BusinessException("商品SPUID:" + productId +" 在拼团活动：" + conflictNames + "中已存在");
            }
        }
    }
}
