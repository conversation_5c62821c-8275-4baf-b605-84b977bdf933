package com.hengtiansoft.operation.freeTrial.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.SkuAttrDao;
import com.hengtiansoft.item.dao.SkuDao;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.DiscountActivity;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.po.SkuAttr;
import com.hengtiansoft.item.entity.vo.ProductSkuListVO;
import com.hengtiansoft.item.manager.DiscountActivityManager;
import com.hengtiansoft.item.manager.DiscountActivityRangeManager;
import com.hengtiansoft.operation.freeTrial.service.FullTrialService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.dao.CouponRuleDao;
import com.hengtiansoft.order.dao.OrderInfoDao;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityDao;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityProductDao;
import com.hengtiansoft.privilege.entity.dto.FullTrialQueryDTO;
import com.hengtiansoft.privilege.entity.dto.FullTrialSaveDTO;
import com.hengtiansoft.privilege.entity.dto.MonitorPageDTO;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.FullTrialDetailVO;
import com.hengtiansoft.privilege.entity.vo.FullTrialDetailVO.Product;
import com.hengtiansoft.privilege.entity.vo.FullTrialExportVO;
import com.hengtiansoft.privilege.entity.vo.FullTrialListVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import com.hengtiansoft.privilege.enums.FreeTrialSubActivityProductTargetTypeEnum;
import com.hengtiansoft.privilege.enums.MoTypeEnum;
import com.hengtiansoft.privilege.manager.MonitorPageManager;
import com.hengtiansoft.privilege.manager.PtActivityManager;
import com.hengtiansoft.privilege.manager.PtGoodsManager;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-08-02 11:52
 **/
@Service
public class FullTrialServiceImpl implements FullTrialService {

    @Autowired
    private FreeTrialSubActivityDao freeTrialSubActivityDao;
    @Autowired
    private FreeTrialSubActivityProductDao freeTrialSubActivityProductDao;

    @Autowired
    private SkuDao skuDao;
    @Autowired
    private CouponRuleDao couponRuleDao;

    @Autowired
    private OrderInfoDao orderInfoDao;

    @Autowired
    private SkuAttrDao skuAttrDao;

    @Autowired
    private MonitorPageManager monitorPageManager;

    @Autowired
    private DiscountActivityManager discountActivityManager;

    @Autowired
    private DiscountActivityRangeManager discountActivityRangeManager;

    @Autowired
    private PtGoodsManager ptGoodsManager;
    @Autowired
    private PtActivityManager ptActivityManager;

    @Autowired
    private MonitorPageService monitorPageService;

    @Override
    public PageVO<FullTrialListVO> getList(FullTrialQueryDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        dto.setActivityType(1);
        List<FreeTrialSubActivity> activityList = freeTrialSubActivityDao.selectPage(dto);
        if (CollectionUtils.isEmpty(activityList)) {
            return PageUtils.emptyPage();
        }
        List<Long> activityIds = activityList.stream().map(FreeTrialSubActivity::getId).collect(Collectors.toList());
        List<FreeTrialSubActivityProduct> activityProductList = freeTrialSubActivityProductDao.selectByActivityIds(activityIds);
        Map<Long, List<FreeTrialSubActivityProduct>> activityProductGroup = activityProductList.stream().collect(Collectors.groupingBy(FreeTrialSubActivityProduct::getFreeTrialSubActivityId));
        Map<Integer, List<FreeTrialSubActivityProduct>> targetTypeGroup = activityProductList.stream().collect(Collectors.groupingBy(FreeTrialSubActivityProduct::getTargetType));
        List<Long> skuIds = Lists.newArrayList();
        List<Long> couponIds = Lists.newArrayList();
        for (Map.Entry<Integer, List<FreeTrialSubActivityProduct>> entry : targetTypeGroup.entrySet()) {
            FreeTrialSubActivityProductTargetTypeEnum targetTypeEnum = FreeTrialSubActivityProductTargetTypeEnum.getByCode(entry.getKey());
            if (targetTypeEnum == FreeTrialSubActivityProductTargetTypeEnum.PRODUCT) {
                skuIds.addAll(entry.getValue().stream().map(FreeTrialSubActivityProduct::getSkuId).collect(Collectors.toList()));
            }
            if (targetTypeEnum == FreeTrialSubActivityProductTargetTypeEnum.COUPON) {
                couponIds.addAll(entry.getValue().stream().map(FreeTrialSubActivityProduct::getTargetId).collect(Collectors.toList()));
            }
        }
        ProductBaseSearchDTO baseSearchDTO = new ProductBaseSearchDTO();
        baseSearchDTO.setSkuIds(skuIds);
        baseSearchDTO.setPageNum(1);
        baseSearchDTO.setPageSize(1000);
        List<ProductSkuListVO> skus = skuDao.selectProductSkuList(baseSearchDTO);
        Map<Long, ProductSkuListVO> skumap = skus.stream().collect(Collectors.toMap(ProductSkuListVO::getSkuId, Function.identity()));
        List<CouponRule> couponRules = couponRuleDao.findByIds(couponIds);
        Map<Long, CouponRule> couponMap = couponRules.stream().collect(Collectors.toMap(CouponRule::getId, Function.identity()));
        List<OrderInfo> orderInfos = orderInfoDao.findByFullTrialIds(activityIds);
        Map<Long, List<OrderInfo>> activityOrdersMap = orderInfos.stream().collect(Collectors.groupingBy(OrderInfo::getFullTrialId));
        List<FullTrialListVO> resultList = Lists.newArrayList();
        activityList.forEach(e -> {
            Long activityId = e.getId();
            List<FreeTrialSubActivityProduct> freeTrialSubActivityProducts = activityProductGroup.get(activityId);
            if (CollectionUtils.isEmpty(freeTrialSubActivityProducts)) {
                throw new BusinessException("活动:" + e.getName() +" 下没有商品");
            }
            FullTrialListVO vo = new FullTrialListVO();
            vo.setIsPublic(e.getIsPublic());
            vo.setId(activityId);
            vo.setName(e.getName());
            // 赠品信息
            freeTrialSubActivityProducts.stream().filter(l -> l.getType() == 2).findFirst().ifPresent(target -> {
                if (target.getTargetType() == 1) {
                    ProductSkuListVO sku = skumap.get(target.getSkuId());
                    vo.setFreePicUrl(StringUtils.isEmpty(sku.getSkuPicUrl())? sku.getPicUrl() : sku.getSkuPicUrl());
                    vo.setFreeProductName(sku.getProductName());
                    vo.setFreeSkuCode(sku.getSkuCode());
                    vo.setFreeSalePrice(sku.getSalePrice());
                } else {
                    CouponRule couponInfo = couponMap.get(target.getTargetId());
                    vo.setFreePicUrl(couponInfo.getCouponUrl());
                    vo.setFreeProductName(couponInfo.getCouponName());
                }
            });

            // 主品信息
            freeTrialSubActivityProducts.stream().filter(l -> l.getType() == 1).findFirst().ifPresent(product -> {
                ProductSkuListVO sku = skumap.get(product.getSkuId());
                if(null != sku){
                    vo.setTrialPicUrl(StringUtils.isEmpty(sku.getSkuPicUrl())? sku.getPicUrl() : sku.getSkuPicUrl());
                    vo.setTrialProductName(sku.getProductName());
                    vo.setTrialSkuCode(sku.getSkuCode());
                    vo.setTrialSalePrice(sku.getSalePrice());
                    vo.setActivityPrice(product.getPrice());
                    vo.setStock(sku.getStock());
                }
            });
            List<OrderInfo> activityOrders = activityOrdersMap.get(activityId);
            vo.setPayUserCnt(0);
            vo.setOrderCnt(0);
            vo.setTotalGmv(new BigDecimal("0"));
            if (CollectionUtils.isNotEmpty(activityOrders)) {
                vo.setPayUserCnt((int) activityOrders.stream().map(OrderInfo::getUserId).distinct().count());
                vo.setOrderCnt(activityOrders.size());
                vo.setTotalGmv(activityOrders.stream().map(OrderInfo::getRealAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            vo.setNoticeTime(e.getNoticeTime());
            vo.setStartTime(e.getStartTime());
            vo.setEndTime(e.getEndTime());
            vo.setUpdateTime(e.getUpdateTime());
            vo.setStatus(e.getStatus());

            resultList.add(vo);
        });
        return PageUtils.toPageVO(resultList);
    }

    @Override
    @Transactional(rollbackFor =Exception.class)
    public void save(FullTrialSaveDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        Long id = dto.getId();
        Date now = new Date();
        validateSelf(dto);
        validateMarketingProduct(dto.getTrialProduct());
        if (null == id) {
            FreeTrialSubActivity activity = new FreeTrialSubActivity();
            activity.setName(dto.getName());
            activity.setNoticeTime(dto.getNoticeTime());
            Date startTime = dto.getStartTime();
            Date endTime = dto.getEndTime();
            activity.setStartTime(startTime);
            activity.setEndTime(endTime);
            if (startTime.after(now)) {
                activity.setStatus(1);
            } else if (endTime.before(now)) {
                activity.setStatus(3);
            } else {
                activity.setStatus(2);
            }
            activity.setIsPublic(dto.getIsPublic());
            activity.setPeopleLimit(dto.getPeopleLimit());
            if(CollectionUtils.isNotEmpty(dto.getLabelIds())){
                activity.setLabelId(StringUtils.join(dto.getLabelIds(), ","));
            }
            if(CollectionUtils.isNotEmpty(dto.getGradeList())){
                activity.setGrade(String.join(",", dto.getGradeList()));
            }
            activity.setShareDiscount(dto.getShareDiscount());
            activity.setOperator(user.getUserName());
            activity.setCreateTime(now);
            activity.setUpdateTime(now);
            activity.setDelflag(0);
            activity.setActivityType(1);
            freeTrialSubActivityDao.insert(activity);
            id = activity.getId();

            //推广
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(dto.getName()+"_满额试用");
            monitorPage.setTargetId(id);
            monitorPage.setType(MoTypeEnum.FULL_TRIAL.getCode());
            monitorPage.setTargetName(dto.getName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
        } else {
            FreeTrialSubActivity activity = freeTrialSubActivityDao.findById(id);
            if (null == activity) {
                throw new BusinessException("该活动不存在");
            }
            activity.setName(dto.getName());
            activity.setNoticeTime(dto.getNoticeTime());
            Date startTime = dto.getStartTime();
            Date endTime = dto.getEndTime();
            activity.setStartTime(startTime);
            activity.setEndTime(endTime);
            if (startTime.after(now)) {
                activity.setStatus(1);
            } else if (endTime.before(now)) {
                activity.setStatus(3);
            } else {
                activity.setStatus(2);
            }
            activity.setIsPublic(dto.getIsPublic());
            activity.setPeopleLimit(dto.getPeopleLimit());
            if(CollectionUtils.isNotEmpty(dto.getLabelIds())){
                activity.setLabelId(StringUtils.join(dto.getLabelIds(), ","));
            }
            if(CollectionUtils.isNotEmpty(dto.getGradeList())){
                activity.setGrade(String.join(",", dto.getGradeList()));
            } else {
                activity.setGrade("");
            }
            activity.setShareDiscount(dto.getShareDiscount());
            activity.setOperator(user.getUserName());
            activity.setUpdateTime(now);
            freeTrialSubActivityDao.updateAll(activity);
        }
        FullTrialSaveDTO.Product trialProduct = dto.getTrialProduct();
        FullTrialSaveDTO.Product freeProduct = dto.getFreeProduct();
        freeTrialSubActivityProductDao.deleteByActivityId(id);
        FreeTrialSubActivityProduct trial = new FreeTrialSubActivityProduct();
        trial.setFreeTrialSubActivityId(id);
        trial.setType(1);
        trial.setTargetType(1);
        trial.setTargetId(trialProduct.getProductId());
        trial.setSkuId(trialProduct.getSkuId());
        trial.setPrice(trialProduct.getActivityPrice());
        trial.setOperator(user.getUserName());
        trial.setCreateTime(now);
        trial.setUpdateTime(now);
        trial.setDelflag(0);
        freeTrialSubActivityProductDao.insert(trial);

        FreeTrialSubActivityProduct free = new FreeTrialSubActivityProduct();
        free.setFreeTrialSubActivityId(id);
        free.setType(2);
        free.setTargetType(1);
        free.setTargetId(freeProduct.getProductId());
        free.setSkuId(freeProduct.getSkuId());
        free.setPrice(freeProduct.getShowPrice());
        free.setOperator(user.getUserName());
        free.setCreateTime(now);
        free.setUpdateTime(now);
        free.setDelflag(0);
        freeTrialSubActivityProductDao.insert(free);
    }

    private void validateMarketingProduct(FullTrialSaveDTO.Product trialProduct) {
        Long productId = trialProduct.getProductId();
        Long skuId = trialProduct.getSkuId();
        List<DiscountActivityRange> discountRanges = discountActivityRangeManager.findBySkuId(skuId);
        if (CollectionUtils.isNotEmpty(discountRanges)) {
            List<Long> discountActivityIds = discountRanges.stream().map(DiscountActivityRange::getDiscountActivityId).distinct().collect(Collectors.toList());
            List<DiscountActivity> discountActivities = discountActivityManager.getByIds(discountActivityIds);
            List<DiscountActivity> conflictDiscountActivities = discountActivities.stream()
                    .filter(e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()).contains(e.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(conflictDiscountActivities)) {
                List<String> conflictNames = conflictDiscountActivities.stream().map(DiscountActivity::getName).distinct().collect(Collectors.toList());
                throw new BusinessException("商品skuId:" + skuId + "在限时折扣活动：" + conflictNames + "中已存在");
            }
        }
        List<PtGoods> ptGoods = ptGoodsManager.findByProductId(productId);
        if (CollectionUtils.isNotEmpty(ptGoods)) {
            List<Long> ptActivityIds = ptGoods.stream().map(PtGoods::getPtActivityId).distinct().collect(Collectors.toList());
            List<PtActivity> ptActivities = ptActivityManager.getByIds(ptActivityIds);
            List<PtActivity> conflictPtActivities = ptActivities.stream()
                    .filter(e -> Lists.newArrayList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()).contains(e.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(conflictPtActivities)) {
                List<String> conflictNames = conflictPtActivities.stream().map(PtActivity::getName).distinct().collect(Collectors.toList());
                throw new BusinessException("商品id" + productId + "在拼团活动：" + conflictNames + "中已存在");
            }
        }
    }

    private void validateSelf(FullTrialSaveDTO dto) {
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();
        FullTrialSaveDTO.Product trialProduct = dto.getTrialProduct();
        FullTrialSaveDTO.Product freeProduct = dto.getFreeProduct();
        if (Objects.nonNull(freeProduct) && freeProduct.getShowPrice().compareTo(new BigDecimal("0.01")) < 0) {
            throw new BusinessException("试用奖品建议零售价不能小于0.01");
        }
        if (Objects.nonNull(trialProduct) && trialProduct.getActivityPrice().compareTo(new BigDecimal("0.01")) < 0) {
            throw new BusinessException("指定商品活动价不能小于0.01");
        }
        if(!dto.getNoticeTime().before(dto.getStartTime())){
            throw new BusinessException("预告时间需在活动开始时间之前");
        }
        if (startTime.after(endTime)) {
            throw new BusinessException("开始时间不能大于结束时间");
        }
        Long productId = trialProduct.getProductId();
        FreeTrialSubActivity trialSubActivity =  freeTrialSubActivityDao.getByProductIdAndTime(productId, startTime, endTime);
        if ((Objects.isNull(dto.getId()) && Objects.nonNull(trialSubActivity)) ||
                (Objects.nonNull(dto.getId()) && Objects.nonNull(trialSubActivity) && !trialSubActivity.getId().equals(dto.getId()))) {
            throw new BusinessException("该商品在开始结束时间内与活动:" + trialSubActivity.getName() + "重合");
        }
    }

    @Override
    public FullTrialDetailVO get(Long id) {
        FreeTrialSubActivity activity = freeTrialSubActivityDao.findById(id);
        if (null == activity) {
            throw new BusinessException("该活动不存在");
        }
        List<FreeTrialSubActivityProduct> activityProductList = freeTrialSubActivityProductDao.selectByActivityIds(Lists.newArrayList(id));
        Map<Integer, List<FreeTrialSubActivityProduct>> mainOrGiftProductGroup = activityProductList.stream().collect(Collectors.groupingBy(FreeTrialSubActivityProduct::getType));

        List<Long> skuIds = activityProductList.stream().map(FreeTrialSubActivityProduct::getSkuId).collect(Collectors.toList());
        ProductBaseSearchDTO baseSearchDTO = new ProductBaseSearchDTO();
        baseSearchDTO.setSkuIds(skuIds);
        baseSearchDTO.setPageNum(1);
        baseSearchDTO.setPageSize(1000);
        List<ProductSkuListVO> skus = skuDao.selectProductSkuList(baseSearchDTO);
        Map<Long, ProductSkuListVO> skumap = skus.stream().collect(Collectors.toMap(ProductSkuListVO::getSkuId, Function.identity()));

        FullTrialDetailVO detailVO = new FullTrialDetailVO();
        detailVO.setId(activity.getId());
        detailVO.setName(activity.getName());
        detailVO.setNoticeTime(activity.getNoticeTime());
        detailVO.setStartTime(activity.getStartTime());
        detailVO.setEndTime(activity.getEndTime());
        detailVO.setPeopleLimit(activity.getPeopleLimit());
        if(StringUtils.isNotBlank(activity.getLabelId())){
            detailVO.setLabelIds(Arrays.stream(activity.getLabelId().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        detailVO.setGrade(activity.getGrade());
        if (StringUtils.isNotEmpty(activity.getGrade())) {
            detailVO.setGradeList(Arrays.asList(activity.getGrade().split(",")));
        }
        detailVO.setIsPublic(activity.getIsPublic());
        detailVO.setShareDiscount(activity.getShareDiscount());
        detailVO.setUpdateTime(activity.getUpdateTime());
        detailVO.setCreateTime(activity.getCreateTime());
        detailVO.setStatus(activity.getStatus());

        List<FreeTrialSubActivityProduct> trialProducts = mainOrGiftProductGroup.get(1);
        if (CollectionUtils.isEmpty(trialProducts)) {
            throw new BusinessException("该活动异常，无绑定主品信息");
        }
        FreeTrialSubActivityProduct dbTrialProduct = trialProducts.get(0);
        ProductSkuListVO productSkuListVO = skumap.get(dbTrialProduct.getSkuId());
        if (null == productSkuListVO) {
            throw new BusinessException("主品信息不存在");
        }
        Product trialProduct = new Product();
        trialProduct.setId(dbTrialProduct.getId());
        trialProduct.setProductId(dbTrialProduct.getTargetId());
        trialProduct.setPicUrl(productSkuListVO.getPicUrl());
        trialProduct.setProductName(productSkuListVO.getProductName());
        trialProduct.setSpecValueList(productSkuListVO.getSpecValueList());
        trialProduct.setSkuCode(productSkuListVO.getSkuCode());
        trialProduct.setSkuId(productSkuListVO.getSkuId());
        trialProduct.setStock(productSkuListVO.getStock());
        trialProduct.setSaleStatus(productSkuListVO.getSaleStatus());
        trialProduct.setSalePrice(productSkuListVO.getSalePrice());
//        trialProduct.setShowPrice(productSkuListVO.getListPrice());
        trialProduct.setActivityPrice(dbTrialProduct.getPrice());
        trialProduct.setIsPublic(productSkuListVO.getEnableShow());

        List<SkuAttr> skuAttrs = skuAttrDao.selectBySkuId(dbTrialProduct.getSkuId());
        List<FullTrialDetailVO.Cycle> cycleList = Lists.newArrayList();
        skuAttrs.stream().filter(e -> e.getAttrName().equals("times")).forEach(el -> {
            FullTrialDetailVO.Cycle cycle = new FullTrialDetailVO.Cycle();
            cycle.setTimes(Integer.valueOf(el.getAttrValue()));
            cycle.setPrice(new BigDecimal(el.getAttrSpecValue()));
            cycleList.add(cycle);
        });
        trialProduct.setCycleList(cycleList);
        detailVO.setTrialProduct(trialProduct);



        List<FreeTrialSubActivityProduct> freeProducts = mainOrGiftProductGroup.get(2);
        if (CollectionUtils.isEmpty(freeProducts)) {
            throw new BusinessException("该活动异常，无绑定赠品信息");
        }
        FreeTrialSubActivityProduct dbFreeProduct = freeProducts.get(0);
        ProductSkuListVO freeProductSkuListVO = skumap.get(dbFreeProduct.getSkuId());
        if (null == freeProductSkuListVO) {
            throw new BusinessException("赠品信息不存在");
        }

        Product freeProduct = new Product();
        freeProduct.setId(dbFreeProduct.getId());
        freeProduct.setProductId(dbFreeProduct.getTargetId());
        freeProduct.setPicUrl(freeProductSkuListVO.getPicUrl());
        freeProduct.setProductName(freeProductSkuListVO.getProductName());
        freeProduct.setSpecValueList(freeProductSkuListVO.getSpecValueList());
        freeProduct.setSkuCode(freeProductSkuListVO.getSkuCode());
        freeProduct.setSkuId(dbFreeProduct.getSkuId());
        freeProduct.setStock(freeProductSkuListVO.getStock());
        freeProduct.setSaleStatus(freeProductSkuListVO.getSaleStatus());
        freeProduct.setSalePrice(freeProductSkuListVO.getSalePrice());
        freeProduct.setShowPrice(dbFreeProduct.getPrice());
        detailVO.setFreeProduct(freeProduct);

        return detailVO;
    }

    @Override
    public PromoVO promotion(Long id) {
        FreeTrialSubActivity activity = freeTrialSubActivityDao.findById(id);
        if(null == activity){
            throw new BusinessException("活动不存在！");
        }
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.FULL_TRIAL.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        PromoVO promoVO = new PromoVO();
        promoVO.setId(id);

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        FreeTrialSubActivity activity = freeTrialSubActivityDao.findById(id);
        if(null == activity){
            throw new BusinessException("活动不存在！");
        }
        if (activity.getStatus().equals(CommonActivityStatusEnum.IN_PROGRESS.getCode())) {
            throw new BusinessException("活动进行中，不能删除！");
        }
        freeTrialSubActivityDao.deleteById(id);
        freeTrialSubActivityProductDao.deleteByActivityId(id);
    }

    @Override
    public void end(Long id) {
        FreeTrialSubActivity activity = freeTrialSubActivityDao.findById(id);
        Assert.notNull(activity, "活动不存在");
        if(Objects.equals(activity.getStatus(), CommonActivityStatusEnum.END.getCode())){
            return;
        }
        if(CommonActivityStatusEnum.NOT_STARTED.getCode().equals(activity.getStatus())){
            throw new BusinessException("未开始的活动不能直接结束！");
        }
        FreeTrialSubActivity updateActivity = new FreeTrialSubActivity();
        updateActivity.setId(id);
        updateActivity.setEndTime(new Date());
        updateActivity.setStatus(CommonActivityStatusEnum.END.getCode());
        freeTrialSubActivityDao.updateById(updateActivity);
    }

    @Override
    public void exportWaitList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FullTrialExportVO.class).build();
        FullTrialQueryDTO queryDTO = new FullTrialQueryDTO();
        queryDTO.setId(dto.getRecordId());
        queryDTO.setName(dto.getName());
        queryDTO.setStatus(dto.getStatus());
        queryDTO.setUpdateTimeStart(dto.getUpdateTimeStart());
        queryDTO.setUpdateTimeEnd(dto.getUpdateTimeEnd());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<FullTrialListVO> fullTrialListVOPageVO = this.getList(queryDTO);
            Pagination pagination = fullTrialListVOPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FullTrialExportVO> exportVOList = BeanUtils.copyList(fullTrialListVOPageVO.getList(), FullTrialExportVO::new);
            exportVOList.forEach(e -> {
                e.setStatusStr(CommonActivityStatusEnum.getDescByCode(e.getStatus()));
                e.setIsPublicStr(e.getIsPublic() == 0?"未公开":"公开");
            });
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }
}
