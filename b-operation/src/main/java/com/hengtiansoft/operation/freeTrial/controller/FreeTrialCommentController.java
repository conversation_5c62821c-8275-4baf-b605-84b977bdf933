package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialCommentService;
import com.hengtiansoft.privilege.entity.dto.FreeTrialCommentPrizeDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialCommentSearchDTO;
import com.hengtiansoft.operation.freeTrial.entity.FreeTrialCommentPrizeVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialCommentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "零元试用测评")
@RestController
@RequestMapping("/freeTrial/comment")
public class FreeTrialCommentController {

    @Resource
    private FreeTrialCommentService freeTrialCommentService;

    @ApiOperation(value = "分页")
    @PostMapping("/page")
    public Response<PageVO<FreeTrialCommentVO>> page(@RequestBody FreeTrialCommentSearchDTO dto) {
        return ResponseFactory.success(freeTrialCommentService.page(dto));
    }

    @ApiOperation(value = "隐藏")
    @GetMapping("/hide")
    public Response<Void> hide(@RequestParam Long id) {
        freeTrialCommentService.hide(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "显示")
    @GetMapping("/show")
    public Response<Void> show(@RequestParam Long id) {
        freeTrialCommentService.show(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "置顶")
    @GetMapping("/top")
    public Response<Void> top(@RequestParam Long id) {
        freeTrialCommentService.top(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "取消置顶")
    @GetMapping("/cancel")
    public Response<Void> cancel(@RequestParam Long id) {
        freeTrialCommentService.cancel(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/get")
    public Response<FreeTrialCommentVO> get(@RequestParam Long id) {
        return ResponseFactory.success(freeTrialCommentService.get(id));
    }


    @ApiOperation(value = "热门测评")
    @PostMapping("/hot")
    public Response<PageVO<FreeTrialCommentVO>> hot(@RequestBody FreeTrialCommentSearchDTO dto) {
        return ResponseFactory.success(freeTrialCommentService.hot(dto));
    }

    @ApiOperation(value = "奖励设置")
    @PostMapping("/prize/save")
    public Response<Void> prizeSave(@RequestBody FreeTrialCommentPrizeDTO dto) {
        freeTrialCommentService.prizeSave(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "奖励详情")
    @GetMapping("/prize/get")
    public Response<FreeTrialCommentPrizeVO> prizeGet() {
        return ResponseFactory.success(freeTrialCommentService.prizeGet());
    }
}
