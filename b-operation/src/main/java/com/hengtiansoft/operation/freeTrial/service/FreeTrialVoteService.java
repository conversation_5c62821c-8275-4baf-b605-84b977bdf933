package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.vo.*;

public interface FreeTrialVoteService {

    PageVO<FreeTrialVoteVO> getList(FreeTrialVoteDTO dto);

    FreeTrialVoteDetailVO get(Long id);

    void save(FreeTrialVoteSaveDTO dto);

    void end(Long id);

    void delete(Long id);

    PromoVO promotion(Long id);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    PageVO<FreeTrialVoteFreeProductVO> getFreeProductList(FreeTrialVoteFreeProductDTO dto);

    PageVO<FreeTrialVoteUserVO> getUserList(FreeTrialVoteUserDTO dto);

    void exportFreeProductList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    void exportUserList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);
}
