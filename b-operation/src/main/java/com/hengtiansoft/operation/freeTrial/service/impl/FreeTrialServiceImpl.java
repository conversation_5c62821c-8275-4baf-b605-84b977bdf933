package com.hengtiansoft.operation.freeTrial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.*;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.dto.SkuProductBaseDTO;
import com.hengtiansoft.item.entity.dto.StockCalculateDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialService;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.entity.po.CouponStats;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.CouponStatusTypeEnum;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.manager.CouponStatsManager;
import com.hengtiansoft.privilege.dao.FreeTrialPrizeDao;
import com.hengtiansoft.privilege.dao.MessageSubscribeDao;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.FreeProductTypeEnum;
import com.hengtiansoft.privilege.enums.MoTypeEnum;
import com.hengtiansoft.privilege.enums.MsgSendStatusEnum;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.privilege.util.FreeTrialConfigUtil;
import com.hengtiansoft.privilege.util.FreeTrialUtil;
import com.hengtiansoft.privilege.util.FreeTrialVoteUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.enumeration.MessageSubscribeEnum;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import com.hengtiansoft.user.dao.CustomerUserDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FreeTrialServiceImpl implements FreeTrialService {
    @Resource
    private FreeTrialManager freeTrialManager;
    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private MonitorPageManager monitorPageManager;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private ProductManager productManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private FreeTrialConfigManager freeTrialConfigManager;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private StockManager stockManager;
    @Resource
    private FreeTrialStatsManager freeTrialStatsManager;
    @Resource
    private FreeTrialStatsItemManager freeTrialStatsItemManager;
    @Resource
    private MessageSubscribeDao messageSubscribeDao;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private FreeTrialPrizeDao freeTrialPrizeDao;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private FreeTrialVoteManager freeTrialVoteManager;
    @Resource
    private MilkProducerService milkProducerService;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private CouponStatsManager couponStatsManager;

    @Override
    public PageVO<FreeTrialVO> getList(FreeTrialDTO dto) {
        dto.setActivityType(1);
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrial> freeTrialList = freeTrialManager.findByCondition(dto);
        List<Long> ids = StreamUtils.toList(freeTrialList, FreeTrial::getId);
        List<FreeTrialProduct> itemPos = freeTrialProductManager.findByFreeTrialIds(ids, null);
        Map<Long, List<FreeTrialProduct>> itemMap = StreamUtils.group(itemPos, FreeTrialProduct::getFreeTrialId);
        if (CollectionUtils.isEmpty(freeTrialList)) {
            return PageUtils.toPageVO(Lists.newArrayList());
        }
        List<Long> spuIds = StreamUtils.filterConvert(itemPos, FreeProductTypeEnum::isTrial, FreeTrialProduct::getProductId);
        List<Product> productList = productManager.findByIds(spuIds);
        Map<Long, Product> productMap = StreamUtils.toMap(productList, Product::getId);

        List<Long> skuIds = StreamUtils.filterConvert(itemPos, FreeProductTypeEnum::isFree, FreeTrialProduct::getSkuId);
        List<SkuProductBaseDTO> skuBaseDTOList = skuManager.skuProductWithStockList(skuIds);
        Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuBaseDTOList, SkuProductBaseDTO::getId);

        List<FreeTrialStats> freeTrialStatsList = freeTrialStatsManager.findByFreeTrialIds(ids);
        Map<Long, FreeTrialStats> freeTrialStatsMap = StreamUtils.toMap(freeTrialStatsList, FreeTrialStats::getFreeTrialId);

        List<FreeTrialPrize> noDrawPrizeList = freeTrialPrizeDao.getNoDrawPrizeList(ids);
        List<Long> allRuleIds = new ArrayList<>();
        for (FreeTrialPrize freeTrialPrize : noDrawPrizeList) {
            FreeTrialPrizeVO prize = JSON.parseObject(freeTrialPrize.getRule(), FreeTrialPrizeVO.class);
            if(null != prize && CollectionUtils.isNotEmpty(prize.getCouponRuleIds())){
                allRuleIds.addAll(prize.getCouponRuleIds());
            }
        }
        Map<Long, CouponStats> couponStatsMap = StreamUtils.toMap(couponStatsManager.getByCouponRuleIdList(allRuleIds), CouponStats::getCouponRuleId);
        Map<Long, CouponRule> couponRuleMap = StreamUtils.toMap(couponRuleManager.findByIds(allRuleIds), CouponRule::getId);
        List<FreeTrialVO> list = new ArrayList<>();
        for (FreeTrialPrize freeTrialPrize : noDrawPrizeList) {
            FreeTrialVO freeTrialVO = new FreeTrialVO();
            freeTrialVO.setId(freeTrialPrize.getFreeTrialId());
            BigDecimal couponPayAmount = BigDecimal.ZERO;
            Long couponRemaining = 0L;
            FreeTrialPrizeVO prize = JSON.parseObject(freeTrialPrize.getRule(), FreeTrialPrizeVO.class);
            if(null != prize && CollectionUtils.isNotEmpty(prize.getCouponRuleIds())){
                for (Long couponRuleId : prize.getCouponRuleIds()) {
                    CouponStats couponStats = couponStatsMap.get(couponRuleId);
                    CouponRule couponRule = couponRuleMap.get(couponRuleId);
                    if(null != couponStats){
                        couponPayAmount = couponPayAmount.add(couponStats.getTotalAmount());
                    }
                    if(null != couponRule){
                        couponRemaining = couponRemaining + (couponRule.getIssuedQuantity() - couponRule.getReceiveCount().longValue());
                    }
                }
            }
            freeTrialVO.setCouponPayAmount(couponPayAmount);
            freeTrialVO.setCouponRemaining(couponRemaining);
            list.add(freeTrialVO);
        }
        Map<Long, FreeTrialVO> voMap = StreamUtils.toMap(list, FreeTrialVO::getId);


        return PageUtils.convert(freeTrialList, data -> {
            FreeTrialVO vo = FreeTrialUtil.convert2VO(data);
            List<FreeTrialProduct> items = itemMap.get(data.getId());
            if(CollectionUtils.isNotEmpty(items)){
                vo.setTrialProductList(Lists.newArrayList());
                for (FreeTrialProduct item : items) {
                    FreeTrialProductVO itemVO =  FreeTrialUtil.convert2ProductVO(item);
                    FreeProductTypeEnum typeEnum = FreeProductTypeEnum.getEnum(item.getType());
                    if(typeEnum == FreeProductTypeEnum.FREE){
                        // sku
                        SkuProductBaseDTO sku = skuMap.get(item.getSkuId());
                        if(null != sku){
                            itemVO.setPicUrl(sku.getPicUrl());
                            itemVO.setSkuCode(sku.getSkuCode());
                            itemVO.setProductName(sku.getProductName());
                            itemVO.setSpecValueList(sku.getSpecValueList());
                            itemVO.setStockNum(sku.getStock());
                            itemVO.setSaleStatus(sku.getSaleStatus());
                            itemVO.setListPrice(sku.getListPrice());
                            itemVO.setSalePrice(sku.getSalePrice());
                            vo.setFreeProduct(itemVO);
                        }
                    }
                    if(typeEnum == FreeProductTypeEnum.TRIAL){
                        // spu
                        Product product = productMap.get(item.getProductId());
                        if(null != product){
                            itemVO.setPicUrl(product.getPicUrl());
                            itemVO.setProductName(product.getProductName());
                            itemVO.setSaleStatus(product.getSaleStatus());
                            vo.getTrialProductList().add(itemVO);
                        }
                    }
                }
            }
            FreeTrialStats freeTrialStats = freeTrialStatsMap.get(data.getId());
            vo.setGmvBindProduct(BigDecimal.ZERO);
            vo.setActivityGmv(BigDecimal.ZERO);
            if(null != freeTrialStats){
                vo.setGmvBindProduct(freeTrialStats.getTotalAmount());
                vo.setActivityGmv(freeTrialStats.getActivityGmv());
            }
            FreeTrialVO freeTrialVO = voMap.get(data.getId());
            if(null != freeTrialVO){
                vo.setCouponRemaining(freeTrialVO.getCouponRemaining());
                vo.setCouponPayAmount(freeTrialVO.getCouponPayAmount());
            }
            return vo;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(FreeTrialDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        Long freeTrialId = null;
        dto.setOperator(user.getUserName());
        dto.setActivityType(1);
        // 数据处理
        handleParram(dto);
        // 数据校验
        validParam(dto);
        Integer status = CommonOptUtil.getStatus(dto.getStartTime(), dto.getEndTime()).getCode();
        if(!Objects.equals(status, CommonActivityStatusEnum.END.getCode())){
            // 校验活动时间重复
            List<FreeTrial> existPos = freeTrialManager.findBySkuId(dto.getFreeProduct().getSkuId(),
                    Arrays.asList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()));
            for (FreeTrial existPo : existPos) {
                if(Objects.equals(existPo.getId(), dto.getId())){
                    continue;
                }
                if (DateUtil.isOverlap(dto.getStartTime(), dto.getEndTime(), existPo.getStartTime(), existPo.getEndTime())) {
                    throw new BusinessException("该活动【"+dto.getName()+"】的时间段与名称为【" + existPo.getName() + "】的有重复，请修改");
                }
            }
        }
        FreeTrial po = FreeTrialUtil.buildPO(dto);
        po.setStatus(CommonOptUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
        FreeTrial old = null;
        //未中奖奖励设置
        FreeTrialPrize prize = FreeTrialUtil.buildFreeTrialPrize(dto);
        checkCoupon(po, dto);
        prize.setOperator(user.getUserName());
        if(Objects.isNull(dto.getId())){
            // 无id，新增
            freeTrialManager.insert(po);
            // 库存更新
            StockCalculateDTO stockdto = new StockCalculateDTO();
            stockdto.setSkuId(dto.getFreeProduct().getSkuId());
            stockdto.setStockNum(Long.valueOf(dto.getTotalCnt()));
            if (!stockManager.decrease(Collections.singletonList(stockdto))) {
                throw new BusinessException("商品库存信息更新失败!");
            }
            //推广
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(dto.getName()+"_免费试用");
            monitorPage.setTargetId(po.getId());
            monitorPage.setType(MoTypeEnum.FREE_TRIAL.getCode());
            monitorPage.setTargetName(dto.getName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
            prize.setFreeTrialId(po.getId());
            freeTrialPrizeDao.insert(prize);
            freeTrialId = po.getId();
        }else{
            old = freeTrialManager.findByIdDetail(dto.getId());
            Assert.notNull(old, "活动不存在");
            freeTrialId = dto.getId();
            redisOperation.hdel(FreeTrialUtil.STATS, dto.getId()+"");
            Integer oldCount = old.getTotalCnt();
            if(!Objects.equals(old.getStatus(), CommonActivityStatusEnum.END.getCode())){
                // 有id，更新
                // 更新换成了updateByPrimaryKey，就不能把null值赋给product了，不然会把数据库的值覆盖掉
                BeanUtil.copyProperties(po, old, CopyOptions.create().setIgnoreNullValue(true));
                old.setUpdateTime(new Date());
                old.setGrade(po.getGrade());
                old.setSupportPoint(po.getSupportPoint());
                old.setRecentDrawCnt(po.getRecentDrawCnt());
                old.setTotalDrawCnt(po.getTotalDrawCnt());
                freeTrialManager.updateAll(old);
                freeTrialPrizeDao.deleteByFreeTrialId(dto.getId());
                freeTrialPrizeDao.insert(prize);
                // 库存更新
                if(Objects.equals(old.getFreeProduct().getSkuId(), dto.getFreeProduct().getSkuId())){
                    int count = dto.getTotalCnt() - oldCount;
                    if(count > 0){
                        StockCalculateDTO stockdto = new StockCalculateDTO();
                        stockdto.setSkuId(dto.getFreeProduct().getSkuId());
                        stockdto.setStockNum(Long.valueOf(count));
                        if (!stockManager.decrease(Collections.singletonList(stockdto))) {
                            throw new BusinessException("商品库存信息更新失败!");
                        }
                    }else if(count < 0){
                        StockCalculateDTO stockdto = new StockCalculateDTO();
                        stockdto.setSkuId(dto.getFreeProduct().getSkuId());
                        stockdto.setStockNum(Long.valueOf(Math.abs(count)));
                        if (!stockManager.increase(Collections.singletonList(stockdto))) {
                            throw new BusinessException("商品库存信息更新失败!");
                        }
                    }
                }else{
                    StockCalculateDTO stockdto = new StockCalculateDTO();
                    stockdto.setSkuId(dto.getFreeProduct().getSkuId());
                    stockdto.setStockNum(Long.valueOf(dto.getTotalCnt()));
                    if (!stockManager.decrease(Collections.singletonList(stockdto))) {
                        throw new BusinessException("商品库存信息更新失败!");
                    }
                    stockdto.setSkuId(old.getFreeProduct().getSkuId());
                    stockdto.setStockNum(Long.valueOf(oldCount));
                    if (!stockManager.increase(Collections.singletonList(stockdto))) {
                        throw new BusinessException("商品库存信息更新失败!");
                    }
                }
            }else{
                old.setUpdateTime(new Date());
                freeTrialManager.updateAll(old);
            }
        }
        // 查看商品信息
        List<Long> skuIds = Lists.newArrayList();
        if(dto.getFreeProduct() != null && dto.getFreeProduct().getSkuId() != null){
            skuIds.add(dto.getFreeProduct().getSkuId());
        }
        List<Sku> skus = skuManager.selectByIds(skuIds);
        Map<Long, Sku> skuMap = StreamUtils.toMap(skus, Sku::getId);
        if(Objects.isNull(old) || !Objects.equals(old.getStatus(), CommonActivityStatusEnum.END.getCode())){
            // 数据处理
            List<FreeTrialProduct> itemPos =  FreeTrialUtil.buildItemPO(dto, po, false, skuMap);
            // 删除旧明细
            freeTrialProductManager.deleteByFreeTrialId(po.getId());
            // 明细新增
            if(CollectionUtils.isNotEmpty(itemPos)){
                freeTrialProductManager.insertList(itemPos);
            }
        }else{
            // 活动结束后只允许修改绑定商品
            // 数据处理
            List<FreeTrialProduct> itemPos =  FreeTrialUtil.buildItemPO(dto, po, true, skuMap);
            // 删除旧明细
            freeTrialProductManager.deleteByFreeTrialIdAndType(po.getId(), FreeProductTypeEnum.TRIAL.getCode());
            // 明细新增
            if(CollectionUtils.isNotEmpty(itemPos)){
                freeTrialProductManager.insertList(itemPos);
            }
        }
        return freeTrialId;
    }

    private void checkCoupon(FreeTrial po, FreeTrialDTO dto) {
        if(CollectionUtils.isEmpty(dto.getCouponRuleIds())){
            return;
        }
        if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.END.getCode())){
            return;
        }
        List<CouponRule> couponRuleList = couponRuleManager.findByIds(dto.getCouponRuleIds());
        for (CouponRule couponRule : couponRuleList) {
            if(Objects.equals(couponRule.getStatus(), CouponStatusTypeEnum.OFFLINE.getCode())){
                throw new BusinessException("优惠券【"+couponRule.getCouponName()+"】已下线，请重新选择");
            }
            Long remainingNum = couponRule.getIssuedQuantity() - couponRule.getReceiveCount().longValue();
            if(remainingNum < 5000){
                throw new BusinessException("优惠券【"+couponRule.getCouponName()+"】库存不足，请重新设置");
            }
        }
    }

    @Override
    public void saveForVote(FreeTrialDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        dto.setOperator(user.getUserName());
        dto.setId(dto.getFreeTrialId());
        dto.setTrialProductList(new ArrayList<>());
        dto.setActivityType(2);
        // 数据处理
        handleParram(dto);
        // 数据校验
        validParam(dto);
        FreeTrial po = FreeTrialUtil.buildPO(dto);
        po.setStatus(CommonOptUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
        FreeTrial old = null;
        //未中奖奖励设置
        FreeTrialPrize prize = FreeTrialUtil.buildFreeTrialPrize(dto);
        prize.setOperator(user.getUserName());
        if(Objects.isNull(dto.getId())){
            // 无id，新增
            freeTrialManager.insert(po);
            dto.setFreeTrialId(po.getId());
            prize.setFreeTrialId(po.getId());
            freeTrialPrizeDao.insert(prize);
        }else{
            old = freeTrialManager.findByIdDetail(dto.getId());
            Assert.notNull(old, "活动不存在");
            // 有id，更新
            // 更新换成了updateByPrimaryKey，就不能把null值赋给product了，不然会把数据库的值覆盖掉
            BeanUtil.copyProperties(po, old, CopyOptions.create().setIgnoreNullValue(true));
            old.setUpdateTime(new Date());
            old.setGrade(po.getGrade());
            old.setSupportPoint(po.getSupportPoint());
            old.setRecentDrawCnt(po.getRecentDrawCnt());
            old.setTotalDrawCnt(po.getTotalDrawCnt());
            freeTrialManager.updateAll(old);
            freeTrialPrizeDao.deleteByFreeTrialId(dto.getId());
            freeTrialPrizeDao.insert(prize);
        }
        // 查看商品信息
        List<Long> skuIds = StreamUtils.filterConvert(dto.getFreeProductList(), x-> Objects.nonNull(x.getSkuId()),FreeTrialProductDTO::getSkuId);
        List<Sku> skus = skuManager.selectByIds(skuIds);
        Map<Long, Sku> skuMap = StreamUtils.toMap(skus, Sku::getId);
        // 数据处理
        List<FreeTrialProduct> itemPos = FreeTrialUtil.buildItemsPO(dto, po, skuMap);
        // 删除旧明细
        freeTrialProductManager.deleteByFreeTrialId(po.getId());
        // 明细新增
        if(CollectionUtils.isNotEmpty(itemPos)){
            freeTrialProductManager.insertList(itemPos);
        }
    }

    private void handleParram(FreeTrialDTO dto) {
        if(Objects.isNull(dto.getCustomCnt())){
            dto.setCustomCnt(0);
        }
        if(Objects.isNull(dto.getManualCnt())){
            dto.setManualCnt(0);
        }
        if(Objects.isNull(dto.getRandomCnt())){
            dto.setRandomCnt(0);
        }
        dto.setTotalCnt(dto.getCustomCnt() + dto.getManualCnt() + dto.getRandomCnt());
        if(Objects.isNull(dto.getInviteFriendRate())){
            dto.setInviteFriendRate(0);
        }
        if(Objects.isNull(dto.getSupportCntRate())){
            dto.setSupportCntRate(0);
        }
        if(Objects.isNull(dto.getLikeRate())){
            dto.setLikeRate(0);
        }
        if(Objects.isNull(dto.getCompeteReviewRate())){
            dto.setCompeteReviewRate(0);
        }
        if(Objects.isNull(dto.getRecentOrderRate())){
            dto.setRecentOrderRate(0);
        }
        if(Objects.isNull(dto.getSupportCntRate())){
            dto.setSupportPointCntRate(0);
        }
    }

    private void validParam(FreeTrialDTO dto) {
        if(!dto.getNoticeTime().before(dto.getStartTime())){
            throw new BusinessException("预告时间需在活动开始时间之前");
        }
        if(dto.getEndTime().before(dto.getStartTime())){
            throw new BusinessException("活动结束时间不能早于开始时间");
        }
        if(!dto.getDrawTime().after(dto.getEndTime())){
            throw new BusinessException("开奖时间需在活动结束时间之后");
        }
        if(CollectionUtils.isEmpty(dto.getFreeProductList())){
            if(Objects.isNull(dto.getFreeProduct()) || Objects.isNull(dto.getFreeProduct().getSkuId())){
                throw new BusinessException("请选择试用商品");
            }
        }else{
            List<Long> skuIds = StreamUtils.filterConvert(dto.getFreeProductList(), x-> Objects.nonNull(x.getSkuId()),FreeTrialProductDTO::getSkuId);
            if(CollectionUtils.isEmpty(skuIds)){
                throw new BusinessException("请选择试用商品");
            }
        }
        Integer rate = dto.getInviteFriendRate() + dto.getSupportCntRate() +
                dto.getLikeRate() + dto.getCompeteReviewRate() +
                dto.getRecentOrderRate() + dto.getSupportPointCntRate();
        if(!NumberOptUtil.isNullOrZero(dto.getCustomCnt())){
            if(rate != 100){
                throw new BusinessException("合计中奖率必须等于100%！");
            }
        }else{
            if(rate > 0){
                throw new BusinessException("不允许设置中奖比例！");
            }
        }
        if(CollectionUtils.isNotEmpty(dto.getTrialProductList()) && dto.getTrialProductList().size() > 30){
            throw new BusinessException("绑定商品不能超过30个！");
        }
        validateThresholds(dto);
    }

    @Override
    public void delete(Long id) {
        FreeTrial po = freeTrialManager.findByIdDetail(id);
        Assert.notNull(po, "活动不存在");
        if(CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(po.getStatus())){
            throw new BusinessException("进行中的活动不能被删除！");
        }
        // 未开始删除，回滚库存, 已结束删除，如果未开奖就删除
        if(Objects.equals(po.getOpenDraw(), 0)){
            StockCalculateDTO stockdto = new StockCalculateDTO();
            stockdto.setSkuId(po.getFreeProduct().getSkuId());
            stockdto.setStockNum(Long.valueOf(po.getTotalCnt()));
            if (!stockManager.increase(Collections.singletonList(stockdto))) {
                throw new BusinessException("商品库存信息更新失败!");
            }
        }
        freeTrialManager.deleteById(id);
        freeTrialProductManager.deleteByFreeTrialId(id);
    }

    @Override
    public PromoVO promotion(Long id) {
        FreeTrial freeTrial = freeTrialManager.findById(id);
        Assert.notNull(freeTrial, "活动不存在");
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.FREE_TRIAL.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        PromoVO promoVO = new PromoVO();
        promoVO.setId(id);

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    @Override
    public FreeTrialVO get(Long id) {
        FreeTrial po = freeTrialManager.findByIdDetail(id);
        Assert.notNull(po, "活动不存在");
        FreeTrialVO vo = FreeTrialUtil.convert2VO(po);

        List<Long> spuIds = StreamUtils.toList(po.getProductList(), FreeTrialProduct::getProductId);
        List<Product> productList = productManager.findByIds(spuIds);
        Map<Long, Product> productMap = StreamUtils.toMap(productList, Product::getId);

        List<Long> skuIds = StreamUtils.filterConvert(po.getProductList(), FreeProductTypeEnum::isFree, FreeTrialProduct::getSkuId);
        List<SkuProductBaseDTO> skuBaseDTOList = skuManager.skuProductWithStockList(skuIds);
        Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuBaseDTOList, SkuProductBaseDTO::getId);

        //未中奖奖励设置
        FreeTrialPrize freeTrialPrize = freeTrialPrizeDao.getNoDrawPrize(id);
        if(null != freeTrialPrize){
            FreeTrialPrizeVO prize = JSON.parseObject(freeTrialPrize.getRule(), FreeTrialPrizeVO.class);
            List<CouponRule> couponRuleList = couponRuleManager.findByIds(prize.getCouponRuleIds());
            List<FreeTrialVO.CouponRule> couponRuleVOList = StreamUtils.convert(couponRuleList, this::convert2CouponRuleVO);
            vo.setCouponRuleList(couponRuleVOList);
            vo.setCouponThreshold(prize.getCouponThreshold());
            vo.setPoint(prize.getPoint());
            vo.setPointThreshold(prize.getPointThreshold());
        }
        vo.setTrialProductList(Lists.newArrayList());
        vo.setFreeProductList(Lists.newArrayList());
        for (FreeTrialProduct item : po.getProductList()) {
            FreeTrialProductVO itemVO =  FreeTrialUtil.convert2ProductVO(item);
            FreeProductTypeEnum typeEnum = FreeProductTypeEnum.getEnum(item.getType());
            Product product = productMap.get(item.getProductId());
            if(typeEnum == FreeProductTypeEnum.FREE){
                // sku
                SkuProductBaseDTO sku = skuMap.get(item.getSkuId());
                if(null != sku){
                    itemVO.setPicUrl(sku.getPicUrl());
                    itemVO.setSkuCode(sku.getSkuCode());
                    itemVO.setProductName(sku.getProductName());
                    itemVO.setSpecValueList(sku.getSpecValueList());
                    itemVO.setStockNum(sku.getStock());
                    itemVO.setSaleStatus(sku.getSaleStatus());
                    itemVO.setListPrice(sku.getListPrice());
                    if(null != product){
                        itemVO.setEnableShow(product.getEnableShow());
                    }
                    itemVO.setSalePrice(sku.getSalePrice());
                    vo.setFreeProduct(itemVO);
                    vo.getFreeProductList().add(itemVO);
                }
            }
            if(typeEnum == FreeProductTypeEnum.TRIAL){
                // spu
                if(null != product){
                    itemVO.setPicUrl(product.getPicUrl());
                    itemVO.setProductName(product.getProductName());
                    itemVO.setSaleStatus(product.getSaleStatus());
                    itemVO.setEnableShow(product.getEnableShow());
                    vo.getTrialProductList().add(itemVO);
                }
            }
        }

        return vo;
    }
    @Override
    public void export(FreeTrialDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        validTimeRange(dto);
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(dto.getExportType());
        if (null == enumByCode){
            throw new BusinessException("导出类型不存在");
        }
        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(dto.getExportType());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());
        dto.setFileName(enumByCode.getFileName() + date);
        dataExportTaskDao.saveOne(dataExportTask);
        // 异步执行
        FreeTrialService bean = SpringContextUtils.getApplicationContext().getBean(FreeTrialService.class);
        bean.export(dto, dataExportTask);
    }

    @Override
    @Async
    public void export(FreeTrialDTO dto, DataExportTask exportTask) {
        PageVO<FreeTrialVO> voPageVOS = this.getList(dto);
        List<FreeTrialVO> vos = voPageVOS.getList();

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            List excelVOS = null;
            Class head = null;
            Integer mergeRowIndex = null;
            List<Integer> mergeColumnRegion = Lists.newArrayList();
            if(enumByCode == FileExportCenterEnum.free_trial){
                excelVOS = this.convert2ExcelList(vos);
                head = FreeTrialExcelVO.class;
                mergeRowIndex = null;
                mergeColumnRegion = Lists.newArrayList();
            }else{
                throw new BusinessException("导出类型有误");
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, head)
                    .autoCloseStream(Boolean.TRUE)
                    .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnRegion))
                    .sheet().doWrite(excelVOS);

            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            String url = aliyunOssUtils.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()),
                    fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (RuntimeException e) {
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }

    private List convert2ExcelList(List<FreeTrialVO> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return Lists.newArrayList();
        }
        List<FreeTrialExcelVO> excelVOS = Lists.newArrayList();
        for (FreeTrialVO vo : vos) {
            FreeTrialExcelVO excelVO = BeanUtils.copy(vo, FreeTrialExcelVO::new);
            excelVO.setProductName(vo.getFreeProduct().getProductName());
            if(Objects.nonNull(vo.getReviewRate())){
                excelVO.setReviewRate(vo.getReviewRate());
            }
            excelVO.setStatusStr(CommonActivityStatusEnum.getEnum(vo.getStatus()).getDesc());
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }

    private void validTimeRange(FreeTrialDTO dto) {

    }

    @Override
    public void start(Long id) {
        // FreeTrial po = freeTrialManager.findById(id);
        // Assert.notNull(po, "活动不存在");
        // List<FreeTrialItem> freeTrialItems = freeTrialItemManager.findByFreeTrialId(id);
        // po.setPointItems(freeTrialItems);
        // FreeTrialDTO dto = BeanUtils.deepCopy(po, FreeTrialDTO.class);
        // dto.setStartTime(new Date());
        // validParam(dto);
        // if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.IN_PROGRESS.getCode())){
        //     return;
        // }
        // FreeTrial po4Update = new FreeTrial();
        // po4Update.setId(id);
        // po4Update.setStartTime(new Date());
        // po4Update.setStatus(CommonActivityStatusEnum.IN_PROGRESS.getCode());
        // freeTrialManager.update(po4Update);
    }

    @Override
    public void end(Long id) {
        FreeTrial po = freeTrialManager.findById(id);
        Assert.notNull(po, "活动不存在");
        if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.END.getCode())){
            return;
        }
        if(CommonActivityStatusEnum.NOT_STARTED.getCode().equals(po.getStatus())){
            throw new BusinessException("未开始的活动不能直接结束！");
        }
        FreeTrial po4Update = new FreeTrial();
        po4Update.setId(id);
        po4Update.setEndTime(new Date());
        po4Update.setStatus(CommonActivityStatusEnum.END.getCode());
        freeTrialManager.update(po4Update);
    }

    @Override
    public void saveConfig(FreeTrialConfigDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        validConfig(dto);
        FreeTrialConfig config = FreeTrialConfigUtil.convert2PO(dto);
        config.setOperator(user.getUserName());
        if(null == dto.getId()){
            freeTrialConfigManager.insert(config);
        }else{
            FreeTrialConfig old = freeTrialConfigManager.get(dto.getId());
            Assert.notNull(old, "活动不存在");
            config.setCreateTime(old.getCreateTime());
            config.setDelflag(old.getDelflag());
            if(CommonLinkTypeEnum.NO_SET.getCode().equals(dto.getLinkType())){
                config.setLink(null);
            }
            if(BasicFlagEnum.NO.getKey().equals(dto.getPrizePoint())){
                config.setPoint(null);
            }
            config.setUpdateTime(new Date());
            freeTrialConfigManager.update(config);
        }
    }

    private void validConfig(FreeTrialConfigDTO dto) {
        if(BasicFlagEnum.YES.getKey().equals(dto.getPrizePoint())){
            Assert.notNull(dto.getPoint(), "勾选测评奖励积分后积分值不能为空!");
            if(dto.getPoint() <= 0 || dto.getPoint() > 100){
                throw new BusinessException("积分值范围是1～100分！");
            }
        }
    }

    @Override
    public FreeTrialConfigVO getConfig() {
        FreeTrialConfigDTO dto = new FreeTrialConfigDTO();
        List<FreeTrialConfig> configList = freeTrialConfigManager.findByCondition(dto);
        FreeTrialConfig config = StreamUtils.getFirst(configList);
        if (null == config){
            return null;
        }
        return FreeTrialConfigUtil.convert2VO(config);
    }

    @Override
    public FreeTrialStatsVO statsGet(Long freeTrialId) {
        FreeTrialStats po = freeTrialStatsManager.findByFreeTrialId(freeTrialId);
        if(po == null){
            return null;
        }
        FreeTrialStatsVO vo = BeanUtils.copy(po, FreeTrialStatsVO::new);
        vo.setPayRate(po.getPayRate().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
        return vo;
    }

    @Override
    public PageVO<FreeTrialStatsItemVO> statsItemGetList(FreeTrialStatsItemDTO dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialStatsItem> pos = freeTrialStatsItemManager.getList(dto);
        return PageUtils.convert(pos, po -> BeanUtils.copy(po, FreeTrialStatsItemVO::new));
    }

    @Override
    public FreeSubscribeCntVO subscribeCnt() {
        FreeSubscribeCntVO vo = redisOperation.get(FreeTrialUtil.CNT_KEY);
        if(vo == null){
            vo = new FreeSubscribeCntVO();
            // 统计所有历史活动，点击过首页、未开始和返场订阅消息提醒的次数
            Integer totalCnt = messageSubscribeDao.getCount(MessageSubscribeEnum.getTotalSubscribe(), null);
            vo.setTotalCnt(totalCnt);
            // 统计所有历史活动，点击过未开始和返场订阅消息提醒还未触达的次数
            Integer unreachCnt = messageSubscribeDao.getCount(MessageSubscribeEnum.getUnreachSubscribe(), MsgSendStatusEnum.NOT_PUSHED.getCode());
            vo.setUnreachCnt(unreachCnt);
            Integer allFissiUserCnt = customerUserDao.cntFissionUser(CustomerUserTypeEnum.freeTrial_user.getCode(), null);
            vo.setAllFissiUserCnt(allFissiUserCnt);
            Integer todayFissiUserCnt = customerUserDao.cntFissionUser(CustomerUserTypeEnum.freeTrial_user.getCode(), DateUtil.getDayOfStart(LocalDate.now()));
            vo.setTodayFissiUserCnt(todayFissiUserCnt);
            Integer freeTasteCnt = messageSubscribeDao.getCount(Arrays.asList(MessageSubscribeEnum.FREE_TASTE_START_NOTICE.getCode()), null);
            vo.setFreeTasteCnt(freeTasteCnt);
            Integer fullTrialCnt = messageSubscribeDao.getCount(Arrays.asList(MessageSubscribeEnum.FULL_TRIAL_START_NOTICE.getCode()), null);
            vo.setFullTrialCnt(fullTrialCnt);
            redisOperation.setex(FreeTrialUtil.CNT_KEY, vo, 130, TimeUnit.MINUTES);
        }
        return vo;
    }

    @Override
    public PageVO<FreeTrialWaitVO> getWaitList(FreeTrialWaitDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialWaitVO> waitList = freeTrialProductManager.findWaitList(dto);
        List<Long> skuIds = StreamUtils.toList(waitList, FreeTrialWaitVO::getSkuId);
        List<Long> freeTrialIds = StreamUtils.toList(waitList, FreeTrialWaitVO::getFreeTrialId);
        List<FreeTrial> freeTrialList = freeTrialManager.findByIds(freeTrialIds);
        Map<Long, FreeTrial> freeTrialMap = StreamUtils.toMap(freeTrialList, FreeTrial::getId);

        List<SkuProductBaseDTO> skuBaseDTOList = skuManager.skuProductWithStockList(skuIds);
        Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuBaseDTOList, SkuProductBaseDTO::getId);

        return PageUtils.convert(waitList, data ->{
            SkuProductBaseDTO skuProductBaseDTO = skuMap.get(data.getSkuId());
            if(null != skuProductBaseDTO){
                data.setProductName(skuProductBaseDTO.getProductName());
                data.setSkuCode(skuProductBaseDTO.getSkuCode());
            }
            FreeTrial freeTrial = freeTrialMap.get(data.getFreeTrialId());
            if(null != freeTrial){
                data.setNoticeTime(freeTrial.getNoticeTime());
            }
            return data;
        });
    }

    @Override
    public void exportWaitList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialWaitExportVO.class).build();
        FreeTrialWaitDTO searchDTO = new FreeTrialWaitDTO();
        searchDTO.setFreeTrialVoteId(dto.getFreeTrialVoteId());
        searchDTO.setName(dto.getName());
        searchDTO.setUpdateTimeStart(dto.getUpdateTimeStart());
        searchDTO.setUpdateTimeEnd(dto.getUpdateTimeEnd());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            searchDTO.setPageNum(pageNum);
            searchDTO.setPageSize(pageSize);
            PageVO<FreeTrialWaitVO> freeProductList = this.getWaitList(searchDTO);
            Pagination pagination = freeProductList.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTrialWaitExportVO> exportVOList = FreeTrialUtil.convert2WaitExportVO(freeProductList.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public FreeTrialVoteDetailVO getWait(Long freeTrialVoteId, Long freeTrialVoteFreeProductId) {
        //获取共创投票信息
        FreeTrialVote freeTrialVote = freeTrialVoteManager.findById(freeTrialVoteId);
        Assert.notNull(freeTrialVote, "活动不存在");
        //获取0元试用活动信息
        FreeTrialVO freeTrialVO = this.get(freeTrialVote.getFreeTrialId());
        Assert.notNull(freeTrialVO, "数据缺失");
        FreeTrialProductVO freeTrialProductVO = StreamUtils.findFirst(freeTrialVO.getFreeProductList(), x -> freeTrialVoteFreeProductId.equals(x.getId()));
        freeTrialVO.setFreeProduct(freeTrialProductVO);
        return FreeTrialVoteUtil.convert2VO(freeTrialVote, freeTrialVO);
    }

    @Override
    public FreeTrialCntVO getCnt() {
        FreeTrialCntVO cntVO = new FreeTrialCntVO();
        cntVO.setAuditedCnt(freeTrialManager.countFreeTrialCnt());
        cntVO.setWaitCnt(freeTrialManager.countWaitCnt());
        return cntVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(FreeTrialAuditDTO dto) {
        // 共创投票状态
        Long freeTrialVoteFreeProductId = dto.getFreeTrialVoteFreeProductId();
        FreeTrialProduct freeTrialProduct = freeTrialProductManager.findById(freeTrialVoteFreeProductId);
        Assert.notNull(freeTrialProduct, "数据缺失");
        if(Objects.equals(freeTrialProduct.getAudit(), BasicFlagEnum.YES.getKey())){
            throw new BusinessException("该商品已审核");
        }
        FreeTrialProduct update4Po = new FreeTrialProduct();
        update4Po.setId(freeTrialVoteFreeProductId);
        update4Po.setAudit(1);
        if(freeTrialProductManager.update(update4Po) <= 0){
            throw new BusinessException("审核失败");
        }
        // 0元试用信息
        dto.setId(null);
        Long freeTrialId = this.save(dto);
        // 订阅用户
        TransactionUtils.afterCommitSyncExecute(() -> {
            // 事务提交后异步推送消息
            milkProducerService.syncFreeTrialVoteSubscribe(freeTrialVoteFreeProductId, freeTrialId);
        });
    }

    private FreeTrialVO.CouponRule convert2CouponRuleVO(CouponRule couponRule) {
        FreeTrialVO.CouponRule vo = new FreeTrialVO.CouponRule();
        vo.setId(couponRule.getId());
        vo.setCouponName(couponRule.getCouponName());
        vo.setChannel(couponRule.getChannel());
        vo.setIsPublic(couponRule.getIsPublic());
        vo.setStartTime(couponRule.getStartTime());
        vo.setEndTime(couponRule.getEndTime());
        vo.setStatus(couponRule.getStatus());
        vo.setOnlineTime(couponRule.getOnlineTime());
        vo.setOfflineTime(couponRule.getOfflineTime());
        vo.setAmountFull(couponRule.getAmountFull());
        vo.setAmountReduce(couponRule.getAmountReduce());
        vo.setAmountLimit(couponRule.getAmountLimit());
        vo.setCouponWay(couponRule.getCouponWay());
        return vo;
    }

    private void validateThresholds(FreeTrialDTO dto){
        FreeTrialDTO.PrizeThreshold couponPrizeThreshold = dto.getCouponThreshold();
        FreeTrialDTO.PrizeThreshold pointPrizeThreshold = dto.getPointThreshold();
        if (Objects.equals(2, couponPrizeThreshold.getType())) {
            if(CollectionUtils.isEmpty(dto.getCouponRuleIds())){
                throw new BusinessException("请至少选择一张[未中奖奖励优惠券]!");
            }
            if (!ObjectUtils.anyNotNull(
                    couponPrizeThreshold.getApplyTotal(),
                    couponPrizeThreshold.getDrawTotal(),
                    couponPrizeThreshold.getHelpRank())) {
                throw new BusinessException("请至少选择一项优惠券奖励门槛!");
            }
        }
        if (Objects.equals(2, pointPrizeThreshold.getType())) {
            if(Objects.isNull(dto.getPoint()) || dto.getPoint() == 0){
                throw new BusinessException("请填写正确的[未中奖用户奖励积分]值!");
            }
            if (!ObjectUtils.anyNotNull(
                    pointPrizeThreshold.getApplyTotal(),
                    pointPrizeThreshold.getDrawTotal(),
                    pointPrizeThreshold.getHelpRank())) {
                throw new BusinessException("请至少选择一项优惠券奖励门槛!");
            }
        }
    }
}
