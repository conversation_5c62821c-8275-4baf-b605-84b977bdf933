package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FullTrialService;
import com.hengtiansoft.privilege.entity.dto.FullTrialQueryDTO;
import com.hengtiansoft.privilege.entity.dto.FullTrialSaveDTO;
import com.hengtiansoft.privilege.entity.vo.FullTrialDetailVO;
import com.hengtiansoft.privilege.entity.vo.FullTrialListVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "满额试用B端")
@RequestMapping("/fullTrial")
public class FullTrialController {

    @Autowired
    private FullTrialService fullTrialService;


    @ApiOperation("分页列表")
    @PostMapping("/getList")
    public Response<PageVO<FullTrialListVO>> getList(@RequestBody FullTrialQueryDTO dto) {
        PageVO<FullTrialListVO> pageVO = fullTrialService.getList(dto);
        return ResponseFactory.success(pageVO);
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Void> save(@RequestBody @Validated FullTrialSaveDTO dto) {
        fullTrialService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<FullTrialDetailVO> get(@RequestParam Long id) {
        FullTrialDetailVO fullTrialDetailVO = fullTrialService.get(id);
        return ResponseFactory.success(fullTrialDetailVO);
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        return ResponseFactory.success(fullTrialService.promotion(id));
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Void> delete(@RequestParam Long id) {
        fullTrialService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("结束")
    @GetMapping("/end")
    public Response<Object> end(@RequestParam Long id) {
        fullTrialService.end(id);
        return ResponseFactory.success();
    }


}
