package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialFissionUserService;
import com.hengtiansoft.privilege.entity.vo.FreeTrialFissionUserCntVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.user.entity.dto.UserQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "零元试用裂变用户")
@RequestMapping("/freeTrial/fissionUser")
public class FreeTrialFissionUserController {

    @Autowired
    private FreeTrialFissionUserService freeTrialFissionUserService;

    @ApiOperation("分页")
    @PostMapping("/page")
    public Response<PageVO<FreeTrialUserVO>> page(@RequestBody UserQueryDTO dto) {
        PageVO<FreeTrialUserVO> list = freeTrialFissionUserService.page(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation("统计")
    @GetMapping("/cnt")
    public Response<FreeTrialFissionUserCntVO> cnt() {
        return ResponseFactory.success(freeTrialFissionUserService.cnt());
    }
}
