package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.vo.*;

public interface FreeTrialService {

    PageVO<FreeTrialVO> getList(FreeTrialDTO dto);

    Long save(FreeTrialDTO dto);

    void saveForVote(FreeTrialDTO dto);

    void delete(Long id);

    PromoVO promotion(Long id);

    FreeTrialVO get(Long id);

    void export(FreeTrialDTO dto);

    void export(FreeTrialDTO dto, DataExportTask dataExportTask);

    void start(Long id);

    void end(Long id);

    void saveConfig(FreeTrialConfigDTO dto);

    FreeTrialConfigVO getConfig();

    FreeTrialStatsVO statsGet(Long freeTrialId);

    PageVO<FreeTrialStatsItemVO> statsItemGetList(FreeTrialStatsItemDTO dto);

    FreeSubscribeCntVO subscribeCnt();

    PageVO<FreeTrialWaitVO> getWaitList(FreeTrialWaitDTO dto);

    void exportWaitList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    FreeTrialVoteDetailVO getWait(Long freeTrialVoteId, Long freeTrialVoteFreeProductId);

    FreeTrialCntVO getCnt();

    void audit(FreeTrialAuditDTO dto);
}
