package com.hengtiansoft.operation.freeTrial.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialCommentPrizeDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialCommentSearchDTO;
import com.hengtiansoft.operation.freeTrial.entity.FreeTrialCommentPrizeVO;
import com.hengtiansoft.privilege.entity.vo.FreeTrialCommentVO;

public interface FreeTrialCommentService {


    PageVO<FreeTrialCommentVO> page(FreeTrialCommentSearchDTO dto);

    FreeTrialCommentVO get(Long id);

    void cancel(Long id);

    void top(Long id);

    void show(Long id);

    void hide(Long id);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    void exportHot(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    PageVO<FreeTrialCommentVO> hot(FreeTrialCommentSearchDTO dto);

    void prizeSave(FreeTrialCommentPrizeDTO dto);

    FreeTrialCommentPrizeVO prizeGet();
}
