package com.hengtiansoft.operation.freeTrial.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialVoteService;
import com.hengtiansoft.privilege.entity.dto.FreeTrialVoteDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialVoteFreeProductDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialVoteSaveDTO;
import com.hengtiansoft.privilege.entity.dto.FreeTrialVoteUserDTO;
import com.hengtiansoft.privilege.entity.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "共创投票B端")
@RequestMapping("/freeTrial/vote")
public class FreeTrialVoteController {

    @Resource
    private FreeTrialVoteService freeTrialVoteService;

    @ApiOperation("分页列表")
    @PostMapping("/getList")
    public Response<PageVO<FreeTrialVoteVO>> getList(@RequestBody FreeTrialVoteDTO dto) {
        PageVO<FreeTrialVoteVO> list = freeTrialVoteService.getList(dto);
        return ResponseFactory.success(list);
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<FreeTrialVoteDetailVO> get(@RequestParam Long id) {
        FreeTrialVoteDetailVO detailVO = freeTrialVoteService.get(id);
        return ResponseFactory.success(detailVO);
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Void> save(@RequestBody @Validated FreeTrialVoteSaveDTO dto) {
        freeTrialVoteService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("结束")
    @GetMapping("/end")
    public Response<Void> end(@RequestParam Long id) {
        freeTrialVoteService.end(id);
        return ResponseFactory.success();
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Void> delete(@RequestParam Long id) {
        freeTrialVoteService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        PromoVO promoVO = freeTrialVoteService.promotion(id);
        return ResponseFactory.success(promoVO);
    }

    @ApiOperation("试用商品列表")
    @PostMapping("/freeProduct/getList")
    public Response<PageVO<FreeTrialVoteFreeProductVO>> getFreeProductList(@RequestBody FreeTrialVoteFreeProductDTO dto) {
        return ResponseFactory.success(freeTrialVoteService.getFreeProductList(dto));
    }

    @ApiOperation("投票用户列表")
    @PostMapping("/user/getList")
    public Response<PageVO<FreeTrialVoteUserVO>> getUserList(@RequestBody FreeTrialVoteUserDTO dto) {
        return ResponseFactory.success(freeTrialVoteService.getUserList(dto));
    }
}
