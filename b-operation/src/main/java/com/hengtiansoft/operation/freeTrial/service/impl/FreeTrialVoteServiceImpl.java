package com.hengtiansoft.operation.freeTrial.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialService;
import com.hengtiansoft.operation.freeTrial.service.FreeTrialVoteService;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.po.FreeTrialProduct;
import com.hengtiansoft.privilege.entity.po.FreeTrialVote;
import com.hengtiansoft.privilege.entity.po.MonitorPage;
import com.hengtiansoft.privilege.entity.po.PointAmount;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.FreeProductTypeEnum;
import com.hengtiansoft.privilege.enums.MoTypeEnum;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.privilege.util.FreeTrialVoteUtil;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

import static com.github.pagehelper.page.PageMethod.startPage;


@Slf4j
@Service
public class FreeTrialVoteServiceImpl implements FreeTrialVoteService {

    @Resource
    private FreeTrialVoteManager freeTrialVoteManager;
    @Resource
    private FreeTrialManager freeTrialManager;
    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private FreeTrialService freeTrialService;
    @Resource
    private MonitorPageManager monitorPageManager;
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private FreeTrialVoteUserManager freeTrialVoteUserManager;
    @Resource
    private ProductManager productManager;
    @Resource
    private MilkProducerService milkProducerService;

    @Override
    public PageVO<FreeTrialVoteVO> getList(FreeTrialVoteDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialVote> freeTrialVoteList = freeTrialVoteManager.findByCondition(dto);
        List<Long> freeTrialIds = StreamUtils.toList(freeTrialVoteList, FreeTrialVote::getFreeTrialId);

        List<FreeTrialProduct> freeTrialProductList = freeTrialProductManager.findByFreeTrialIds(freeTrialIds, FreeProductTypeEnum.FREE.getCode());
        Map<Long, List<FreeTrialProduct>> freeTrialProductMap = StreamUtils.group(freeTrialProductList, FreeTrialProduct::getFreeTrialId);

        return PageUtils.convert(freeTrialVoteList, data -> {
            FreeTrialVoteVO freeTrialVoteVO = new FreeTrialVoteVO();
            freeTrialVoteVO.setId(data.getId());
            freeTrialVoteVO.setName(data.getName());
            List<FreeTrialProduct> freeTrialProducts = freeTrialProductMap.get(data.getFreeTrialId());
            freeTrialVoteVO.setFreeTrialProductCnt(CollectionUtils.size(freeTrialProducts));
            freeTrialVoteVO.setTotalVotes(data.getTotalVotes());
            freeTrialVoteVO.setSubscribeCnt(data.getSubscribeCnt());
            freeTrialVoteVO.setVoteStartTime(data.getVoteStartTime());
            freeTrialVoteVO.setVoteEndTime(data.getVoteEndTime());
            freeTrialVoteVO.setUpdateTime(data.getUpdateTime());
            freeTrialVoteVO.setStatus(data.getStatus());
            freeTrialVoteVO.setNumber(data.getNumber());
            return freeTrialVoteVO;
        });
    }

    @Override
    public FreeTrialVoteDetailVO get(Long id) {
        //获取共创投票信息
        FreeTrialVote freeTrialVote = freeTrialVoteManager.findById(id);
        Assert.notNull(freeTrialVote, "活动不存在");
        //获取0元试用活动信息
        FreeTrialVO freeTrialVO = freeTrialService.get(freeTrialVote.getFreeTrialId());
        Assert.notNull(freeTrialVO, "数据缺失");
        return FreeTrialVoteUtil.convert2VO(freeTrialVote, freeTrialVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(FreeTrialVoteSaveDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        FreeTrialVote freeTrialVotePo = FreeTrialVoteUtil.convert2PO(dto);
        Integer status = CommonOptUtil.getStatus(freeTrialVotePo.getVoteStartTime(), freeTrialVotePo.getVoteEndTime()).getCode();
        freeTrialVotePo.setStatus(status);
        // 时间交叉校验
        FreeTrialVoteDTO queryDto = new FreeTrialVoteDTO();
        queryDto.setStatusList(Arrays.asList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()));
        queryDto.setNotId(dto.getId());
        List<FreeTrialVote> freeTrialVoteList = freeTrialVoteManager.findByCondition(queryDto);
        for (FreeTrialVote freeTrialVote : freeTrialVoteList) {
            if(Objects.equals(freeTrialVote.getId(), dto.getId())){
                continue;
            }
            if (DateUtil.isOverlap(dto.getVoteStartTime(), dto.getVoteEndTime(), freeTrialVote.getVoteStartTime(), freeTrialVote.getVoteEndTime())) {
                throw new BusinessException("该活动【" + dto.getName() + "】的时间段与名称为【" + freeTrialVote.getName() + "】的有重复，请修改");
            }
        }
        freeTrialVotePo.setOperator(user.getUserName());
        if(Objects.isNull(freeTrialVotePo.getId())){
            // 0元试用设置
            freeTrialService.saveForVote(dto);
            // 共创投票设置
            freeTrialVotePo.setFreeTrialId(dto.getFreeTrialId());
            Integer maxNumber = freeTrialVoteManager.findMaxNumber();
            freeTrialVotePo.setNumber(Objects.isNull(maxNumber) ? 1 : maxNumber + 1);
            freeTrialVoteManager.insert(freeTrialVotePo);
            //推广
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(dto.getName()+"_共创投票");
            monitorPage.setTargetId(freeTrialVotePo.getId());
            monitorPage.setType(MoTypeEnum.FREE_TRIAL_VOTE.getCode());
            monitorPage.setTargetName(dto.getName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
        }else{
            FreeTrialVote old = freeTrialVoteManager.findById(freeTrialVotePo.getId());
            Assert.notNull(old, "活动不存在");
            // 共创投票设置
            freeTrialVoteManager.update(freeTrialVotePo);
            // 0元试用设置
            dto.setFreeTrialId(old.getFreeTrialId());
            freeTrialService.saveForVote(dto);
        }
    }

    @Override
    public void end(Long id) {
        FreeTrialVote freeTrialVote = freeTrialVoteManager.findById(id);
        Assert.notNull(freeTrialVote, "活动不存在");
        if(Objects.equals(freeTrialVote.getStatus(), CommonActivityStatusEnum.END.getCode())){
            return;
        }
        if(!CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(freeTrialVote.getStatus())){
            throw new BusinessException("只有进行中才可以结束活动！");
        }
        FreeTrialVote po4Update = new FreeTrialVote();
        po4Update.setId(id);
        po4Update.setVoteEndTime(new Date());
        po4Update.setStatus(CommonActivityStatusEnum.END.getCode());
        freeTrialVoteManager.update(po4Update);
        TransactionUtils.afterCommitSyncExecute(() -> {
            // 事务提交后异步推送消息
            milkProducerService.syncFreeTrialVote(id, true);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        FreeTrialVote freeTrialVote = freeTrialVoteManager.findById(id);
        Assert.notNull(freeTrialVote, "活动不存在");
        if(!CommonActivityStatusEnum.END.getCode().equals(freeTrialVote.getStatus())){
            throw new BusinessException("只有已结束的活动才可以删除！");
        }
        freeTrialVoteManager.deleteById(id);
        freeTrialManager.deleteById(freeTrialVote.getFreeTrialId());
        freeTrialProductManager.deleteByFreeTrialId(freeTrialVote.getFreeTrialId());
    }

    @Override
    public PromoVO promotion(Long id) {
        FreeTrialVote freeTrialVote = freeTrialVoteManager.findById(id);
        Assert.notNull(freeTrialVote, "活动不存在");
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.FREE_TRIAL_VOTE.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        PromoVO promoVO = new PromoVO();
        promoVO.setId(id);

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialVoteExportVO.class).build();
        FreeTrialVoteDTO searchDTO = new FreeTrialVoteDTO();
        searchDTO.setName(dto.getName());
        searchDTO.setStatus(dto.getStatus());
        searchDTO.setUpdateTimeStart(dto.getUpdateTimeStart());
        searchDTO.setUpdateTimeEnd(dto.getUpdateTimeEnd());
        searchDTO.setId(dto.getRecordId());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            searchDTO.setPageNum(pageNum);
            searchDTO.setPageSize(pageSize);
            PageVO<FreeTrialVoteVO> freeTrialVotePageVO = this.getList(searchDTO);
            Pagination pagination = freeTrialVotePageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTrialVoteExportVO> exportVOList = FreeTrialVoteUtil.convert2ExportVO(freeTrialVotePageVO.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public PageVO<FreeTrialVoteFreeProductVO> getFreeProductList(FreeTrialVoteFreeProductDTO dto) {
        FreeTrialVote freeTrialVote = freeTrialVoteManager.findById(dto.getFreeTrialVoteId());
        Assert.notNull(freeTrialVote, "活动不存在");
        Assert.notNull(freeTrialVote.getFreeTrialId(), "数据缺失");
        dto.setFreeTrialId(freeTrialVote.getFreeTrialId());
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialVoteFreeProductVO> freeProductVOList = freeTrialProductManager.findFreeTrialVoteFreeProduct(dto);
        return PageUtils.convert(freeProductVOList, data ->{
            data.setName(freeTrialVote.getName());
            return data;
        });
    }

    @Override
    public PageVO<FreeTrialVoteUserVO> getUserList(FreeTrialVoteUserDTO dto) {
        FreeTrialProduct freeTrialProduct = freeTrialProductManager.findById(dto.getFreeTrialVoteFreeProductId());
        Assert.notNull(freeTrialProduct, "活动不存在");
        Product product = productManager.getOne(freeTrialProduct.getProductId());
        startPage(dto.getPageNum(), dto.getPageSize());
        List<FreeTrialVoteUserVO> freeTrialVoteUserVOList = freeTrialVoteUserManager.findFreeTrialVoteUser(dto);
        return PageUtils.convert(freeTrialVoteUserVOList, data ->{
            data.setProductName(product.getProductName());
            return data;
        });
    }

    @Override
    public void exportFreeProductList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialVoteFreeProductExportVO.class).build();
        FreeTrialVoteFreeProductDTO searchDTO = new FreeTrialVoteFreeProductDTO();
        searchDTO.setFreeTrialVoteId(dto.getFreeTrialVoteId());
        searchDTO.setSpuId(dto.getSpuId());
        searchDTO.setSkuCode(dto.getSkuCode());
        searchDTO.setProductName(dto.getProductName());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            searchDTO.setPageNum(pageNum);
            searchDTO.setPageSize(pageSize);
            PageVO<FreeTrialVoteFreeProductVO> freeProductList = this.getFreeProductList(searchDTO);
            Pagination pagination = freeProductList.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTrialVoteFreeProductExportVO> exportVOList = FreeTrialVoteUtil.convert2FreeProductExportVO(freeProductList.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public void exportUserList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(FreeTrialVoteUserExportVO.class).build();
        FreeTrialVoteUserDTO searchDTO = new FreeTrialVoteUserDTO();
        searchDTO.setFreeTrialVoteFreeProductId(dto.getFreeTrialVoteFreeProductId());
        searchDTO.setUserId(dto.getUserId());
        searchDTO.setNickName(dto.getNickName());
        searchDTO.setPhone(dto.getPhone());
        searchDTO.setHasSubscribe(dto.getHasSubscribe());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            searchDTO.setPageNum(pageNum);
            searchDTO.setPageSize(pageSize);
            PageVO<FreeTrialVoteUserVO> freeProductList = this.getUserList(searchDTO);
            Pagination pagination = freeProductList.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<FreeTrialVoteUserExportVO> exportVOList = FreeTrialVoteUtil.convert2UserExportVO(freeProductList.getList());
            excelWriter.write(exportVOList, writeSheet);
        }while (pageNum <= pages);
    }
}
