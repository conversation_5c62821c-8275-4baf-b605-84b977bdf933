package com.hengtiansoft.operation.privilege.service.impl;

import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.PrivilegeRelationTypeEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.TransactionUtils;
import com.hengtiansoft.item.dao.ItemProductGroupDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.dao.SkuDao;
import com.hengtiansoft.item.entity.dto.SkuStockAndPurchaseLimitDTO;
import com.hengtiansoft.item.entity.po.ItemProductGroup;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.privilege.service.PrivilegeLabelActivityService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.privilege.dao.PrivilegeItemUserDao;
import com.hengtiansoft.privilege.dao.PrivilegeLableDao;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivityPageDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivitySaveDTO;
import com.hengtiansoft.privilege.entity.po.PrivilegeItemUser;
import com.hengtiansoft.privilege.entity.po.PrivilegeLable;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityDetailVO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityPageVO;
import com.hengtiansoft.privilege.manager.PrivilegeLabelManager;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * @Author: ocean
 * @Date: 2024-09-20 14:31
 * @Desc:
 */
@Slf4j
@Service
public class PrivilegeLabelActivityServiceImpl implements PrivilegeLabelActivityService {

    @Resource
    private PrivilegeLableDao privilegeLableDao;

    @Resource
    private PrivilegeItemUserDao privilegeItemUserDao;

    @Resource
    private SkuDao skuDao;

    @Resource
    private ProductDao productDao;

    @Resource
    private ItemProductGroupDao itemProductGroupDao;

    @Resource
    private PrivilegeLabelManager privilegeLabelManager;

    @Resource
    private Executor reportTask;
    @Resource
    private MilkProducerService milkProducerService;

    @Override
    public PageVO<PrivilegeLabelActivityPageVO> getPage(PrivilegeLabelActivityPageDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<PrivilegeLable> privilegeLables = privilegeLableDao.listPrivilegeLabelActivity(dto);
        if (CollectionUtils.isEmpty(privilegeLables)) {
            return PageUtils.emptyPage();
        }
        List<Long> lableIds = privilegeLables.stream().map(PrivilegeLable::getId).collect(Collectors.toList());
        List<PrivilegeItemUser> privilegeItemUsers = privilegeItemUserDao.findByLableIds(lableIds, PrivilegeRelationTypeEnum.SPU_ID.getCode());
        Map<Long, Long> productCntMap = privilegeItemUsers.stream().collect(Collectors.groupingBy(PrivilegeItemUser::getLableId, Collectors.counting()));
        return PageUtils.convert(privilegeLables,  data -> {
            PrivilegeLabelActivityPageVO privilegeLabelActivityPageVO = new PrivilegeLabelActivityPageVO();
            BeanUtils.copy(data, privilegeLabelActivityPageVO);
            privilegeLabelActivityPageVO.setName(data.getLableName());
            privilegeLabelActivityPageVO.setProductCnt(Math.toIntExact(productCntMap.getOrDefault(data.getId(), 0L)));
            return privilegeLabelActivityPageVO;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PrivilegeLabelActivitySaveDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PrivilegeLable privilegeLable = new PrivilegeLable();
        validateActivityTime(dto.getStartTime(), dto.getEndTime());
        List<Long> dtoProductIds = dto.getProductList().stream().map(PrivilegeLabelActivitySaveDTO.Product::getProductId).collect(Collectors.toList());
        validateInProgressProduct(dto.getId(), dto.getStartTime(), dto.getEndTime(), dtoProductIds);
//        validateStockAndLimitNum(dto.getProductList());
        Date nowTime = new Date();
        privilegeLable.setStatus(checkStatus(dto.getStartTime(), nowTime, dto.getEndTime()));
        Long existedSameNameLableId = privilegeLableDao.getLabelIdByName(dto.getName());
        Long id = dto.getId();
        if (null == id) {
            if (null != existedSameNameLableId) {
                throw new BusinessException("存在相同名称特权标签！");
            }
            privilegeLable.setLableName(dto.getName());
            privilegeLable.setStartTime(dto.getStartTime());
            privilegeLable.setEndTime(dto.getEndTime());
            privilegeLable.setOnlineFlag(1);
            privilegeLable.setDelflag(0);
            privilegeLable.setCreateTime(new Date());
            privilegeLable.setUpdateTime(new Date());
            privilegeLable.setImageUrl(dto.getImageUrl());
            privilegeLable.setPeopleLimit(dto.getPeopleLimit());
            privilegeLable.setPrivilegeCnt(0);
            privilegeLable.setAlreadyCnt(0);
            privilegeLable.setNotSetCnt(0);
            privilegeLable.setLinkType(dto.getLinkType());
            privilegeLable.setLink(dto.getLink());
            privilegeLable.setLimitNum(dto.getLimitNum());
            if (CollectionUtils.isNotEmpty(dto.getLabelIds())) {
                privilegeLable.setLabelId(String.join(",", dto.getLabelIds().stream().map(String::valueOf).toArray(String[]::new)));
            }
            privilegeLableDao.insertSelective(privilegeLable);
            TransactionUtils.afterCommitAsyncExecute(reportTask, () -> {
                privilegeLabelManager.initPrivilegeLabelUser(dto.getLabelIds(), privilegeLable.getId());
            });
        } else {
            privilegeLable.setId(id);
            privilegeLable.setLableName(dto.getName());
            if (null != existedSameNameLableId && !existedSameNameLableId.equals(id)) {
                throw new BusinessException("存在相同名称特权标签！");
            }
            List<PrivilegeItemUser> privilegeItemProducts = privilegeItemUserDao.findByLableIds(Lists.newArrayList(id), PrivilegeRelationTypeEnum.SPU_ID.getCode());
            List<Long> productIds = privilegeItemProducts.stream().map(PrivilegeItemUser::getValueId).distinct().collect(Collectors.toList());
            PrivilegeLable dbPrivilegeLable = privilegeLableDao.findById(id);
            if (dbPrivilegeLable.getStatus() == 1) {
                if (!dbPrivilegeLable.getLableName().equals(dto.getName())) {
                    throw new BusinessException("特权名称-活动开始后不可编辑！");
                }
                List<Long> paramProductIds = dto.getProductList().stream().map(PrivilegeLabelActivitySaveDTO.Product::getProductId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(paramProductIds)) {
                    if (!CollectionUtils.isEqualCollection(productIds, paramProductIds)) {
                        throw new BusinessException("特权商品-活动开始后不可编辑！");
                    }
                }
            }
            String labelId = dbPrivilegeLable.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                List<Long> dbLabelIds = Arrays.asList(labelId.split(",")).stream()
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                dbPrivilegeLable.setLabelIds(dbLabelIds);
                if (!CollectionUtils.isEqualCollection(dbLabelIds, dto.getLabelIds())) {
                    privilegeLable.setPrivilegeCnt(0);
                    privilegeLable.setAlreadyCnt(0);
                    privilegeLable.setNotSetCnt(0);
                }
            }
            privilegeLable.setStartTime(dto.getStartTime());
            privilegeLable.setEndTime(dto.getEndTime());
            privilegeLable.setUpdateTime(new Date());
            privilegeLable.setImageUrl(null == dto.getImageUrl()? "" : dto.getImageUrl());
            privilegeLable.setPeopleLimit(dto.getPeopleLimit());
            privilegeLable.setLinkType(dto.getLinkType());
            privilegeLable.setLink(dto.getLink());
            privilegeLable.setLimitNum(dto.getLimitNum());
            if (CollectionUtils.isNotEmpty(dto.getLabelIds())) {
                privilegeLable.setLabelId(String.join(",", dto.getLabelIds().stream().map(String::valueOf).toArray(String[]::new)));
            } else {
                privilegeLable.setLabelId("");
            }

            privilegeLableDao.updateSelective(privilegeLable);
            TransactionUtils.afterCommitAsyncExecute(reportTask, () -> {
                if (CollectionUtils.isEmpty(dbPrivilegeLable.getLabelIds())) {
                    if (CollectionUtils.isNotEmpty(dto.getLabelIds())) {
                        privilegeLabelManager.initPrivilegeLabelUser(dto.getLabelIds(), privilegeLable.getId());
                    }
                } else {
                    privilegeLabelManager.updatePrivilegeLabelUser(dbPrivilegeLable.getLabelIds(), dto.getLabelIds(), privilegeLable.getId());
                }

            });
        }
        privilegeItemUserDao.deleteByLableIdAndType(id, PrivilegeRelationTypeEnum.SPU_ID.getCode());
        if (CollectionUtils.isNotEmpty(dto.getProductList())) {
            List<PrivilegeItemUser> privilegeItemUsers = dto.getProductList().stream().map(product -> {
                PrivilegeItemUser privilegeItemUser = new PrivilegeItemUser();
                privilegeItemUser.setPrivilegeLableId(privilegeLable.getId());
                privilegeItemUser.setLableId(privilegeLable.getId());
                privilegeItemUser.setValueId(product.getProductId());
                privilegeItemUser.setLimitNum(product.getLimitNum());
                privilegeItemUser.setType(PrivilegeRelationTypeEnum.SPU_ID.getCode());
                privilegeItemUser.setDelflag(0);
                return privilegeItemUser;
            }).collect(Collectors.toList());
            privilegeItemUserDao.batchInsert(privilegeItemUsers);
        }

    }

    private void validateStockAndLimitNum(List<PrivilegeLabelActivitySaveDTO.Product> productList) {
        List<Long> productIds = productList.stream().map(PrivilegeLabelActivitySaveDTO.Product::getProductId).collect(Collectors.toList());
        List<SkuStockAndPurchaseLimitDTO> skuStockAndPurchaseLimitDTOS = skuDao.findLimitPurchaseAndStock(productIds);

        if (CollectionUtils.isNotEmpty(skuStockAndPurchaseLimitDTOS)) {
            for (PrivilegeLabelActivitySaveDTO.Product product : productList) {
                Optional<SkuStockAndPurchaseLimitDTO> minPurchase = skuStockAndPurchaseLimitDTOS.stream().filter(item -> item.getProductId().equals(product.getProductId())).min(Comparator.comparingInt(SkuStockAndPurchaseLimitDTO::getPurchaseLimit));
                Optional<SkuStockAndPurchaseLimitDTO> minStock = skuStockAndPurchaseLimitDTOS.stream().filter(item -> item.getProductId().equals(product.getProductId())).min(Comparator.comparingInt(SkuStockAndPurchaseLimitDTO::getStock));
                if (minPurchase.isPresent()) {
                    if (product.getLimitNum() > minPurchase.get().getPurchaseLimit()) {
                        throw new BusinessException("商品ID: " + product.getProductId() + " 可升级提数不能大于商品限购数量");
                    }
                }
                if (minStock.isPresent()) {
                    if (product.getLimitNum() > minStock.get().getStock()) {
                        throw new BusinessException("商品ID: " + product.getProductId() + "可升级提数不能大于库存数量");
                    }
                }
            }
        }
    }

    private void validateInProgressProduct(Long id, Date startTime, Date endTime,  List<Long> productIds) {
        List<PrivilegeLable> conflictPrivilegeLabels = privilegeLabelManager.selectConflictPrivilegeLabel(productIds, id, startTime, endTime);
        if (CollectionUtils.isNotEmpty(conflictPrivilegeLabels)) {
            throw new BusinessException("SPUID【" + conflictPrivilegeLabels.stream().map(e -> String.valueOf(e.getProductId())).distinct().collect(Collectors.joining(",")) + "】已参加未结束的升级提奶活动，请更换商品后重试！");
        }
    }


    @Override
    public PrivilegeLabelActivityDetailVO get(Long id) {
        PrivilegeLable privilegeLable = privilegeLableDao.findById(id);
        if (null == privilegeLable) {
            throw new BusinessException("特权标签不存在！");
        }
        PrivilegeLabelActivityDetailVO privilegeLabelActivityDetailVO = new PrivilegeLabelActivityDetailVO();
        BeanUtils.copy(privilegeLable, privilegeLabelActivityDetailVO);
        privilegeLabelActivityDetailVO.setName(privilegeLable.getLableName());
        privilegeLabelActivityDetailVO.setLabelIds(StringUtils.isNotEmpty(privilegeLable.getLabelId()) ? Arrays.stream(privilegeLable.getLabelId().split(",")).map(Long::parseLong).collect(Collectors.toList()) : null);
        List<PrivilegeItemUser> privilegeItemProducts = privilegeItemUserDao.findByLableIds(Lists.newArrayList(id), PrivilegeRelationTypeEnum.SPU_ID.getCode());
        List<Long> productIds =privilegeItemProducts.stream().map(PrivilegeItemUser::getValueId).distinct().collect(Collectors.toList());

        List<Product> productList = productDao.findByIds(productIds);

        List<Sku> skuList = skuDao.findByProductIds(productIds);
        Map<Long, List<Sku>> skuGroupMap = skuList.stream().collect(Collectors.groupingBy(Sku::getProductId));

        List<ItemProductGroup> itemProductGroups = itemProductGroupDao.selectByProductIds(productIds);
        Map<Long, List<ItemProductGroup>> itemGroupMap = itemProductGroups.stream().collect(Collectors.groupingBy(ItemProductGroup::getProductId));

        privilegeLabelActivityDetailVO.setProductList(productList.stream().map(product -> {
            PrivilegeLabelActivityDetailVO.Product productVO = new PrivilegeLabelActivityDetailVO.Product();
            productVO.setProductName(product.getProductName());
            productVO.setPicUrl(product.getPicUrl());
            productVO.setId(product.getId());
            productVO.setSaleStatus(product.getSaleStatus());
            List<ItemProductGroup> productGroups = itemGroupMap.get(product.getId());
            if (CollectionUtils.isNotEmpty(productGroups)) {
                productVO.setGroupNameList(productGroups.stream().map(ItemProductGroup::getName).collect(Collectors.toList()));
            }
            productVO.setLimitNum(privilegeItemProducts.stream().filter(item -> item.getValueId().equals(product.getId())).findFirst().get().getLimitNum());
            List<Sku> skus = skuGroupMap.get(product.getId());
            productVO.setSkuList(skus.stream().map(sku -> {
                PrivilegeLabelActivityDetailVO.Product.Sku skuVO = new PrivilegeLabelActivityDetailVO.Product.Sku();
                skuVO.setSkuCode(sku.getSkuCode());
                skuVO.setStock(sku.getStock());
                return skuVO;
            }).collect(Collectors.toList()));
            return productVO;
        }).collect(Collectors.toList()));
        return privilegeLabelActivityDetailVO;
    }

    @Override
    public void end(Long id) {
        PrivilegeLable privilegeLable = privilegeLableDao.findById(id);
        if (null == privilegeLable) {
            throw new BusinessException("特权标签不存在！");
        }
        privilegeLableDao.offlineLabelByLabelId(id, 2);
    }

    @Override
    public void start(Long id) {
        PrivilegeLable privilegeLable = privilegeLableDao.findById(id);
        if (null == privilegeLable) {
            throw new BusinessException("特权标签不存在！");
        }
        List<PrivilegeItemUser> productItems = privilegeItemUserDao.findPrivilegeLables(Lists.newArrayList(privilegeLable.getId()), PrivilegeRelationTypeEnum.SPU_ID.getCode());
        List<Long> productIds = productItems.stream().map(PrivilegeItemUser::getValueId).distinct().collect(Collectors.toList());
        validateInProgressProduct(privilegeLable.getId(), new Date(), privilegeLable.getEndTime(), productIds);
        privilegeLableDao.onlineLabelByLabelId(id, 1);
    }

    @Override
    public void delete(Long id) {
        PrivilegeLable privilegeLable = privilegeLableDao.findById(id);
        if (null == privilegeLable) {
            throw new BusinessException("特权标签已删除！");
        }
        if (privilegeLable.getStatus() == 1) {
            throw new BusinessException("特权标签已生效，无法删除！");
        }
        privilegeLableDao.deleteById(id);
        privilegeItemUserDao.deleteByLableIdAndType(id, PrivilegeRelationTypeEnum.SPU_ID.getCode());
    }


    /**
     * 根据起止时间和现在时间判断状态
     * @param startTime
     * @param nowTime
     * @param endTime
     * @return
     */
    public int checkStatus(Date startTime, Date nowTime, Date endTime){
        int status;
        //现在时间 < 开始时间 --未生效
        if(nowTime.compareTo(startTime) == -1){
            status = 0;
        }
        //开始时间 <= 现在时间 < 结束时间  --生效
        else if((nowTime.compareTo(startTime) == 1 || nowTime.compareTo(startTime) == 0)
                && endTime.compareTo(nowTime) == 1){
            status = 1;
        }
        //失效
        else{
            status = 2;
        }
        return status;
    }

    private void validateActivityTime( Date startTime, Date endTime) {
        if (startTime.after(endTime)) {
            throw new RuntimeException("活动结束时间不能早于开始时间！");
        }
    }
}
