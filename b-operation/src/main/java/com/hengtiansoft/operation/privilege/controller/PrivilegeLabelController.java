package com.hengtiansoft.operation.privilege.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.operation.privilege.service.PrivilegeLabelService;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelSearchDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLableDTO;
import com.hengtiansoft.privilege.entity.dto.SavePrivilegeItemDTO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLableVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Api(tags = "特权标签管理")
@RestController
@RequestMapping("/privilegeLabel")
public class PrivilegeLabelController {

    @Autowired
    private PrivilegeLabelService privilegeService;


    @ApiOperation(value = "新建")
    @PostMapping(value = "savePrivilegeLabel")
    public Response savePrivilegeLabel(@RequestBody PrivilegeLableDTO data){
        privilegeService.savePrivilegeLabel(data);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "立即生效")
    @GetMapping(value = "onlineByLabelId")
    public Response onlineByLabelId(@RequestParam Long labelId){
        privilegeService.onlineLabelByLabelId(labelId);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "立即失效")
    @GetMapping(value = "offlineByLabelId")
    public Response offlineByLabelId(@RequestParam Long labelId){
        privilegeService.offlineLabelByLabelId(labelId);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping(value = "deleteOneLabelById")
    public Response deleteOneLabelById(@RequestParam Long labelId){
        privilegeService.deleteOneLabelById(labelId);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "editLabelInfoById")
    public Response editLabelInfoById(@RequestBody PrivilegeLableDTO privilegeLableDTO){
        privilegeService.editLabelInfoById(privilegeLableDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "用户打标")
    @PostMapping(value = "userMark")
    public Response userMark(MultipartFile file){
        return ResponseFactory.success(privilegeService.userMark(file));
    }

    @ApiOperation(value = "标签列表数据")
    @PostMapping(value = "getAllLabelList")
    public Response<PageVO<PrivilegeLableVO>> getAllLabelList(@RequestBody PrivilegeLabelSearchDTO privilegeLabelSearchDTO){
        return ResponseFactory.success(privilegeService.getAllLabelList(privilegeLabelSearchDTO));
    }

    @ApiOperation(value = "特权商品维护")
    @PostMapping(value = "savePrivilegeItem")
    public Response savePrivilegeItem(@RequestBody @Validated SavePrivilegeItemDTO savePrivilegeItemDTO){
        privilegeService.savePrivilegeItem(savePrivilegeItemDTO);
        return ResponseFactory.success();
    }


    @ApiOperation(value = "修改特权商品")
    @PostMapping(value = "editPrivilegeItem")
    public Response editPrivilegeItem(@RequestBody @Validated SavePrivilegeItemDTO savePrivilegeItemDTO){
        privilegeService.editPrivilegeItem(savePrivilegeItemDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "特权商品信息")
    @GetMapping(value = "privilegeItemDetail")
    public Response<List<ProductBaseDTO>> privilegeItemDetail(@RequestParam Long labelId){
        return ResponseFactory.success(privilegeService.privilegeItemDetail(labelId));
    }

    @ApiOperation(value = "标签列表数据")
    @PostMapping(value = "getLabelList")
    public Response<PageVO<PrivilegeLableVO>> getLabelList(@RequestBody PrivilegeLabelSearchDTO privilegeLabelSearchDTO){
        return ResponseFactory.success(privilegeService.getLabelList(privilegeLabelSearchDTO));
    }

    @ApiOperation(value = "标签列表数据-下拉框用")
    @PostMapping(value = "getAll")
    public Response<PageVO<PrivilegeLableVO>> getAll(@RequestBody PrivilegeLabelSearchDTO privilegeLabelSearchDTO){
        return ResponseFactory.success(privilegeService.getAllLabel(privilegeLabelSearchDTO));
    }

}
