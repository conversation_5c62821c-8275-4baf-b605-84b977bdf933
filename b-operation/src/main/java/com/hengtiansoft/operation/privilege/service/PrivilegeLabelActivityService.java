package com.hengtiansoft.operation.privilege.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivityPageDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivitySaveDTO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityDetailVO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityPageVO;

/**
 * @Author: ocean
 * @Date: 2024-09-20 14:31
 * @Desc:
 */
public interface PrivilegeLabelActivityService {
    PageVO<PrivilegeLabelActivityPageVO> getPage(PrivilegeLabelActivityPageDTO dto);

    void save(PrivilegeLabelActivitySaveDTO dto);

    PrivilegeLabelActivityDetailVO get(Long id);

    void end(Long id);

    void start(Long id);

    void delete(Long id);
}
