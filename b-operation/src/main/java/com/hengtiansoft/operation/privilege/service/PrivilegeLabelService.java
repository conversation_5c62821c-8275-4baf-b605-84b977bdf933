package com.hengtiansoft.operation.privilege.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelSearchDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLableDTO;
import com.hengtiansoft.privilege.entity.dto.SavePrivilegeItemDTO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLableVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


public interface PrivilegeLabelService {


    /**
     * 写入一条新的特权标签数据
     * @param data
     * @return
     */
    void savePrivilegeLabel(PrivilegeLableDTO data);

    /**
     * 上线一条已下线的特权标签
     * @return
     */
    void onlineLabelByLabelId(Long labelId);

    /**
     * 下线一条已上线的特权标签
     * @return
     */
    void offlineLabelByLabelId(Long labelId);

    /**
     * 根据名称更新一条特权标签为删除状态
     * 条件：
     * 1: 名字匹配上
     * 2: 除已生效且已上线的标签,其余都可以删除
     * @param labelId
     * @return
     */
    void deleteOneLabelById(Long labelId);

    /**
     * 用户打标
     * @param file
     * @return
     */
    String userMark(MultipartFile file);

    /**
     * 根据标签id编辑标签内容
     * @param data
     * @return
     */
    void editLabelInfoById(PrivilegeLableDTO data);

    /**
     * 获取列表
     * @param privilegeLabelSearchDTO
     * @return
     */
    PageVO<PrivilegeLableVO> getAllLabelList(PrivilegeLabelSearchDTO privilegeLabelSearchDTO);

    /**
     * 获取列表
     * @param privilegeLabelSearchDTO
     * @return
     */
    PageVO<PrivilegeLableVO> getLabelList(PrivilegeLabelSearchDTO privilegeLabelSearchDTO);


    /**
     * 编辑特权商品
     * @param savePrivilegeItemDTO 商品相关信息
     */
    void editPrivilegeItem(SavePrivilegeItemDTO savePrivilegeItemDTO);


    /**
     * 保存特权商品
     * @param savePrivilegeItemDTO 商品相关信息
     */
    void savePrivilegeItem(SavePrivilegeItemDTO savePrivilegeItemDTO);


    /**
     * 特权标签商品信息
     * @param labelId
     * @return
     */
    List<ProductBaseDTO> privilegeItemDetail(Long labelId);

    PageVO<PrivilegeLableVO> getAllLabel(PrivilegeLabelSearchDTO privilegeLabelSearchDTO);

}
