package com.hengtiansoft.operation.privilege.service.impl;

import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.dto.PageParams;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.excel.ExcelImportHelper;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.operation.privilege.service.PrivilegeLabelService;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelSearchDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLableDTO;
import com.hengtiansoft.privilege.entity.dto.SavePrivilegeItemDTO;
import com.hengtiansoft.privilege.entity.po.PrivilegeItemUser;
import com.hengtiansoft.privilege.entity.po.PrivilegeLabelSearch;
import com.hengtiansoft.privilege.entity.po.PrivilegeLable;
import com.hengtiansoft.privilege.entity.po.UserImportInfo;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLableVO;
import com.hengtiansoft.privilege.manager.PrivilegeActManager;
import com.hengtiansoft.privilege.manager.PrivilegeLabelManager;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.entity.po.CustomerUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;

@Slf4j
@Service
public class PrivilegeLabelServiceImpl implements PrivilegeLabelService {

    @Autowired
    private PrivilegeLabelManager privilegeManager;

    @Autowired
    private PrivilegeActManager privilegeActManager;

    @Resource
    private CustomerUserDao customerUserDao;

    @Resource
    private ProductDao productDao;

    /**
     * 新建标签
     * 除已失效状态 和 已被删除,不可以和其他标签名重复
     * @param data
     * @return
     */
    @Override
    public void savePrivilegeLabel(PrivilegeLableDTO data){
        if(Objects.nonNull(data)){
            if(Strings.isBlank(data.getLableName()) || null == data.getStartTime()
            || null == data.getEndTime()){
                throw new BusinessException("信息不能为空");
            }else{
                //除标签名为已失效状态,不可以和其他标签名重复
                int existed = existed = privilegeManager.getLabelCountByLabelName(data.getLableName());
                if (existed == 0){
                    PrivilegeLable privilegeLable = new PrivilegeLable();
                    Date nowTime = new Date();
                    privilegeLable.setStatus(checkStatus(data.getStartTime(), nowTime, data.getEndTime()));
                    privilegeLable.setLableName(data.getLableName());
                    privilegeLable.setStartTime(data.getStartTime());
                    privilegeLable.setEndTime(data.getEndTime());

                    int saveResult = privilegeManager.savePrivilegeLabel(privilegeLable);
                    if(saveResult == 0){
                        throw new BusinessException("创建标签失败");
                    }
                }else{
                    throw new BusinessException("当前标签已存在");
                }
            }
        }
    }

    /**
     * 根据起止时间和现在时间判断状态
     * @param startTime
     * @param nowTime
     * @param endTime
     * @return
     */
    public int checkStatus(Date startTime, Date nowTime, Date endTime){
        int status;
        //现在时间 < 开始时间 --未生效
        if(nowTime.compareTo(startTime) == -1){
            status = 0;
        }
        //开始时间 <= 现在时间 < 结束时间  --生效
        else if((nowTime.compareTo(startTime) == 1 || nowTime.compareTo(startTime) == 0)
                && endTime.compareTo(nowTime) == 1){
            status = 1;
        }
        //失效
        else{
            status = 2;
        }
        return status;
    }

    /**
     * 已生效 且 已下线 且 未被删除
     * @param labelId
     * @return
     */
    @Override
    public void onlineLabelByLabelId(Long labelId){
        int status = 1;
        int updateResult = privilegeManager.onlineLabelByLabelId(labelId, status);
        if(0 == updateResult){
            throw new BusinessException("生效失败");
        }
    }

    /**
     * 已生效 且 已上线 且 未被删除
     * @param labelId
     * @return
     */
    @Override
    public void offlineLabelByLabelId(Long labelId){
        int status = 2;
        int updateResult = privilegeManager.offlineLabelByLabelId(labelId, status);
        if(0 == updateResult){
            throw new BusinessException("失效失败");
        }
    }

    /**
     * 可以删除条件
     * 1: 未生效 或 已失效
     * 2: 已生效 且 未上线
     * 最后都要满足未被删除
     * @param labelId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteOneLabelById(Long labelId){
        int delResult = privilegeManager.deleteOneLabelById(labelId);
        if(delResult > 0){
            int updateUserPrivilegeResult = privilegeManager.updateCustomerPrivilegeInDelLabel(labelId);
            int delPrivilegeItemUserInfo = privilegeManager.delPrivilegeItemUserInfoByLabelId(labelId, 1);
        }
        if(0 == delResult){
            throw new BusinessException("删除失败");
        }
    }


    /**
     * 由标签名获取标签id(如1000同样标签只存一个id),不在这个map中的标签   即为不存在标签
     * 具体实现思路同 getUserIdMap()函数一样
     * @param list
     * @return
     */
    public Map<String, Long> getLabelIdMap(List<UserImportInfo> list){
        Map<String, Long> labelIdMap = new HashMap<>();
        Set<String> noLabelIdSet = new HashSet<>();
        for (UserImportInfo item : list) {
            if (!labelIdMap.containsKey(item.getLabelName()) && !noLabelIdSet.contains(item.getLabelName())){
                Long labelId = privilegeManager.getLabelIdByName(item.getLabelName());
                if(null != labelId){
                    labelIdMap.put(item.getLabelName(), labelId);
                }else{
                    noLabelIdSet.add(item.getLabelName());
                }
            }
        }
        return labelIdMap;
    }

    /**
     * 由电话获取用户的id(如1000个同样用户只存一个id)  不在这个map中的电话 即为不存在用户
     * @param totalImportList
     * @return
     */
    public Map<String, Long> getUserIdMap(List<UserImportInfo> totalImportList){
        //  电话,用户id  这个map中只存在数据库中有数据的用户信息  不在这个map中即为不存在用户
        Map<String,Long> userIdMap = new HashMap<>();
        // 电话+标签名   这个set用于存不在数据库中的用户信息(电话+标签名)
        // 主要用图: 减少 不在数据库中且在excel中有大量同样信息数据 访问数据库的次数
        // 效果: 保证excel中 不重样的数据 只访问 一次 数据库
        Set<String> noUserIdSet = new HashSet<>();
        for (UserImportInfo item : totalImportList) {
            if(!userIdMap.containsKey(item.getPhone()) && !noUserIdSet.contains(item.getPhone())){
                CustomerUser first = StreamUtils.getFirst(customerUserDao.findByPhone(item.getPhone()));
                if(first != null){
                    userIdMap.put(item.getPhone(), first.getId());
                }else{
                    noUserIdSet.add(item.getPhone());
                }
            }
        }
        return userIdMap;
    }

    /**
     * 存取用户打标过的数据 如果这个excel中的数据在这个set中 即为已打过标数据
     * @param list
     * @param userIdMap
     * @param labelIdMap
     * @return
     */
    public Set<String> getMarkedData(List<UserImportInfo> list, Map<String, Long> userIdMap, Map<String, Long> labelIdMap){
        //  电话+标签名  存已打过标签的信息
        Set<String> markedDataSet = new HashSet<>();
        int marked = 0;
        String phone;
        String labelName;
        for (UserImportInfo item : list) {
            phone = item.getPhone();
            labelName = item.getLabelName();
            if(!markedDataSet.contains(phone + labelName)){
                marked = privilegeManager.checkUserMarked(userIdMap.get(phone), labelIdMap.get(labelName), 0);
                if(marked > 0){
                    markedDataSet.add(phone + labelName);
                }
            }
        }
        return markedDataSet;
    }

    /**
     * 设置错误信息,并按照提示文案要求拼接提示
     * @param list
     * @param userIdMap
     * @param labelIdMap
     * @param markedDataSet
     * @return
     */
    public String arrangeErrorMes(List<UserImportInfo> list, Map<String, Long> userIdMap, Map<String, Long> labelIdMap, Set<String> markedDataSet){
        StringBuilder msg = new StringBuilder();    //返回的错误信息
        Set<String> data = new HashSet();           //用户判断excel中的重复的数据
        String phone;
        String labelName;
        boolean dataExisted;            //数据是否重复
        boolean userExisted;            //用户是否存在
        boolean labelExisted;           //标签是否存在
        boolean markDataExisted;        //数据是否已打过标
        int index = 2;        //错误行数
        String info;          //错误信息
        String mid = ",";     //中间连接,一行数据有多个错误时用,连接
        String start;         //错误信息开头
        int currentLength;
        for (UserImportInfo item : list) {
            phone = item.getPhone();
            labelName = item.getLabelName();
            dataExisted = false;
            start = "第" + index + "行";
            currentLength = msg.length();
            if (!data.add(phone + labelName)){
                msg.append(start + "文件中存在重复数据");
                dataExisted = true;
            }

            userExisted = userIdMap.containsKey(phone);
            if(!userExisted){
                info = true == dataExisted ? mid : start;
                msg.append(info + "用户数据不存在");
            }

            labelExisted = labelIdMap.containsKey(labelName);
            if(!labelExisted){
                info = (true == dataExisted || true != userExisted) ? mid : start;
                msg.append(info + "标签数据不存在");
            }

            markDataExisted = markedDataSet.contains(phone + labelName);
            if(markDataExisted){
                info = (true == dataExisted || true != userExisted || true != labelExisted) ? mid : start;
                msg.append(info + "用户标签已存在");
            }

            msg = currentLength == msg.length() ? msg : msg.append("<br>");
            index++;
        }
        return  msg.toString();
    }

    /**
     * 1: 将excel中数据全部读入totalUserInfoList中
     * 2: 根据用户电话找到用户id存与userIdMap中 不在这个map中的电话 即为不存在用户
     * 3: 根据标签名找到标签id存有labelIdMap中  不在这个map中的标签 即为不存在标签
     * 4: 根据电话+标签名找到 已打过标数据存有markedDataSet中 在这个set中的数据 即为已打标过
     * 4: 将错误信息拼接 拼接时候还会判断数据在excel中是否重复
     * 5: 有错误即返回,无错误则设置excel中每条要写入数据库的字段
     * 6: 更新用户为特权用户
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String userMark(MultipartFile file){
        //存储excel中需要打标的数据
        List<UserImportInfo> totalUserInfoList = readUserInfo(file);

        //由电话获取用户的id(如1000个同样用户只存一个id),不在这个map中的电话 即为不存在用户
        Map<String, Long> userIdMap = getUserIdMap(totalUserInfoList);
        //由标签名获取标签id(如1000同样标签只存一个id),不在这个map中的标签   即为不存在标签
        Map<String, Long> labelIdMap = getLabelIdMap(totalUserInfoList);
        //存取用户打标过的数据 如果这个excel中的数据在这个set中 即为已打过标数据
        Set<String> markedDataSet = getMarkedData(totalUserInfoList, userIdMap, labelIdMap);
        //将错误信息按提示文案要求拼接信息
        String errorMsg = arrangeErrorMes(totalUserInfoList, userIdMap, labelIdMap, markedDataSet);
        if (StringUtils.isNotBlank(errorMsg)){
            return errorMsg;
        }
        List<PrivilegeItemUser> data = getUserMarkListData(totalUserInfoList, userIdMap, labelIdMap);

        totalUserInfoList.clear();
        labelIdMap.clear();
        markedDataSet.clear();

        //写入可以成功打标数据
        int result = privilegeManager.saveUserMarkData(data);
        if(result > 0){
            //批量更新用户为特权用户
            int setPrivilegeUser = privilegeManager.setPrivilegeCustomerUser(userIdMap);
        }
        userIdMap.clear();
        data.clear();
        if(0 == result){
            throw new BusinessException("用户打标失败");
        }
        return null;
    }

    public List<PrivilegeItemUser> getUserMarkListData(List<UserImportInfo> totalUserInfoList,Map<String, Long> userIdMap, Map<String, Long> labelIdMap){
        List<PrivilegeItemUser> userMarkData = new ArrayList<>();
        for (UserImportInfo item : totalUserInfoList) {
            PrivilegeItemUser data = new PrivilegeItemUser();
            Long userId = userIdMap.get(item.getPhone());
            Long labelId = labelIdMap.get(item.getLabelName());
            data.setValueId(userId);
            data.setLableId(labelId);
            data.setType(2);
            userMarkData.add(data);
        }
        return userMarkData;
    }



    /**
     * 先判断标签名 标签状态不为已失效和删除即为存在,如果存在改名字不可再用
     * 已生效且已上线 和 已失效的不能编辑 不能为已删除
     * @param privilegeLableDTO
     * @return
     */
    @Override
    public void editLabelInfoById(PrivilegeLableDTO privilegeLableDTO){
        String labelName = privilegeLableDTO.getLableName();
        if(StringUtils.isBlank(labelName)){
            throw new BusinessException("标签名不能为空！");
        }
        if(labelName.length() > 6){
            throw new BusinessException("特权标签名称不能超过6个字！");
        }
        //标签状态不为已失效和删除即为存在,如果存在改名字不可再用
        int existed = privilegeManager.getLabelCountByLabelNameAndId(privilegeLableDTO.getId(),labelName);
        if(existed > 0){
            throw new BusinessException("标签名已存在！");
        } else{
            PrivilegeLable data = new PrivilegeLable();
            Date nowTime = new Date();
            data.setId(privilegeLableDTO.getId());
            data.setLableName(labelName);
            data.setStartTime(privilegeLableDTO.getStartTime());
            data.setEndTime(privilegeLableDTO.getEndTime());
            data.setStatus(checkStatus(data.getStartTime(), nowTime, data.getEndTime()));

            //已生效且已上线 和 已失效的不能编辑
            int editResult = privilegeManager.editLabelInfoById(data);
            if(0 == editResult){
                throw new BusinessException("编辑失败！");
            }
        }
    }

    /**
     * 拿去列表数据
     * 不拿已删除的
     * 未生效和已失效时候 onlineFlag = -1 线上状态为 -
     * @param privilegeLableSearchDTO
     * @return
     */
    @Override
    public PageVO<PrivilegeLableVO> getAllLabelList(PrivilegeLabelSearchDTO privilegeLableSearchDTO){
        PageHelper.startPage(privilegeLableSearchDTO.getPageNum(), privilegeLableSearchDTO.getPageSize());
        PrivilegeLabelSearch privilegeLabelSearch = new PrivilegeLabelSearch();
        privilegeLabelSearch.setLabelName(privilegeLableSearchDTO.getLabelName());
        privilegeLabelSearch.setStatus(privilegeLableSearchDTO.getStatus());
        privilegeLabelSearch.setOnlineFlag(privilegeLableSearchDTO.getOnlineFlag());
        privilegeLabelSearch.setStartTime(privilegeLableSearchDTO.getStartTime());
        privilegeLabelSearch.setEndTime(privilegeLableSearchDTO.getEndTime());

        List<PrivilegeLable> privilegeLables = privilegeManager.getAllLabelList(privilegeLabelSearch);
        if(CollectionUtils.isEmpty(privilegeLables)){
            PageParams pageParams = new PageParams();
            pageParams.setPageNum(privilegeLableSearchDTO.getPageNum());
            pageParams.setPageSize(privilegeLableSearchDTO.getPageSize());
            return PageUtils.emptyPage(pageParams);
        }
//        List<Long> ids = StreamUtils.toList(privilegeLables, PrivilegeLable::getId);
        //用户数
/*        List<PrivilegeItemUserVO> labelUserList = privilegeActManager.findPrivilegeLables(ids, 2);
        Map<Long, List<PrivilegeItemUserVO>> labelUserMap = StreamUtils.group(labelUserList, PrivilegeItemUserVO::getLableId);*/

        //特权商品
/*        List<PrivilegeItemUserVO> labelItemList = privilegeActManager.findPrivilegeLables(ids, 1);
        Map<Long, List<PrivilegeItemUserVO>> labelItemMap = StreamUtils.group(labelItemList, PrivilegeItemUserVO::getLableId);*/

        return PageUtils.convert(privilegeLables, data -> {
            PrivilegeLableVO vo = BeanUtils.copy(data, PrivilegeLableVO::new);
/*            vo.setUserCnt(0);
            List<PrivilegeItemUserVO> userlist = labelUserMap.get(data.getId());
            if(CollectionUtils.isNotEmpty(userlist)){
                vo.setUserCnt(userlist.size());
            }*/
/*            List<PrivilegeItemUserVO> itemList = labelItemMap.get(data.getId());
            if(CollectionUtils.isNotEmpty(itemList)){
                List<Long> spuIds = StreamUtils.toList(itemList, PrivilegeItemUserVO::getValueId);
                List<Product> productList = productDao.findNotDeleteByIds(spuIds);
                if(CollectionUtils.isNotEmpty(productList)){
                    String productNameStr = StreamUtils.joinStringFilter(productList, "、", Product::getProductName, StringUtils::isNotBlank);
                    vo.setProductStr(productNameStr);
                    vo.setProductList(productList);
                }
            }*/
            return vo;
        });
    }

    @Override
    public PageVO<PrivilegeLableVO> getLabelList(PrivilegeLabelSearchDTO privilegeLabelSearchDTO) {
        List<PrivilegeLableVO> privilegeLables=privilegeActManager.findPrivilegeLablesById(privilegeLabelSearchDTO.getLabelIdList());
        if(CollectionUtils.isEmpty(privilegeLables)){
            PageParams pageParams = new PageParams();
            pageParams.setPageNum(privilegeLabelSearchDTO.getPageNum());
            pageParams.setPageSize(privilegeLabelSearchDTO.getPageSize());
            return PageUtils.emptyPage(pageParams);
        }
        return PageUtils.convert(privilegeLables, data -> BeanUtils.copy(data, PrivilegeLableVO::new));
    }

    /**
     * 读取excel数据 list形式
     * @param file
     * @return
     */
    public List<UserImportInfo> readUserInfo(MultipartFile file){

        List<UserImportInfo> readList = new ArrayList<>();
        try (InputStream is = file.getInputStream()) {
            readList = ExcelImportHelper.loadExcelDataFully(
                    ExcelImportHelper.getExcelTypeByName(file.getOriginalFilename()), is, 0, 1, UserImportInfo.class);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("导入的文件格式不正确");
        } catch (Exception e) {
            throw new BusinessException("导入有误");
        }
        if (CollectionUtils.isEmpty(readList)) {
            throw new BusinessException("导入数据为空");
        }

        return StreamUtils.filter(readList, userImportInfo -> Objects.nonNull(userImportInfo) && StringUtils.isNotBlank(userImportInfo.getPhone())
                && StringUtils.isNotBlank(userImportInfo.getLabelName()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePrivilegeItem(SavePrivilegeItemDTO savePrivilegeItemDTO){
        privilegeActManager.savePrivilegeItem(savePrivilegeItemDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPrivilegeItem(SavePrivilegeItemDTO savePrivilegeItemDTO) {
        List<PrivilegeLableVO> privilegeLableVOS = privilegeActManager.findPrivilegeLablesById(Collections.singletonList(savePrivilegeItemDTO.getId()));
        if (CollectionUtils.isEmpty(privilegeLableVOS)){
            throw new BusinessException("特权标签不存在");
        }
        privilegeActManager.editPrivilegeItem(savePrivilegeItemDTO);
    }

    @Override
    public List<ProductBaseDTO> privilegeItemDetail(Long labelId) {
        return privilegeActManager.privilegeItemDetail(labelId);
    }

    @Override
    public PageVO<PrivilegeLableVO> getAllLabel(PrivilegeLabelSearchDTO privilegeLableSearchDTO) {
        PageHelper.startPage(privilegeLableSearchDTO.getPageNum(), privilegeLableSearchDTO.getPageSize());
        PrivilegeLabelSearch privilegeLabelSearch = new PrivilegeLabelSearch();
        privilegeLabelSearch.setLabelName(privilegeLableSearchDTO.getLabelName());
        privilegeLabelSearch.setStatus(privilegeLableSearchDTO.getStatus());
        privilegeLabelSearch.setOnlineFlag(privilegeLableSearchDTO.getOnlineFlag());
        privilegeLabelSearch.setStartTime(privilegeLableSearchDTO.getStartTime());
        privilegeLabelSearch.setEndTime(privilegeLableSearchDTO.getEndTime());

        List<PrivilegeLable> privilegeLables = privilegeManager.getAllLabelList(privilegeLabelSearch);
        if(CollectionUtils.isEmpty(privilegeLables)){
            PageParams pageParams = new PageParams();
            pageParams.setPageNum(privilegeLableSearchDTO.getPageNum());
            pageParams.setPageSize(privilegeLableSearchDTO.getPageSize());
            return PageUtils.emptyPage(pageParams);
        }
        return PageUtils.convert(privilegeLables, data -> BeanUtils.copy(data, PrivilegeLableVO::new));
    }

}
