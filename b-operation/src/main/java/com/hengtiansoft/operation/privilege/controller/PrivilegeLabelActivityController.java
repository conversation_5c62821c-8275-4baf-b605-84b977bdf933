package com.hengtiansoft.operation.privilege.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.privilege.service.PrivilegeLabelActivityService;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivityPageDTO;
import com.hengtiansoft.privilege.entity.dto.PrivilegeLabelActivitySaveDTO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityDetailVO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLabelActivityPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Author: haiyang
 * @Date: 2024-09-20 14:24
 * @Desc:
 */
@RestController
@Api(tags = "特权标签活动")
@RequestMapping("/privilegeLabel/activity")
public class PrivilegeLabelActivityController {

    @Resource
    private PrivilegeLabelActivityService privilegeLabelActivityService;


    @ApiOperation("分页列表")
    @PostMapping("/page")
    public Response<PageVO<PrivilegeLabelActivityPageVO>> getPage(@RequestBody PrivilegeLabelActivityPageDTO dto) {
        return ResponseFactory.success(privilegeLabelActivityService.getPage(dto));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Void> save(@Validated @RequestBody PrivilegeLabelActivitySaveDTO dto) {
        privilegeLabelActivityService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<PrivilegeLabelActivityDetailVO> get(@RequestParam Long id) {
        return ResponseFactory.success(privilegeLabelActivityService.get(id));
    }

    @ApiOperation("结束")
    @GetMapping("/end")
    public Response<Void> end(@RequestParam Long id) {
        privilegeLabelActivityService.end(id);
        return ResponseFactory.success();
    }

    @ApiOperation("开始")
    @GetMapping("/start")
    public Response<Void> start(@RequestParam Long id) {
        privilegeLabelActivityService.start(id);
        return ResponseFactory.success();
    }


    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Void> delete(@RequestParam Long id) {
        privilegeLabelActivityService.delete(id);
        return ResponseFactory.success();
    }


}
