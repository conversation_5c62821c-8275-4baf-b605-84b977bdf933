package com.hengtiansoft.operation.item.service.impl;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.CommentDTO;
import com.hengtiansoft.item.entity.po.Comment;
import com.hengtiansoft.item.entity.po.CommentTag;
import com.hengtiansoft.item.entity.po.CommentTagJoin;
import com.hengtiansoft.item.entity.vo.CommentTagVO;
import com.hengtiansoft.item.entity.vo.CommentVO;
import com.hengtiansoft.item.manager.CommentManager;
import com.hengtiansoft.item.manager.CommentTagJoinManager;
import com.hengtiansoft.item.manager.CommentTagManager;
import com.hengtiansoft.item.utils.CommentTagUtil;
import com.hengtiansoft.item.utils.CommentUtil;
import com.hengtiansoft.operation.item.service.CommentService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.manager.CustomerUserManager;
import com.hengtiansoft.user.util.CustomerUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommentServiceImpl implements CommentService {
    @Resource
    private CommentManager commentManager;
    @Resource
    private CommentTagJoinManager commentTagJoinManager;
    @Resource
    private CommentTagManager commentTagManager;
    @Resource
    private CustomerUserManager customerUserManager;
    @Override
    public PageVO<CommentVO> getList(CommentDTO dto) {
        if(StringUtils.isNotBlank(dto.getPhone())){
            List<CustomerUser> customerUsers = customerUserManager.findByPhones(Lists.newArrayList(dto.getPhone()));
            if(CollectionUtils.isEmpty(customerUsers)){
                List<Comment> list = new ArrayList<>();
                return PageUtils.convert(list, data -> CommentUtil.convert2VO(data));
            }
            dto.setUserIdList(StreamUtils.toList(customerUsers, CustomerUser::getId));
        }
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<Comment> list = commentManager.getList(dto);
        List<Long> userIds = StreamUtils.toList(list, Comment::getUserId);
        List<CustomerUser> userList = customerUserManager.findByIdList(userIds);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(userList, CustomerUser::getId);
        return PageUtils.convert(list, data -> this.convert2VOWithUser(data, userMap.get(data.getUserId())));
    }

    private CommentVO convert2VOWithUser(Comment data, CustomerUser user) {
        CommentVO commentVO = CommentUtil.convert2VO(data);
        if(user != null){
            commentVO.setPhone(user.getPhone());
            commentVO.setGrade(user.getGrade());
            commentVO.setUserPic(user.getUserPic());
            if(Objects.equals(CustomerUserUtil.NICK_NAME, user.getNickName())){
                // 牛主人+手机号后四位
                commentVO.setNickName(user.getNickName() + StringUtils.right(user.getPhone(), 4));
            }else{
                commentVO.setNickName(user.getNickName());
            }
        }
        return commentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateShow(CommentDTO dto) {
        //校验:订单售后中不能评价，已评价不能评价
        Assert.notNull(dto.getIds(), "ids参数不能为空");
        Assert.notNull(dto.getShow(), "show参数不能为空");
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        FlagEnum flagEnum = FlagEnum.getEnum(dto.getShow(), "是否展示枚举类型出错");
        List<Comment> comments4Update = StreamUtils.toList(dto.getIds(),
                id -> CommentUtil.buildUpdateShow(id, flagEnum.getCode(), user.getUserName()));
        commentManager.batchUpdate(comments4Update);
    }

    @Override
    public CommentVO get(Long id) {
        Comment comment = commentManager.get(id);
        Assert.notNull(comment, "没有此评论");
        List<Long> userIds = Lists.newArrayList(comment.getUserId());
        List<CustomerUser> userList = customerUserManager.findByIdList(userIds);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(userList, CustomerUser::getId);
        CommentVO commentVO = this.convert2VOWithUser(comment, userMap.get(comment.getUserId()));
        List<CommentTagJoin> joins = commentTagJoinManager.findByCommentId(id);
        if(CollectionUtils.isNotEmpty(joins)){
            List<Long> tagIds = StreamUtils.toList(joins, CommentTagJoin::getTagId);
            List<CommentTag> commentTags = commentTagManager.findByIds(tagIds);
            List<CommentTagVO> commentTagVOS = StreamUtils.toList(commentTags, CommentTagUtil::convert2VO);
            commentVO.setCommentTagVOList(commentTagVOS);
        }
        return commentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(CommentDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        dto.setOperator(user.getUserName());
        Comment comment = CommentUtil.build4Delete(dto);
        commentManager.delete(comment);
    }
}
