package com.hengtiansoft.operation.exportcenter.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.operation.exportcenter.entity.dto.ExportCenterSearchDTO;
import com.hengtiansoft.operation.exportcenter.entity.vo.DataExportTaskVO;

/**
 * <AUTHOR>
 * @description
 **/
public interface FileExportCenterService {

    Long export(ReportFormExportBackupDTO dto);

    DataExportTaskVO queryById(Long id);

    PageVO<DataExportTaskVO> queryByType(ExportCenterSearchDTO exportCenterSearchDTO);

}
