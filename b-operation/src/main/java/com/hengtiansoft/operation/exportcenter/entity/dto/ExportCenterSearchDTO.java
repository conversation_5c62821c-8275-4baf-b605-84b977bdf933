package com.hengtiansoft.operation.exportcenter.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.hengtiansoft.common.entity.dto.PageParams;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 **/
@Data
@ApiModel("导出列表")
@EqualsAndHashCode(callSuper = true)
public class ExportCenterSearchDTO extends PageParams {

    @ApiModelProperty("导出文件的类型（1-月提货统计报表，2-财务报表，3-静态月提货统计报表，4-静态财务报表）")
    private Integer exportType;

    @ApiModelProperty("导出文件的类型（1-月提货统计报表，2-财务报表，3-静态月提货统计报表，4-静态财务报表）")
    private List<Integer> exportTypeList;

}