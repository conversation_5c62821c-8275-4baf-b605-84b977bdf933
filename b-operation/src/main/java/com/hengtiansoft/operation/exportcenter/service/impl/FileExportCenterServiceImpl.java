package com.hengtiansoft.operation.exportcenter.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.enumeration.SwapTypeEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.manager.CardManager;
import com.hengtiansoft.operation.content.service.PageModulePointAmountService;
import com.hengtiansoft.operation.exportcenter.entity.dto.ExportCenterSearchDTO;
import com.hengtiansoft.operation.exportcenter.entity.vo.DataExportTaskVO;
import com.hengtiansoft.operation.exportcenter.service.FileExportCenterService;
import com.hengtiansoft.operation.freeTrial.service.*;
import com.hengtiansoft.operation.fullReduce.service.FullReduceService;
import com.hengtiansoft.operation.item.service.ProductFrontClassifyService;
import com.hengtiansoft.operation.luckyDraw.service.LuckyRecordService;
import com.hengtiansoft.operation.order.service.OrderService;
import com.hengtiansoft.operation.pintuan.service.PtActivityService;
import com.hengtiansoft.operation.pointMall.service.PointMallService;
import com.hengtiansoft.operation.productSuggest.service.ProductSuggestItemService;
import com.hengtiansoft.operation.productSuggest.service.ProductSuggestService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.user.service.UserService;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.manager.MilkDispatchPlanManager;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
/**
 * <AUTHOR>
 * @description 文件导出中心
 **/
@Slf4j
@Service
public class FileExportCenterServiceImpl implements FileExportCenterService {

    @Value("${divisionDate:2022-02-20}")
    private String divisionDate;
    @Value("${local.tmp}")
    private String localDir;
    @Autowired
    private DataExportTaskDao dataExportTaskDao;
    @Autowired
    private DataSourceTransactionManager dstManager;
    @Autowired
    private MilkDispatchPlanManager milkDispatchPlanManager;
    @Autowired
    private OrderManager orderManager;
    @Autowired
    private AliyunOSSUtils aliyunOssUtils;
    @Autowired
    private CardManager cardManager;
    @Autowired
    private LuckyRecordService luckyRecordService;
    @Resource
    private FullReduceService fullReduceService;
    @Resource
    private FreeTrialCommentService freeTrialCommentService;
    @Resource
    private UserService userService;
    @Resource
    private FreeTrialFissionUserService freeTrialFissionUserService;
    @Resource
    private FreeTrialHelpService freeTrialHelpService;
    @Resource
    private PointMallService pointMallService;
    @Resource
    private ProductSuggestService productSuggestService;
    @Resource
    private ProductSuggestItemService productSuggestItemService;
    @Resource
    private PageModulePointAmountService pageModulePointAmountService;
    @Resource
    private PtActivityService ptActivityService;
    @Resource
    private ProductFrontClassifyService productFrontClassifyService;
    @Resource
    private FreeTrialVoteService freeTrialVoteService;
    @Resource
    private FreeTrialService freeTrialService;
    @Resource
    private FreeTasteService freeTasteService;
    @Resource
    private OrderService orderService;

    @Resource
    private FullTrialService fullTrialService;

    private static final ThreadPoolExecutor EXECUTOR;

    private static final ThreadFactory NAMED_THREAD_FACTORY;

    static {
        NAMED_THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("fileExportCenter-pool-%d").build();
        EXECUTOR = new ThreadPoolExecutor(5, 7, 500, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(), NAMED_THREAD_FACTORY, new ThreadPoolExecutor.AbortPolicy());
        EXECUTOR.allowCoreThreadTimeOut(true);
    }

    @Override
    public Long export(ReportFormExportBackupDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnumByCode(dto.getExportType());
        if (null == enumByCode){
            throw new BusinessException("导出类型不存在");
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);
        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
       if(Lists.newArrayList(
                FileExportCenterEnum.inventoryForm,
                FileExportCenterEnum.reportForm,
                FileExportCenterEnum.static_reportForm,
                FileExportCenterEnum.card_summary).contains(enumByCode)){
            Integer belongToBU = dto.getBelongToBU();
            if (Objects.nonNull(belongToBU)) {
                if (belongToBU == 1) {
                    dataExportTask.setExportName(enumByCode.getFileName() + "_液奶事业部" + date);
                } else if (belongToBU == 2) {
                    dataExportTask.setExportName(enumByCode.getFileName() + "_冰淇淋事业部" + date);
                } else if(belongToBU == 3){
                    dataExportTask.setExportName(enumByCode.getFileName() + "_肉业事业部" + date);
                }
            }
        }
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(dto.getExportType());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());
        dto.setFileName(enumByCode.getFileName() + date);
        Long exportId;
        try {
            exportId = dataExportTaskDao.saveOne(dataExportTask);
            dstManager.commit(transaction);
            dto.setId(exportId);
        } catch (Exception e) {
            log.error("创建导出任务出错{}", e);
            dstManager.rollback(transaction);
            throw new BusinessException("创建导出任务出错");
        }
        try {
            EXECUTOR.execute(() -> this.monthlyFormExport(dto));
        } catch (Exception e) {
            throw new BusinessException("创建导出任务失败");
        }
        return exportId;
    }

    private DataExportTask doExport(ReportFormExportBackupDTO dto, File localTmpDirectory, Long exportId) throws IOException {
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnumByCode(dto.getExportType());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }
        dto.setFileName(enumByCode.getFileName() + dto.getFileName());
        if (Lists.newArrayList(
                FileExportCenterEnum.inventoryForm,
                FileExportCenterEnum.reportForm,
                FileExportCenterEnum.static_reportForm,
                FileExportCenterEnum.card_summary).contains(enumByCode)) {
            Integer belongToBU = dto.getBelongToBU();
            if (Objects.nonNull(belongToBU)) {
                if (belongToBU == 1) {
                    dto.setFileName(enumByCode.getFileName() + "_液奶事业部" + dto.getFileName());
                } else if (belongToBU == 2) {
                    dto.setFileName(enumByCode.getFileName() + "_冰淇淋事业部" + dto.getFileName());
                }
            }
        }
        dto.setFileSuffix(enumByCode.getSubfix());
        File exportFile = new File(localTmpDirectory, dto.getFileName() + dto.getFileSuffix());
        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportId);
        try (ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath()).build()) {

            switch (enumByCode) {
                case monthly:
                    milkDispatchPlanManager.monthlyFormExport(dto, excelWriter);
                    break;
                case reportForm:
                    orderManager.reportFormExport(dto, excelWriter);
                    break;
                case static_monthly:
                    // 查备份表
                    // 实际没有用到 所以不做改造。
/*                    Map<String, List<MonthlyPlanFormListVO>> staticMonthlyMap = milkDispatchPlanManager.monthlyFormExportFromBackup(dto);
                    for (Map.Entry<String, List<MonthlyPlanFormListVO>> entry : staticMonthlyMap.entrySet()) {
                        context.createSheet(entry.getKey());
                        context.writeData(entry.getValue());
                        context.finishWriteData(MonthlyPlanFormListVO.class);
                    }*/
                    break;
                case static_reportForm:
                    Date date = DateUtil.stringToDate(divisionDate, DateUtil.SIMPLE_YMD);
                    Date searchTime = DateUtil.stringToDate(dto.getDate(), DateUtil.SIMPLE_YMD);
                    if(searchTime.before(date)){
                        orderManager.reportFormExportFromBackup(dto, excelWriter);
                    }else{
                        orderManager.reportFormExportFromZipper(dto, excelWriter);
                    }
                    break;
                case inventoryForm:
                    orderManager.inventoryFormExport(dto, excelWriter);
                    break;
                case second_pushForm:
                    // 实际没有用到 所以不做改造。
/*                    List<DispatchPlanListVO> planListVOList= milkDispatchPlanManager.batchPush(dto.getPlanIdList());
                    context.createSheet("二次推送");
                    context.writeData(planListVOList,true);
                    context.finishWriteData(DispatchPlanListVO.class);*/
                    break;
                case daily_no_count_user:
                    cardManager.noRemainingHis(dto.getDate(), excelWriter);
                    break;
                case lucky_record:
                    luckyRecordService.export(dto, excelWriter);
                    break;
                case full_reduce:
                    fullReduceService.export(dto, excelWriter);
                    break;
                case free_trial_comment:
                    freeTrialCommentService.export(dto, excelWriter);
                    break;
                case user_match:
                    userService.export(dto, excelWriter);
                    break;
                case free_trial_fission_user:
                    freeTrialFissionUserService.export(dto, excelWriter);
                    break;
                case free_trial_help_user:
                    freeTrialHelpService.export(dto, excelWriter);
                    break;
                case point_mall_coupon:
                    dto.setSwapType(SwapTypeEnum.COUPON.getCode());
                    pointMallService.export(dto, excelWriter);
                    break;
                case point_mall_product:
                    dto.setSwapType(SwapTypeEnum.PRODUCT.getCode());
                    pointMallService.export(dto, excelWriter);
                    break;
                case product_suggest:
                    productSuggestService.export(dto, excelWriter);
                    break;
                case product_suggest_item:
                    productSuggestItemService.export(dto, excelWriter);
                    break;
                case page_module_point_amount:
                    pageModulePointAmountService.export(dto, excelWriter);
                    break;
                case pt_activity:
                    ptActivityService.export(dto, excelWriter);
                    break;
                case product_front_classify:
                    productFrontClassifyService.export(dto, excelWriter);
                    break;
                case card_summary:
                    orderManager.cardSummaryList(dto, excelWriter);
                    break;
                case free_trial_comment_hot:
                    freeTrialCommentService.exportHot(dto, excelWriter);
                    break;
                case free_trial_vote:
                    freeTrialVoteService.export(dto, excelWriter);
                    break;
                case free_trial_vote_free_product:
                    freeTrialVoteService.exportFreeProductList(dto, excelWriter);
                    break;
                case free_trial_vote_user:
                    freeTrialVoteService.exportUserList(dto, excelWriter);
                    break;
                case free_trial_wait:
                    freeTrialService.exportWaitList(dto, excelWriter);
                    break;
                case free_taste:
                    freeTasteService.exportWaitList(dto, excelWriter);
                    break;
                case full_trial:
                    fullTrialService.exportWaitList(dto, excelWriter);
                    break;
                case full_or_free_pay_user:
                    orderService.exportPayUserList(dto, excelWriter);
                    break;
                default:
                    break;
            }
            excelWriter.finish();
            //文件流上传oss
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input,
                    dto.getFileName() + dto.getFileSuffix());
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        }catch (Exception e){
            log.error("文件导出失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        }
        return dataExportTask;
    }

    private void monthlyFormExport(ReportFormExportBackupDTO dto) {
        DataExportTask dataExportTask = new DataExportTask();
        Long exportId = dto.getId();
        dataExportTask.setId(exportId);
        //将上传的文件流先保存到本地文件再上传，避免上传大文件时失败
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        dto.setFileName(date);
        // 使用时间(yyyyMMddHHmmss)+UUID作为唯一的名字
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        if (dto.getExportType().equals(FileExportCenterEnum.static_reportForm.getCode())) {
            uniqueName = localDir + DateUtil.dateToString(DateUtil.stringToDate(dto.getDate(), DateUtil.SIMPLE_YMD) ,DateUtil.SIMPLE_DATE_YMD) + date + UUID.randomUUID().toString();
            dto.setFileName(DateUtil.dateToString(DateUtil.stringToDate(dto.getDate(), DateUtil.SIMPLE_YMD) ,DateUtil.SIMPLE_DATE_YMD) + "_" + DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis());
        }

        File localTmpDirectory = new File(uniqueName);
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }
            dataExportTask = doExport(dto, localTmpDirectory, exportId);
        } catch (IOException e) {
            log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
        } catch (Exception e) {
            log.error("导出任务失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);
        try {
            dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            dstManager.commit(transaction);
        } catch (Exception e) {
            log.error("创建导出任务出错{}", e);
            dstManager.rollback(transaction);
        }
    }

    @Override
    public DataExportTaskVO queryById(Long id) {
        DataExportTask dataExportTask = dataExportTaskDao.selectById(id);
        return BeanUtils.copy(dataExportTask, DataExportTaskVO::new);
    }

    @Override
    public PageVO<DataExportTaskVO> queryByType(ExportCenterSearchDTO exportCenterSearchDTO) {
        PageHelper.startPage(exportCenterSearchDTO.getPageNum(), exportCenterSearchDTO.getPageSize());
        List<DataExportTask> dataExportTasks;
        if(CollectionUtils.isNotEmpty(exportCenterSearchDTO.getExportTypeList())){
            dataExportTasks = dataExportTaskDao.queryList(exportCenterSearchDTO.getExportTypeList());
        }else{
            dataExportTasks = dataExportTaskDao.queryList(exportCenterSearchDTO.getExportType());
        }
        if (CollectionUtils.isEmpty(dataExportTasks)){
           return PageUtils.emptyPage();
        }
        return PageUtils.convert(dataExportTasks, data -> BeanUtils.copy(data, DataExportTaskVO::new));
    }
}