package com.hengtiansoft.operation.exportcenter.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.operation.exportcenter.entity.dto.ExportCenterSearchDTO;
import com.hengtiansoft.operation.exportcenter.entity.vo.DataExportTaskVO;
import com.hengtiansoft.operation.exportcenter.service.FileExportCenterService;

/**
 * <AUTHOR>
 * @description
 **/
@RestController
@Api(tags = "文件导出中心")
@RequestMapping(value = "fileExport")
public class ExportCenterController {


    @Autowired
    private FileExportCenterService fileExportCenterService;

    @ApiOperation(value = "列表")
    @PostMapping(value = "queryList")
    public Response<PageVO<DataExportTaskVO>> queryList(@RequestBody ExportCenterSearchDTO exportCenterSearchDTO){
        return ResponseFactory.success(fileExportCenterService.queryByType(exportCenterSearchDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "detail")
    public Response<DataExportTaskVO> detail(@RequestParam Long id){
        return ResponseFactory.success(fileExportCenterService.queryById(id));
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "export")
    public Response<Long> export(@RequestBody ReportFormExportBackupDTO dto) {
        fileExportCenterService.export(dto);
        return ResponseFactory.success();
    }

}