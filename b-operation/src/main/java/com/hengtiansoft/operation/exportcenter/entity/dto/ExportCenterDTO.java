package com.hengtiansoft.operation.exportcenter.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 导出中心基础DTO
 **/
@Data
public class ExportCenterDTO {

    @ApiModelProperty("导出文件的类型  1：月提货统计报表， 2：财务报表")
    private Integer exportType;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件后缀")
    private String fileSuffix;

    @ApiModelProperty(value = "id", hidden = true)
    private Long id;

}