package com.hengtiansoft.operation.exportcenter.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 **/
@Data
public class DataExportTaskVO {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 导出状态
     * NEW(0, "新建"), PROCESSING(1, "处理中"), FINISH(2, "完成"), FAILURE(3, "失败"),
     */
    @ApiModelProperty("导出状态 : 0新建 1处理中 2完成 3失败")
    private Integer status;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String fileUrl;

    /**
     * 文件类型
     */
    @ApiModelProperty("导出文件的类型（1-月提货统计报表，2-财务报表）")
    private Integer exportType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String operation;

    @ApiModelProperty("导出名称")
    private String exportName;
    /**
     * 传参
     */
    @ApiModelProperty("传参")
    private String exportParam;

    @ApiModelProperty("失败原因")
    private String failedReason;

}