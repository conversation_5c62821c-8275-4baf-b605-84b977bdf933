package com.hengtiansoft.operation.logistics.service.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.operation.logistics.service.LogisticsInfoService;
import com.hengtiansoft.order.dao.LogisticsInfoDao;
import com.hengtiansoft.order.dao.OrderPackageDao;
import com.hengtiansoft.order.entity.dto.LogisticsInfoExcelDTO;
import com.hengtiansoft.order.entity.po.LogisticsInfo;
import com.hengtiansoft.order.entity.po.OrderPackage;
import com.hengtiansoft.order.enums.LogisticsStatusEnum;
import com.hengtiansoft.thirdpart.util.MilkCreateClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class LogisticsInfoServiceImpl implements LogisticsInfoService {


    @Resource
    private LogisticsInfoDao logisticsInfoDao;

    @Resource
    private OrderPackageDao orderPackageDao;

    @Resource
    private MilkCreateClient milkCreateClient;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logisticsInfoUpload(List<LogisticsInfoExcelDTO> list) {

        //参数校验
        checkParams(list);

        List<String> logisticsNoList = StreamUtils.convert(list, x -> x.getLogisticsNo());
        List<OrderPackage> orderPackageList = orderPackageDao.findByTrackingNos(logisticsNoList);
        Map<String, OrderPackage> orderPackageMap = StreamUtils.toMap(orderPackageList, x -> x.getPackageNo());
        List<LogisticsInfoExcelDTO> logisticsListFromCreate = new ArrayList<>();

        Map<String, List<LogisticsInfoExcelDTO>> logisticsInfoMap = StreamUtils.group(list, x -> x.getLogisticsNo().trim());
        for (Map.Entry<String, List<LogisticsInfoExcelDTO>> entry : logisticsInfoMap.entrySet()) {
            List<LogisticsInfo> logisticsInfos = logisticsInfoDao.queryByLogisticsNo(entry.getKey());
            List<LogisticsInfoExcelDTO> logisticsInfoExcelDTOS = entry.getValue();

            Map<Integer, LogisticsInfo> LogisticsInfoDataMap = StreamUtils.toMap(logisticsInfos, x -> x.getStatus());
            Map<Integer, LogisticsInfoExcelDTO> LogisticsInfoExcelMap = StreamUtils.toMap(logisticsInfoExcelDTOS, x -> x.getStatus());

            for (LogisticsInfoExcelDTO logisticsInfo : logisticsInfoExcelDTOS) {

                //校验物流状态和时间
                checkStatusAndTime(LogisticsInfoDataMap, LogisticsInfoExcelMap, logisticsInfo);

                LogisticsInfo logisticsInfoData = LogisticsInfoDataMap.get(logisticsInfo.getStatus());
                LogisticsInfo updateOrSaveInfo = new LogisticsInfo();
                BeanUtils.copy(logisticsInfo, updateOrSaveInfo);
                if (Objects.isNull(logisticsInfoData)) {
                    logisticsInfoDao.save(updateOrSaveInfo);
                } else {
                    updateOrSaveInfo.setId(logisticsInfoData.getId());
                    logisticsInfoDao.update(updateOrSaveInfo);
                }

                if (!orderPackageMap.containsKey(logisticsInfo.getLogisticsNo())) {
                    logisticsListFromCreate.add(logisticsInfo);
                }
            }
        }
        //同步物流信息给创新
        Map<String, Object> map = new HashMap<>();
        map.put("LogisticsInfoList", logisticsListFromCreate);
        milkCreateClient.synLogistics(map);

    }

    private void checkStatusAndTime(Map<Integer, LogisticsInfo> LogisticsInfoDataMap, Map<Integer, LogisticsInfoExcelDTO> LogisticsInfoExcelMap, LogisticsInfoExcelDTO logisticsInfo) {
        if (LogisticsStatusEnum.SHIPPING == LogisticsStatusEnum.getEnum(logisticsInfo.getStatus())) {
            LogisticsInfo info = LogisticsInfoDataMap.get(LogisticsStatusEnum.COLLECTED.getCode());
            LogisticsInfoExcelDTO infoExcelDTO = LogisticsInfoExcelMap.get(LogisticsStatusEnum.COLLECTED.getCode());

            if (Objects.isNull(info) && Objects.isNull(infoExcelDTO)) {
                throw new BusinessException("物流单号:" + logisticsInfo.getLogisticsNo() + ",缺少[已揽收]前置物流状态");
            } else {
                Date d = Objects.isNull(info) ? infoExcelDTO.getTime() : info.getTime();
                if (d.after(logisticsInfo.getTime())) {
                    throw new BusinessException("物流单号:" + logisticsInfo.getLogisticsNo() + ",[配送中]的时间不能早于[已揽收]");
                }
            }
        }

        if (LogisticsStatusEnum.SIGNED == LogisticsStatusEnum.getEnum(logisticsInfo.getStatus())) {
            LogisticsInfo info = LogisticsInfoDataMap.get(LogisticsStatusEnum.SHIPPING.getCode());
            LogisticsInfoExcelDTO infoExcelDTO = LogisticsInfoExcelMap.get(LogisticsStatusEnum.SHIPPING.getCode());

            if (Objects.isNull(info) && Objects.isNull(infoExcelDTO)) {
                throw new BusinessException("物流单号:" + logisticsInfo.getLogisticsNo() + ",缺少[配送中]前置物流状态");
            } else {
                Date d = Objects.isNull(info) ? infoExcelDTO.getTime() : info.getTime();
                if (d.after(logisticsInfo.getTime())) {
                    throw new BusinessException("物流单号:" + logisticsInfo.getLogisticsNo() + ",[已签收]的时间不能早于[配送中]");
                }
            }
        }
    }

    private void checkParams(List<LogisticsInfoExcelDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("请完善信息");
        }
        List<String> repeatPhones = Lists.newArrayList();
        int i = 1;
        for (LogisticsInfoExcelDTO excelDTO : list) {
            i++;
            if (StringUtils.isAnyBlank(excelDTO.getLogisticsNo(), excelDTO.getDesc())) {
                throw new BusinessException("第" + i + "行, 请完善信息");
            }

            if (Objects.isNull(excelDTO.getTime())) {
                throw new BusinessException("第" + i + "行, 请完善信息");
            }

            if (repeatPhones.contains(excelDTO.getLogisticsNo().trim() + excelDTO.getStatus())) {
                throw new BusinessException("第" + i + "行, 内容重复");
            }
            repeatPhones.add(excelDTO.getLogisticsNo().trim() + excelDTO.getStatus());
        }
    }


}
