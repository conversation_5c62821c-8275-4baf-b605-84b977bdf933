package com.hengtiansoft.operation.dispatch.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchPlanListVO;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.entity.dto.DispatchPlanSaveDTO;
import com.hengtiansoft.order.entity.dto.DispatchPlanSearchDTO;
import com.hengtiansoft.order.entity.vo.ExpressInfoVO;
import com.hengtiansoft.order.entity.vo.PlanedMilkStatisticalTableVO;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanActVO;

import java.util.List;

/**
 * Created by yuchenchen on 2020/9/17 14:41
 */
public interface DispatchPlanService {
    /**
     * 配送计划列表
     *
     * @param searchDTO
     * @return
     */
    PageVO<DispatchPlanListVO> search(DispatchPlanSearchDTO searchDTO);


    /**
     * 配送计划列表 - 导出定制
     *
     * @param searchDTO
     * @param detailDTO
     * @return
     */
    PageVO<DispatchPlanListVO> searchForExport(DispatchPlanSearchDTO searchDTO, UserDetailDTO detailDTO);

    /**
     * 暂停计划
     * @param id
     */
    void pause(Long id);

    /**
     * 激活计划
     * @param id
     */
    DispatchPlanActVO activate(Long id);

    /**
     * 删除计划
     * @param id
     */
    void delete(Long id);

    /**
     * 编辑
     * @param dto
     */
    void update(DispatchPlanSaveDTO dto);

    /**
     * 编辑(同个规则下所有计划)
     * @param dto
     */
    void updateAll(DispatchPlanSaveDTO dto);

    /**
     * 推送
     * @param id
     */
    void push(Long id);

    /**
     * 物流查询
     * @param expressCode
     * @param orderNo
     * @param planId
     * @return
     */
    ExpressInfoVO queryExpress(String expressCode, String orderNo, Long planId ,String expressCompany);

    PlanedMilkStatisticalTableVO dataStatistics();

    List<Long> expirePlan();

    void exportOrder(DispatchPlanSearchDTO searchDTO);
}
