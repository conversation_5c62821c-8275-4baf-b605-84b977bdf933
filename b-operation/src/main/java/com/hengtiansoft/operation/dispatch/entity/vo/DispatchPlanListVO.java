package com.hengtiansoft.operation.dispatch.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 */
@Data
public class DispatchPlanListVO {

    @ApiModelProperty("配送计划id")
    private Long id;

    @ApiModelProperty("配送规则id")
    private Long ruleId;

    @ApiModelProperty("商品物料编码")
    private String skuCode;

    @ApiModelProperty("商品skuId")
    private Long skuId;

    @ApiModelProperty("商品id")
    private Long productId;

    @ApiModelProperty("商品规格描述")
    private String skuDesc;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("商品单位")
    private String unit;

    @ApiModelProperty("奶卡卡号")
    private String cardNumber;

    @ApiModelProperty("卡券名称")
    private String cardName;

    @ApiModelProperty("奶卡类别id")
    private Long categoryId;

    @ApiModelProperty("用户手机号")
    private String userPhone;

    @ApiModelProperty("是否奶卡白名单：0否1是")
    private Integer whiteList;

    @ApiModelProperty("配送时间")
    private String dispatchDate;

    @ApiModelProperty("配送模式 0-工作日送达 1-周末送达")
    private Integer dispatchMode;

    @ApiModelProperty("配送提数")
    private Integer milkAmount;

    @ApiModelProperty("计划状态（1-计划中，2-配送中，3-已签收，4-暂停中，5-已过期）")
    private Integer planStatus;

    @ApiModelProperty("计划状态描述")
    private String planStatusDesc;

    @ApiModelProperty("推送状态（1-未推送，2-推送成功，3-推送失败）")
    private Integer pushFlag;

    @ApiModelProperty("推送状态描述")
    private String pushFlagDesc;

    @ApiModelProperty("推送失败原因")
    private String pushFailReason;

    @ApiModelProperty("收货人名称")
    private String receiverName;

    @ApiModelProperty("收货人电话")
    private String receiverPhone;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("省code")
    private String provinceCode;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("市code")
    private String cityCode;

    @ApiModelProperty("区")
    private String district;

    @ApiModelProperty("区code")
    private String districtCode;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("旺店通单号")
    private String planOrderNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("物流包裹列表")
    private List<PlanPackageVO> packageList;

    @ApiModelProperty("类型 0- 普通提货 1-普通特权提货 2-低温奶提货  3- 低温奶特权提货 ")
    private Integer ruleType;

    @ApiModelProperty("主体 1-奶卡 2-周期购")
    private Integer subject;

    @ApiModelProperty("用户备注")
    private String userRemark;

    @ApiModelProperty("温度 0：常温  1：低温")
    private Integer temperature;

    @ApiModelProperty("除数，计算推送中台发货数量")
    private Integer divisor;


    @ApiModelProperty("中台商品编码")
    private String skuEcode;

    @ApiModelProperty("中台商品数量")
    private Integer number;

    @ApiModelProperty("是否组合商品")
    private Integer isCombine;
}
