package com.hengtiansoft.operation.dispatch.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchRuleDetailVO;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchRuleListVO;
import com.hengtiansoft.order.entity.dto.DispatchAmountDTO;
import com.hengtiansoft.order.entity.dto.DispatchRuleUpdateDTO;
import com.hengtiansoft.order.entity.dto.DispatchRuleSaveDTO;
import com.hengtiansoft.order.entity.dto.DispatchRuleSearchDTO;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanActVO;

import java.util.List;

/**
 * @Author: <EMAIL>
 */
public interface DispatchRuleService {

    /**
     * 新增配送规则
     *
     * @param param
     */
    void create(DispatchRuleSaveDTO param);

    /**
     * 编辑配送规则
     *
     * @param param
     */
    void update(DispatchRuleSaveDTO param);

    /**
     * 配送规则详情
     *
     * @param id
     * @return
     */
    DispatchRuleDetailVO detail(Long id);

    /**
     * 暂停规则
     * 暂停规则下的计划
     * @param id
     */
    void pause(Long id);

    /**
     * 激活规则
     * 激活规则下的计划
     * @param id
     */
    DispatchPlanActVO activate(Long id);

    /**
     * 删除规则
     * 删除规则下的计划
     * @param id
     */
    void delete(Long id);

    /**
     * 配送规则列表
     *
     * @param searchDTO
     * @return
     */
    PageVO<DispatchRuleListVO> search(DispatchRuleSearchDTO searchDTO);

    /**
     * 获取配送规则的剩余提数
     *
     * @param dto
     * @return
     */
    Integer getDispatchAmount(DispatchAmountDTO dto);

    /**
     * 获取奶卡的剩余提数
     *
     * @param cardNumber
     * @param skuId
     * @param ruleId
     * @return
     */
    Integer getCardLeftDispatchAmount(String cardNumber, Long skuId, Long ruleId);

    /**
     * 校验商品能否配送
     * @param productId
     * @param provinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    Boolean checkCanDelivery(Long productId, String provinceCode, String cityCode, String districtCode);

    Integer purchaseLimit(Long skuId, Long ruleId, String cardNumber);

    List<String> getLowMilkDispatchDay(Integer times, Long productId);

    List<String> userRemarkExpress();

    Integer batchUpdateRemark(List<DispatchRuleUpdateDTO> dto);
}
