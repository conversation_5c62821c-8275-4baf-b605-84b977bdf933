package com.hengtiansoft.operation.dispatch.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.config.RequestLogger;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchRuleDetailVO;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchRuleListVO;
import com.hengtiansoft.operation.dispatch.service.DispatchRuleService;
import com.hengtiansoft.order.entity.dto.DispatchAmountDTO;
import com.hengtiansoft.order.entity.dto.DispatchRuleUpdateDTO;
import com.hengtiansoft.order.entity.dto.DispatchRuleSaveDTO;
import com.hengtiansoft.order.entity.dto.DispatchRuleSearchDTO;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanActVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: <EMAIL>
 */
@Api(tags = "配送规则")
@RestController
@RequestMapping("/dispatch/rule")
public class DispatchRuleController {

    @Autowired
    private DispatchRuleService dispatchRuleService;

    @ApiOperation(value = "编辑")
    @PostMapping("/update")
    @RequestLogger(value = "配送规则编辑")
    public Response<Object> update(@RequestBody DispatchRuleSaveDTO param) {
        dispatchRuleService.update(param);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public Response<DispatchRuleDetailVO> detail(@RequestParam Long id) {
        return ResponseFactory.success(dispatchRuleService.detail(id));
    }

    @ApiOperation(value = "暂停")
    @GetMapping("/pause")
    @RequestLogger(value = "配送规则暂停")
    public Response<Object> pause(@RequestParam Long id) {
        dispatchRuleService.pause(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    @RequestLogger(value = "配送规则删除")
    public Response<Object> delete(@RequestParam Long id) {
        dispatchRuleService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "激活")
    @GetMapping("/activate")
    @RequestLogger(value = "配送规则激活")
    public Response<DispatchPlanActVO> activate(@RequestParam Long id) {
        return ResponseFactory.success(dispatchRuleService.activate(id));
    }

    @ApiOperation(value = "列表")
    @PostMapping("/search")
    public Response<PageVO<DispatchRuleListVO>> search(@RequestBody DispatchRuleSearchDTO searchDTO) {
        return ResponseFactory.success(dispatchRuleService.search(searchDTO));
    }

    @ApiOperation(value = "获取剩余配送提数-为了获取提数下拉框最大提数")
    @PostMapping("/getDispatchAmount")
    public Response<Integer> getDispatchAmount(@RequestBody DispatchAmountDTO dto) {
        return ResponseFactory.success(dispatchRuleService.getDispatchAmount(dto));
    }

    @ApiOperation(value = "获取奶卡剩余提数")
    @GetMapping("/getCardLeftDispatchAmount")
    public Response<Integer> getCardLeftDispatchAmount(@RequestParam String cardNumber,
                                                       @RequestParam(required = false) Long skuId,
                                                       @RequestParam(required = false) Long ruleId) {
        return ResponseFactory.success(dispatchRuleService.getCardLeftDispatchAmount(cardNumber, skuId, ruleId));
    }



    @ApiOperation(value = "商品剩余提奶数量")
    @GetMapping("/purchaseLimit")
    public Response<Integer> purchaseLimit(@RequestParam Long skuId,
                                                         @RequestParam(required = false) Long ruleId,
                                                         @RequestParam(required = false) String cardNumber) {
        return ResponseFactory.success(dispatchRuleService.purchaseLimit(skuId,ruleId,cardNumber));
    }

    @ApiOperation(value = "校验地区能否配送商品")
    @GetMapping("/checkDelivery")
    public Response<Boolean> checkDelivery(@RequestParam Long productId,
                                           @RequestParam String provinceCode,
                                           @RequestParam String cityCode,
                                           @RequestParam String districtCode) {
        return ResponseFactory.success(dispatchRuleService.checkCanDelivery(productId,provinceCode,cityCode,districtCode));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/create")
    @RequestLogger(value = "配送规则新增")
    public Response<Object> create(@RequestBody DispatchRuleSaveDTO param) {
        dispatchRuleService.create(param);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "获取低温奶发货日期")
    @GetMapping("/getLowDispatchDay")
    public Response<List<String>> getLowMilkDispatchDay(@RequestParam Integer times, @RequestParam(required = false) Long productId) {
        return ResponseFactory.success(dispatchRuleService.getLowMilkDispatchDay(times, productId));
    }

    @ApiOperation(value = "用户备注快递")
    @GetMapping("/userRemarkExpress")
    public Response<List<String>> userRemarkExpress() {
        return ResponseFactory.success(dispatchRuleService.userRemarkExpress());
    }

    @ApiOperation(value = "批量修改备注")
    @PostMapping("/batchUpdateRemark")
    @RequestLogger(value = "批量修改备注")
    public Response<Integer> batchUpdateRemark(@RequestBody List<DispatchRuleUpdateDTO> dtos) {
        return ResponseFactory.success(dispatchRuleService.batchUpdateRemark(dtos));
    }

}
