package com.hengtiansoft.operation.dispatch.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: <EMAIL>
 */
@Data
public class DispatchRuleListVO {

    @ApiModelProperty("配送规则ID")
    private Long id;

    @ApiModelProperty("卡号")
    private String cardNumber;

    @ApiModelProperty("卡券名称")
    private String cardName;

    @ApiModelProperty("奶卡类别id")
    private Long categoryId;

    @ApiModelProperty("用户手机号")
    private String userPhone;

    @ApiModelProperty("是否奶卡白名单：0否1是")
    private Integer whiteList;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("商品id")
    private Long productId;

    @ApiModelProperty("商品规格描述")
    private String skuDesc;

    @ApiModelProperty("商品单位")
    private String unit;

    @ApiModelProperty("单次配送提数")
    private Integer milkAmount;

    @ApiModelProperty("配送次数")
    private Integer times;

    @ApiModelProperty("发货日期描述（每月X号/星期X）")
    private String dispatchDateDesc;

    @ApiModelProperty("配送模式 0-工作日送达 1-周末送达")
    private Integer dispatchMode;

    @ApiModelProperty("间隔时长")
    private Integer intervalTime;

    @ApiModelProperty("规则状态（1：服务中，2：已结束，3：暂停中，4：服务完成）")
    private Integer ruleStatus;

    @ApiModelProperty("类型 0- 普通提货 1-普通特权提货 2-低温奶提货  3- 低温奶特权提货")
    private Integer ruleType;

    @ApiModelProperty("规则状态描述")
    private String ruleStatusDesc;

    @ApiModelProperty("收货人名称")
    private String receiverName;

    @ApiModelProperty("收货人电话")
    private String receiverPhone;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("区")
    private String district;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("主体 1-奶卡 2-周期购")
    private Integer subject;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("用户备注")
    private String userRemark;
}
