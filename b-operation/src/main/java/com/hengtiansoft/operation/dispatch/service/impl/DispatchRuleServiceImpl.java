package com.hengtiansoft.operation.dispatch.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.content.dao.BaseDataDao;
import com.hengtiansoft.content.entity.po.BaseData;
import com.hengtiansoft.content.enums.BaseDataTypeEnum;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.enumeration.CardTypeEnum;
import com.hengtiansoft.item.interfaces.DeliveryAddressTemplateManager;
import com.hengtiansoft.item.manager.CardManager;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchRuleDetailVO;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchRuleListVO;
import com.hengtiansoft.operation.dispatch.service.DispatchRuleService;
import com.hengtiansoft.operation.dispatch.service.DispatchService;
import com.hengtiansoft.order.dao.OrderSkuDao;
import com.hengtiansoft.order.entity.dto.*;
import com.hengtiansoft.order.entity.mapper.OrderSkuCardMapper;
import com.hengtiansoft.order.entity.po.MilkDispatchPlan;
import com.hengtiansoft.order.entity.po.MilkDispatchRule;
import com.hengtiansoft.order.entity.po.OrderSku;
import com.hengtiansoft.order.entity.po.OrderSkuCard;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanActVO;
import com.hengtiansoft.order.enums.PlanSubjectEnum;
import com.hengtiansoft.order.enums.PushFlagEnum;
import com.hengtiansoft.order.enums.RuleStatusEnum;
import com.hengtiansoft.order.manager.MilkDispatchPlanManager;
import com.hengtiansoft.order.manager.MilkDispatchRuleManager;
import com.hengtiansoft.order.util.MilkDispatchUtil;
import com.hengtiansoft.order.util.OrderSkuUtil;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.manager.CustomerUserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 */
@Service
public class DispatchRuleServiceImpl implements DispatchRuleService {

    @Autowired
    private MilkDispatchRuleManager milkDispatchRuleManager;
    @Autowired
    private MilkDispatchPlanManager milkDispatchPlanManager;
    @Autowired
    private CardManager cardManager;
    @Autowired
    private OrderSkuDao orderSkuDao;
    @Autowired
    private CardDao cardDao;
    @Autowired
    private DeliveryAddressTemplateManager deliveryAddressTemplateManager;
    @Resource
    private OrderSkuCardMapper orderSkuCardManager;
    @Resource
    private BaseDataDao baseDataDao;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private DispatchService dispatchService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(DispatchRuleSaveDTO param){
        param.setCardNumber(param.getCardNumber().toUpperCase());
        param.setId(null);
        // 激活奶卡
        Card card = cardManager.activateAndCheckCard(param.getCardNumber());
/*        if(CardTypeEnum.CYCLE_CARD.getCode().equals(card.getCardType())){
            throw new BusinessException("周期购不允许后台新增规则！");
        }*/
        //权限校验和地址
        verifyRuleRole(param, null, card);

        milkDispatchRuleManager.checkCycleAfterSale(card.getCardNumber(), null);

        //确认新增时再次校验地区能否配送
        if(!checkCanDelivery(param.getProductId(),param.getProvinceCode(),param.getCityCode(),param.getDistrictCode())){
            throw new BusinessException("当前地区不可配送，请重新选择");
        }
        // 新建规则
        if(CardTypeEnum.CYCLE_CARD.getCode().equals(card.getCardType())){
            param.setSubject(PlanSubjectEnum.CYCLE.getCode());
        }else{
            param.setSubject(PlanSubjectEnum.CARD.getCode());
        }

        milkDispatchRuleManager.create(param, true);
    }


    private void verifyRuleRole(DispatchRuleSaveDTO param, MilkDispatchRule oldRule, Card card) {
        MilkDispatchDTO newAddress = MilkDispatchUtil.buildAddress(param);
        MilkDispatchDTO oldAddress = null == oldRule ? null : MilkDispatchUtil.buildAddress(oldRule);
        if(null != oldRule){
            String newAddressStr = MilkDispatchUtil.buildAddressString(newAddress);
            String oldAddressStr = MilkDispatchUtil.buildAddressString(oldAddress);
            if(newAddressStr.equals(oldAddressStr)){
                return;
            }
        }
        dispatchService.verifyRole(newAddress, oldAddress, card,  BaseDataTypeEnum.RULE_ROLE.getCode());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DispatchRuleSaveDTO param){
        // 后台默认为每月配送
        MilkDispatchRule milkDispatchRule = milkDispatchRuleManager.detail(param.getId());
        param.setUserRemark(milkDispatchRule.getUserRemark());

        Card card = cardManager.selectByNumber(milkDispatchRule.getCardNumber());
        //权限校验和地址
        verifyRuleRole(param, milkDispatchRule, card);
        milkDispatchRuleManager.checkCycleAfterSale(milkDispatchRule.getCardNumber(), null);

/*        if(DispatchRuleTypeEnum.LOW_PRIVILEGE.getCode().equals(milkDispatchRule.getRuleType())
           || DispatchRuleTypeEnum.NORMAL_PRIVILEGE.getCode().equals(milkDispatchRule.getRuleType())){
            if(!param.getSkuId().equals(milkDispatchRule.getSkuId()) || !param.getProductId().equals(milkDispatchRule.getProductId())){
                throw new BusinessException("特权商品不允许变更商品信息!");
            }
        }*/

        //确认编辑时再次校验地区能否配送
        if(!checkCanDelivery(param.getProductId(),param.getProvinceCode(),param.getCityCode(),param.getDistrictCode())){
            throw new BusinessException("当前地区不可配送，请重新选择");
        }
        // 删除现有计划
        milkDispatchPlanManager.deleteFuturePlan(param.getId(),param.getSkuId(),milkDispatchRule.getCardNumber());

        // 更新规则
        param.setSubject(milkDispatchRule.getSubject());
        milkDispatchRuleManager.update(param, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pause(Long id){
        // 暂停规则
        milkDispatchRuleManager.pause(id);
        // 暂停计划
        milkDispatchPlanManager.pause(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id){
        MilkDispatchRule rule = milkDispatchRuleManager.detail(id);
        if(null == rule){
            throw new BusinessException("规则不存在!");
        }
        milkDispatchRuleManager.checkCycleAfterSale(rule.getCardNumber(), null);

        List<MilkDispatchPlan> planList = milkDispatchPlanManager.findByRuleId(id);
        List<MilkDispatchPlan> pushPlans = StreamUtils.filter(planList,x-> PushFlagEnum.COMPLETED.getCode().equals(x.getPushFlag()));
        if(CollectionUtils.isNotEmpty(pushPlans)){
            throw new BusinessException("该规则下存在已推送的计划，不允许删除规则！");
        }
        // 删除计划
        milkDispatchPlanManager.delete(id,rule.getSkuId());
        // 删除规则
        milkDispatchRuleManager.delete(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DispatchPlanActVO activate(Long id){
        // 激活规则
        milkDispatchRuleManager.activate(id);
        // 激活计划
        return milkDispatchPlanManager.activate(id);
    }

    @Override
    public DispatchRuleDetailVO detail(Long id){
        MilkDispatchRule rule = milkDispatchRuleManager.detail(id);
        if(null == rule){
            throw new BusinessException("规则不存在!");
        }
        DispatchRuleDetailVO vo = new DispatchRuleDetailVO();
        BeanUtils.copy(rule, vo);

        Card card = cardManager.selectByNumber(rule.getCardNumber());
        vo.setCategoryId(card.getCategoryId());

        vo.setDispatchDateDesc(milkDispatchRuleManager.switchDispatchDateDesc(rule.getDispatchType(), rule.getDispatchDate(), rule.getIntervalTime()));
        vo.setRuleStatusDesc(RuleStatusEnum.getDescByCode(rule.getRuleStatus()));
        return vo;
    }

    @Override
    public PageVO<DispatchRuleListVO> search(DispatchRuleSearchDTO searchDTO){
        if(CollectionUtils.isNotEmpty(searchDTO.getCardNumberList())){
            searchDTO.setCardNumberList(searchDTO.getCardNumberList());
        }
        if(StringUtils.isNotBlank(searchDTO.getOrderNo())){
            List<OrderSkuCard> orderSkuCardList = orderSkuCardManager.findByOrderNoOrSrcNo(searchDTO.getOrderNo());
            if(CollectionUtils.isNotEmpty(orderSkuCardList)) {
                List<String> cardNumberList = orderSkuCardList.stream().filter(skuCard -> StringUtils.isNotBlank(skuCard.getCardNumber())).map(OrderSkuCard::getCardNumber).collect(Collectors.toList());
                // 集合交集
                if(CollectionUtils.isNotEmpty(searchDTO.getCardNumberList())) {
                    searchDTO.setCardNumberList(searchDTO.getCardNumberList().stream().filter(cardNumberList::contains).collect(Collectors.toList()));
                }else{
                    searchDTO.setCardNumberList(cardNumberList);
                }
            }
        }
        // 查询分页的规则列表
        List<MilkDispatchRule> list = milkDispatchRuleManager.search(searchDTO);

        if (CollectionUtils.isEmpty(list)) {
            Pagination pagination = new Pagination(searchDTO.getPageNum(), searchDTO.getPageSize(), 0, 1);
            PageVO<DispatchRuleListVO> pageVO = new PageVO<>();
            pageVO.setPagination(pagination);
            pageVO.setList(new LinkedList<>());
            return pageVO;
        }

        List<String> cardNumbers = StreamUtils.toList(list, MilkDispatchRule::getCardNumber);
        List<Card> cards = cardManager.selectByNumberList(cardNumbers);
        Map<String, Card> cardMap = StreamUtils.toMap(cards, Card::getCardNumber);

        List<Long> userIds = StreamUtils.toList(cards, Card::getUserId);
        List<CustomerUser> userList = customerUserManager.findByIdList(userIds);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(userList, CustomerUser::getId);

        // 查询订单
        List<OrderSku> orderSkuList = orderSkuDao.findByCardCodeV2(cardNumbers);
        Map<String, OrderSku> orderSkuMap = OrderSkuUtil.skusToMap(orderSkuList, true);

        return PageUtils.convert(list, rule -> {
            DispatchRuleListVO vo = new DispatchRuleListVO();
            BeanUtils.copy(rule, vo);
            Card card = cardMap.get(rule.getCardNumber());
            if (null != card) {
                vo.setCategoryId(card.getCategoryId());
                vo.setCardName(card.getCategoryName());
                vo.setUserPhone(card.getUserPhone());
                CustomerUser user = userMap.get(card.getUserId());
                vo.setWhiteList(user != null ? user.getWhiteList() : null);
                OrderSku orderSku = orderSkuMap.get(rule.getCardNumber().toUpperCase());
                if (Objects.nonNull(orderSku)) {
                    vo.setCardName(orderSku.getProductName());
                    vo.setOrderNo(orderSku.getOrderNo());
                }
            }

            vo.setDispatchDateDesc(milkDispatchRuleManager.switchDispatchDateDesc(rule.getDispatchType(), rule.getDispatchDate(), vo.getIntervalTime()));
            vo.setRuleStatusDesc(RuleStatusEnum.getDescByCode(rule.getRuleStatus()));
            return vo;
        });
    }

    @Override
    public Integer getDispatchAmount(DispatchAmountDTO dto){
        return milkDispatchRuleManager.getDispatchAmount(dto);
    }

    @Override
    public Integer getCardLeftDispatchAmount(String cardNumber, Long skuId, Long ruleId) {
        return milkDispatchRuleManager.getCardLeftDispatchAmount(cardNumber, skuId, ruleId);
    }

    @Override
    public Boolean checkCanDelivery(Long productId, String provinceCode, String cityCode, String districtCode) {
        return deliveryAddressTemplateManager.checkCanDelivery(productId,provinceCode,cityCode,districtCode);
    }

    @Override
    public Integer purchaseLimit(Long skuId, Long ruleId, String cardNumber) {
        Long userId = 0L;
        if(Objects.nonNull(ruleId)){
            MilkDispatchRule rule = milkDispatchRuleManager.detail(ruleId);
            if(Objects.isNull(rule)){
                throw new BusinessException("规则不存在!");
            }
            userId = rule.getUserId();
        }else{
            Card card = cardManager.selectByNumber(cardNumber);
            if(Objects.isNull(card)){
                throw new BusinessException("奶卡不存在!");
            }
            userId = card.getUserId();
        }
        return milkDispatchRuleManager.purchaseLimit(skuId,userId,ruleId,cardNumber);
    }

    @Override
    public List<String> getLowMilkDispatchDay(Integer times, Long productId) {
        return milkDispatchRuleManager.getLowMilkDispatchDay2Str(times, productId, true);
    }

    @Override
    public List<String> userRemarkExpress() {
        BaseData baseData = baseDataDao.selectByType(BaseDataTypeEnum.USER_REMARK_EXPRESS.getCode());
        List<String> express = JSON.parseArray(baseData.getValue(), String.class);
        return express;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateRemark(List<DispatchRuleUpdateDTO> dto) {
        milkDispatchPlanManager.batchUpdateRemark(dto);
        return milkDispatchRuleManager.batchUpdateRemark(dto);
    }
}
