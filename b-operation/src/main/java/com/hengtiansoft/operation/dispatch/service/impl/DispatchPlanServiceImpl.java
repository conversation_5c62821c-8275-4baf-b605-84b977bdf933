package com.hengtiansoft.operation.dispatch.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.constant.CardConstant;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.content.dao.BaseDataDao;
import com.hengtiansoft.content.entity.po.BaseData;
import com.hengtiansoft.content.enums.BaseDataTypeEnum;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.dao.ItemSkuDataItemDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.StockCalculateDTO;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.po.ItemSkuDataItem;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.operation.dispatch.dto.PlanDataIsoDTO;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchPlanListVO;
import com.hengtiansoft.operation.dispatch.entity.vo.PlanPackageVO;
import com.hengtiansoft.operation.dispatch.service.DispatchPlanService;
import com.hengtiansoft.operation.dispatch.service.DispatchService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.dao.MilkDispatchPlanDao;
import com.hengtiansoft.order.dao.OrderSkuDao;
import com.hengtiansoft.order.entity.dto.DispatchPlanSaveDTO;
import com.hengtiansoft.order.entity.dto.DispatchPlanSearchDTO;
import com.hengtiansoft.order.entity.dto.DispatchPushDTO;
import com.hengtiansoft.order.entity.dto.MilkDispatchDTO;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.entity.vo.ExpressInfoVO;
import com.hengtiansoft.order.entity.vo.PlanedMilkStatisticalTableVO;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanActVO;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanListOrderExcelVO;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.enums.PlanStatusEnum;
import com.hengtiansoft.order.enums.PushFlagEnum;
import com.hengtiansoft.order.manager.MilkDispatchPlanManager;
import com.hengtiansoft.order.manager.MilkDispatchRuleManager;
import com.hengtiansoft.order.manager.OrderSkuCardManager;
import com.hengtiansoft.order.manager.impl.OrderWangdianManagerImpl;
import com.hengtiansoft.order.util.MilkDispatchUtil;
import com.hengtiansoft.order.util.OrderSkuUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.manager.CustomerUserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 */
@Slf4j
@Service
public class DispatchPlanServiceImpl implements DispatchPlanService {


    @Autowired
    private MilkDispatchPlanManager milkDispatchPlanManager;
    @Autowired
    private OrderWangdianManagerImpl orderWangdianManagerImpl;
    @Autowired
    private CardDao cardDao;
    @Autowired
    private OrderSkuDao orderSkuDao;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private MilkDispatchPlanDao milkDispatchPlanDao;
    @Autowired
    private MilkDispatchRuleManager milkDispatchRuleManager;
    @Autowired
    private StockManager stockManager;
    @Autowired
    private OrderSyncAdapter orderSyncAdapter;
    @Resource
    private BaseDataDao baseDataDao;
    @Resource
    private OrderSkuCardManager orderSkuCardManager;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private DispatchService dispatchService;
    @Resource
    private ProductDao productDao;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private Executor labelTask;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Autowired
    private ItemSkuDataItemDao itemSkuDataItemDao;

    @Override
    public PageVO<DispatchPlanListVO> search(DispatchPlanSearchDTO searchDTO) {

        //特殊用户根据地区数据隔离
        userDataIso(searchDTO);

        if(StringUtils.isNotBlank(searchDTO.getOrderNoOrSrcNo())){
//            List<OrderSku> orderSkuList = orderSkuDao.selectOrderByOrderNoOrSrcNo(searchDTO.getOrderNoOrSrcNo());
            // 根据orderNo获取List<orderSkuCard>
            // List<OrderSku> orderSkuList = orderSkuDao.selectOrderByOrderNoOrSrcNo(searchDTO.getOrderNoOrSrcNo());
            List<OrderSkuCard> orderSkuCardList = orderSkuCardManager.findByOrderNoOrSrcNo(searchDTO.getOrderNoOrSrcNo());
            if(CollectionUtils.isNotEmpty(orderSkuCardList)) {
                List<String> cardNumberList = orderSkuCardList.stream().filter(skuCard -> StringUtils.isNotBlank(skuCard.getCardNumber())).map(OrderSkuCard::getCardNumber).collect(Collectors.toList());
                searchDTO.setCardNumberList(cardNumberList);
            } else {
                Pagination pagination = new Pagination(searchDTO.getPageNum(), searchDTO.getPageSize(), 0, 1);
                PageVO<DispatchPlanListVO> pageVO = new PageVO<>();
                pageVO.setPagination(pagination);
                pageVO.setList(new LinkedList<>());
                return pageVO;
            }
        }

        // 查询分页的规则列表
        List<MilkDispatchPlan> list = milkDispatchPlanManager.search(searchDTO);
        if (CollectionUtils.isEmpty(list)) {
            Pagination pagination = new Pagination(searchDTO.getPageNum(), searchDTO.getPageSize(), 0, 1);
            PageVO<DispatchPlanListVO> pageVO = new PageVO<>();
            pageVO.setPagination(pagination);
            pageVO.setList(new LinkedList<>());
            return pageVO;
        }
        List<Long> productIds = StreamUtils.convertDistinct(list, MilkDispatchPlan::getProductId, Long::compareTo);
        Map<Long, Product> productMap = StreamUtils.toMap(productDao.findByIds(productIds), Product::getId);

        List<String> cardNumberList = StreamUtils.toList(list, MilkDispatchPlan::getCardNumber);
        List<Long> ruleIdList = StreamUtils.toList(list, MilkDispatchPlan::getRuleId);
        List<Card> cards = cardDao.selectByNumberList(cardNumberList);
        Map<String, Card> cardMap = StreamUtils.toMap(cards, card -> card.getCardNumber().toUpperCase());
        Map<Long, MilkDispatchRule> ruleMap = StreamUtils.toMap(milkDispatchRuleManager.findByIds(ruleIdList), rule -> rule.getId());

        List<Long> userIds = StreamUtils.toList(cards, Card::getUserId);
        List<CustomerUser> userList = customerUserManager.findByIdList(userIds);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(userList, CustomerUser::getId);
        // 查询订单
        // List<OrderSku> orderSkuList = orderSkuDao.findByCardCode(cardNumberList);
        // 根据List<CardString> 获取 List<orderSku>
        List<OrderSku> orderSkuList = orderSkuDao.findByCardCodeV2(cardNumberList);
        Map<String, OrderSku> orderSkuMap = OrderSkuUtil.skusToMap(orderSkuList, true);
        return PageUtils.convert(list, plan -> {
            DispatchPlanListVO vo = new DispatchPlanListVO();
            BeanUtils.copy(plan, vo);
            // 物流
            vo.setPackageList(new LinkedList<>());
            if (StringUtils.isNotBlank(plan.getLogistics())) {
                List<Map<String, String>> logisticsList = JSON.parseObject(plan.getLogistics(), List.class);
                for (Map<String, String> map : logisticsList) {
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        PlanPackageVO packageVO = new PlanPackageVO(entry.getValue(), entry.getKey());
                        vo.getPackageList().add(packageVO);
                    }
                }
            }

            vo.setDispatchDate(DateUtil.dateToString(plan.getDispatchDate(), DateUtil.SIMPLE_YMD));
            vo.setPlanStatusDesc(PlanStatusEnum.getDescByCode(plan.getPlanStatus()));
            vo.setPushFlagDesc(PushFlagEnum.getDescByCode(plan.getPushFlag()));
            // 卡类别名称
            vo.setCardName(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? "" : cardMap.get(plan.getCardNumber().toUpperCase()).getCategoryName());

            OrderSku orderSku = orderSkuMap.get(plan.getCardNumber().toUpperCase());
            if (Objects.nonNull(orderSku)) {
                vo.setCardName(orderSku.getProductName());
                vo.setOrderNo(orderSku.getOrderNo());
            }
            Card card = cardMap.get(plan.getCardNumber().toUpperCase());
            if(null != card){
                CustomerUser user = userMap.get(card.getUserId());
                vo.setWhiteList(user != null ? user.getWhiteList() : null);
            }
            Product product = productMap.get(plan.getProductId());
            if(null != product){
                vo.setTemperature(product.getTemperature());
            }
            vo.setUserPhone(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? "" :cardMap.get(plan.getCardNumber().toUpperCase()).getUserPhone());
            vo.setCategoryId(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? null :cardMap.get(plan.getCardNumber().toUpperCase()).getCategoryId());
            vo.setRuleType(Objects.isNull(ruleMap.get(plan.getRuleId())) ? 0 : ruleMap.get(plan.getRuleId()).getRuleType());
            vo.setDispatchMode(Objects.isNull(ruleMap.get(plan.getRuleId())) ? null : ruleMap.get(plan.getRuleId()).getDispatchMode());
            return vo;
        });
    }

    @Override
    public PageVO<DispatchPlanListVO> searchForExport(DispatchPlanSearchDTO searchDTO, UserDetailDTO detailDTO) {
        //特殊用户根据地区数据隔离
        userDataIso(searchDTO, detailDTO);

        if(StringUtils.isNotBlank(searchDTO.getOrderNoOrSrcNo())){
//            List<OrderSku> orderSkuList = orderSkuDao.selectOrderByOrderNoOrSrcNo(searchDTO.getOrderNoOrSrcNo());
            // 根据orderNo获取List<orderSkuCard>
            // List<OrderSku> orderSkuList = orderSkuDao.selectOrderByOrderNoOrSrcNo(searchDTO.getOrderNoOrSrcNo());
            List<OrderSkuCard> orderSkuCardList = orderSkuCardManager.findByOrderNoOrSrcNo(searchDTO.getOrderNoOrSrcNo());
            if(CollectionUtils.isNotEmpty(orderSkuCardList)) {
                List<String> cardNumberList = orderSkuCardList.stream().filter(skuCard -> StringUtils.isNotBlank(skuCard.getCardNumber())).map(OrderSkuCard::getCardNumber).collect(Collectors.toList());
                searchDTO.setCardNumberList(cardNumberList);
            } else {
                Pagination pagination = new Pagination(searchDTO.getPageNum(), searchDTO.getPageSize(), 0, 1);
                PageVO<DispatchPlanListVO> pageVO = new PageVO<>();
                pageVO.setPagination(pagination);
                pageVO.setList(new LinkedList<>());
                return pageVO;
            }
        }

        // 查询分页的规则列表
        List<MilkDispatchPlan> list = milkDispatchPlanManager.search(searchDTO);
        Pagination currentPageInfo = PageUtils.extract(list);
        if (CollectionUtils.isEmpty(list)) {
            Pagination pagination = new Pagination(searchDTO.getPageNum(), searchDTO.getPageSize(), 0, 1);
            PageVO<DispatchPlanListVO> pageVO = new PageVO<>();
            pageVO.setPagination(pagination);
            pageVO.setList(new LinkedList<>());
            return pageVO;
        }

        List<String> skuCodes = list.stream().map(MilkDispatchPlan::getSkuCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<ItemSkuDataItem> skuDataItems = itemSkuDataItemDao.selectBySkuCodeList(skuCodes);
        Map<String, List<ItemSkuDataItem>> skuDataItemGroupMap = skuDataItems.stream().collect(Collectors.groupingBy(ItemSkuDataItem::getSkuCode));

        List<Long> productIds = StreamUtils.convertDistinct(list, MilkDispatchPlan::getProductId, Long::compareTo);
        Map<Long, Product> productMap = StreamUtils.toMap(productDao.findByIds(productIds), Product::getId);

        List<String> cardNumberList = StreamUtils.toList(list, MilkDispatchPlan::getCardNumber);
        List<Long> ruleIdList = StreamUtils.toList(list, MilkDispatchPlan::getRuleId);
        List<Card> cards = cardDao.selectByNumberList(cardNumberList);
        Map<String, Card> cardMap = StreamUtils.toMap(cards, card -> card.getCardNumber().toUpperCase());
        Map<Long, MilkDispatchRule> ruleMap = StreamUtils.toMap(milkDispatchRuleManager.findByIds(ruleIdList), MilkDispatchRule::getId);

        List<Long> userIds = StreamUtils.toList(cards, Card::getUserId);
        List<CustomerUser> userList = customerUserManager.findByIdList(userIds);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(userList, CustomerUser::getId);
        // 查询订单
        // List<OrderSku> orderSkuList = orderSkuDao.findByCardCode(cardNumberList);
        // 根据List<CardString> 获取 List<orderSku>
        List<OrderSku> orderSkuList = orderSkuDao.findByCardCodeV2(cardNumberList);
        Map<String, OrderSku> orderSkuMap = OrderSkuUtil.skusToMap(orderSkuList, true);
        List<DispatchPlanListVO> resultList = Lists.newArrayList();
        for (MilkDispatchPlan plan : list) {
            String skuCode = plan.getSkuCode();
            List<ItemSkuDataItem> currentSkuDataItems = skuDataItemGroupMap.get(skuCode);
            if (CollectionUtils.isNotEmpty(currentSkuDataItems)) {
                for (int i = 0; i < currentSkuDataItems.size(); i++) {
                    ItemSkuDataItem skuDataItem = currentSkuDataItems.get(i);
                    DispatchPlanListVO vo = new DispatchPlanListVO();
                    BeanUtils.copy(plan, vo);
                    if(i > 0){
                        vo.setId(null);
                    }
                    vo.setSkuEcode(skuDataItem.getSkuEcode());
                    vo.setNumber(skuDataItem.getNumber());
                    // 物流
                    vo.setPackageList(new LinkedList<>());
                    if (StringUtils.isNotBlank(plan.getLogistics())) {
                        List<Map<String, String>> logisticsList = JSON.parseObject(plan.getLogistics(), List.class);
                        for (Map<String, String> map : logisticsList) {
                            for (Map.Entry<String, String> entry : map.entrySet()) {
                                PlanPackageVO packageVO = new PlanPackageVO(entry.getValue(), entry.getKey());
                                vo.getPackageList().add(packageVO);
                            }
                        }
                    }

                    vo.setDispatchDate(DateUtil.dateToString(plan.getDispatchDate(), DateUtil.SIMPLE_YMD));
                    vo.setPlanStatusDesc(PlanStatusEnum.getDescByCode(plan.getPlanStatus()));
                    vo.setPushFlagDesc(PushFlagEnum.getDescByCode(plan.getPushFlag()));
                    // 卡类别名称
                    vo.setCardName(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? "" : cardMap.get(plan.getCardNumber().toUpperCase()).getCategoryName());

                    OrderSku orderSku = orderSkuMap.get(plan.getCardNumber().toUpperCase());
                    if (Objects.nonNull(orderSku)) {
                        vo.setCardName(orderSku.getProductName());
                        vo.setOrderNo(orderSku.getOrderNo());
                    }
                    Card card = cardMap.get(plan.getCardNumber().toUpperCase());
                    if(null != card){
                        CustomerUser user = userMap.get(card.getUserId());
                        vo.setWhiteList(user != null ? user.getWhiteList() : null);
                    }
                    Product product = productMap.get(plan.getProductId());
                    if(null != product){
                        vo.setTemperature(product.getTemperature());
                    }
                    vo.setUserPhone(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? "" :cardMap.get(plan.getCardNumber().toUpperCase()).getUserPhone());
                    vo.setCategoryId(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? null :cardMap.get(plan.getCardNumber().toUpperCase()).getCategoryId());
                    vo.setRuleType(Objects.isNull(ruleMap.get(plan.getRuleId())) ? 0 : ruleMap.get(plan.getRuleId()).getRuleType());
                    vo.setDispatchMode(Objects.isNull(ruleMap.get(plan.getRuleId())) ? null : ruleMap.get(plan.getRuleId()).getDispatchMode());
                    resultList.add(vo);
                }
            } else {
                DispatchPlanListVO vo = new DispatchPlanListVO();
                BeanUtils.copy(plan, vo);
                // 物流
                vo.setPackageList(new LinkedList<>());
                if (StringUtils.isNotBlank(plan.getLogistics())) {
                    List<Map<String, String>> logisticsList = JSON.parseObject(plan.getLogistics(), List.class);
                    for (Map<String, String> map : logisticsList) {
                        for (Map.Entry<String, String> entry : map.entrySet()) {
                            PlanPackageVO packageVO = new PlanPackageVO(entry.getValue(), entry.getKey());
                            vo.getPackageList().add(packageVO);
                        }
                    }
                }

                vo.setDispatchDate(DateUtil.dateToString(plan.getDispatchDate(), DateUtil.SIMPLE_YMD));
                vo.setPlanStatusDesc(PlanStatusEnum.getDescByCode(plan.getPlanStatus()));
                vo.setPushFlagDesc(PushFlagEnum.getDescByCode(plan.getPushFlag()));
                // 卡类别名称
                vo.setCardName(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? "" : cardMap.get(plan.getCardNumber().toUpperCase()).getCategoryName());

                OrderSku orderSku = orderSkuMap.get(plan.getCardNumber().toUpperCase());
                if (Objects.nonNull(orderSku)) {
                    vo.setCardName(orderSku.getProductName());
                    vo.setOrderNo(orderSku.getOrderNo());
                }
                Card card = cardMap.get(plan.getCardNumber().toUpperCase());
                if(null != card){
                    CustomerUser user = userMap.get(card.getUserId());
                    vo.setWhiteList(user != null ? user.getWhiteList() : null);
                }
                Product product = productMap.get(plan.getProductId());
                if(null != product){
                    vo.setTemperature(product.getTemperature());
                }
                vo.setUserPhone(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? "" :cardMap.get(plan.getCardNumber().toUpperCase()).getUserPhone());
                vo.setCategoryId(Objects.isNull(cardMap.get(plan.getCardNumber().toUpperCase())) ? null :cardMap.get(plan.getCardNumber().toUpperCase()).getCategoryId());
                vo.setRuleType(Objects.isNull(ruleMap.get(plan.getRuleId())) ? 0 : ruleMap.get(plan.getRuleId()).getRuleType());
                vo.setDispatchMode(Objects.isNull(ruleMap.get(plan.getRuleId())) ? null : ruleMap.get(plan.getRuleId()).getDispatchMode());
                resultList.add(vo);
            }
        }
        return PageUtils.toPageVO(currentPageInfo, resultList);
    }

    private void userDataIso(DispatchPlanSearchDTO searchDTO) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        BaseData baseData = baseDataDao.selectByType(BaseDataTypeEnum.PLAN_DATA_ISO.getCode());
        List<PlanDataIsoDTO> planDataIsoDTOList = JSON.parseArray(baseData.getValue(), PlanDataIsoDTO.class);
        if(CollectionUtils.isNotEmpty(planDataIsoDTOList)){
            Map<Long, PlanDataIsoDTO> dataIsoDTOMap = StreamUtils.toMap(planDataIsoDTOList, x -> x.getUserId());
            PlanDataIsoDTO planDataIsoDTO = dataIsoDTOMap.get(detailDTO.getUserId());
            if(Objects.nonNull(planDataIsoDTO)){
                if(StringUtils.isNotBlank(planDataIsoDTO.getProvince())){
                    searchDTO.setProvince(Arrays.asList(planDataIsoDTO.getProvince().split(",")));
                }
                if(StringUtils.isNotBlank(planDataIsoDTO.getCity())){
                    searchDTO.setCity(Arrays.asList(planDataIsoDTO.getCity().split(",")));

                }
                if(StringUtils.isNotBlank(planDataIsoDTO.getDistrict())){
                    searchDTO.setDistrict(Arrays.asList(planDataIsoDTO.getDistrict().split(",")));
                }
            }
        }
    }

    private void userDataIso(DispatchPlanSearchDTO searchDTO, UserDetailDTO detailDTO) {
        BaseData baseData = baseDataDao.selectByType(BaseDataTypeEnum.PLAN_DATA_ISO.getCode());
        List<PlanDataIsoDTO> planDataIsoDTOList = JSON.parseArray(baseData.getValue(), PlanDataIsoDTO.class);
        if(CollectionUtils.isNotEmpty(planDataIsoDTOList)){
            Map<Long, PlanDataIsoDTO> dataIsoDTOMap = StreamUtils.toMap(planDataIsoDTOList, x -> x.getUserId());
            PlanDataIsoDTO planDataIsoDTO = dataIsoDTOMap.get(detailDTO.getUserId());
            if(Objects.nonNull(planDataIsoDTO)){
                if(StringUtils.isNotBlank(planDataIsoDTO.getProvince())){
                    searchDTO.setProvince(Arrays.asList(planDataIsoDTO.getProvince().split(",")));
                }
                if(StringUtils.isNotBlank(planDataIsoDTO.getCity())){
                    searchDTO.setCity(Arrays.asList(planDataIsoDTO.getCity().split(",")));

                }
                if(StringUtils.isNotBlank(planDataIsoDTO.getDistrict())){
                    searchDTO.setDistrict(Arrays.asList(planDataIsoDTO.getDistrict().split(",")));
                }
            }
        }
    }

    @Override
    public ExpressInfoVO queryExpress(String expressCode, String orderNo, Long planId, String expressCompany) {
        return milkDispatchPlanManager.queryExpress(expressCode, orderNo, planId, expressCompany);
    }

    @Override
    public void pause(Long id) {
        milkDispatchPlanManager.pauseByPlanId(id);
    }

    @Override
    public DispatchPlanActVO activate(Long id) {
        return milkDispatchPlanManager.activateByPlanId(id);
    }

    @Override
    public void delete(Long id) {
        milkDispatchPlanManager.deleteByPlanId(id);
    }

    @Override
    public void update(DispatchPlanSaveDTO dto) {
        if(null == dto.getId()){
            throw new BusinessException("请选择计划!");
        }
        MilkDispatchPlan plan = milkDispatchPlanDao.findById(dto.getId());
        if(null == plan){
            throw new BusinessException("计划不存在!");
        }
        if(StringUtils.isBlank(plan.getCardNumber())){
            throw new BusinessException("卡号缺失!");
        }
        Card card = cardDao.selectByNumber(plan.getCardNumber());
        if(null == card){
            throw new BusinessException("奶卡不存在!");
        }
        //校验权限和地址
        verifyPlanRole(dto, plan, card);
        milkDispatchPlanManager.update(dto);
    }

    private void verifyPlanRole(DispatchPlanSaveDTO param, MilkDispatchPlan oldPlan, Card card) {
        MilkDispatchDTO newAddress = MilkDispatchUtil.buildAddress(param);
        MilkDispatchDTO oldAddress = null == oldPlan ? null : MilkDispatchUtil.buildAddress(oldPlan);
        if(null != oldPlan){
            String newAddressStr = MilkDispatchUtil.buildAddressString(newAddress);
            String oldAddressStr = MilkDispatchUtil.buildAddressString(oldAddress);
            if(newAddressStr.equals(oldAddressStr)){
                return;
            }
        }
        dispatchService.verifyRole(newAddress, oldAddress, card, BaseDataTypeEnum.PLAN_ROLE.getCode());
    }

    @Override
    public void updateAll(DispatchPlanSaveDTO dto) {
        milkDispatchPlanManager.updateAll(dto);
    }

    @Override
    public void push(Long id) {
        // 查询计划
        if(null == id){
            throw new BusinessException("请选择具体计划!");
        }
        MilkDispatchPlan plan = milkDispatchPlanManager.detail(id);
        if (!plan.getPushFlag().equals(PushFlagEnum.FAILED.getCode())) {
            throw new BusinessException("推送状态错误");
        }

/*        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 9);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        if (plan.getDispatchDate().getTime() >= calendar.getTimeInMillis()) {
            throw new BusinessException("超过最迟推送时间");
        }*/

        // 计算单价
        DispatchPushDTO pushDTO = new DispatchPushDTO();
        pushDTO.setPlan(plan);
//        BigDecimal price = plan.getAmount().divide(BigDecimal.valueOf(plan.getMilkAmount()/2), 2, BigDecimal.ROUND_HALF_UP);
//        pushDTO.setPrice(price);

        // 推送配送计划
        orderSyncAdapter.orderDispatchPlanPush(pushDTO);

    }



    @Override
    public PlanedMilkStatisticalTableVO dataStatistics() {
        Object redisObject = redisTemplate.opsForValue().get("plan_statistic_cache");
        if (Objects.isNull(redisObject)) {
            PlanedMilkStatisticalTableVO planedMilkStatisticalTableVO = milkDispatchPlanManager.dataStatistics();
            redisTemplate.opsForValue().set("plan_statistic_cache", planedMilkStatisticalTableVO, CardConstant.ONE, TimeUnit.DAYS);
            return planedMilkStatisticalTableVO;
        } else {
            return BeanUtils.deepCopy(redisObject, PlanedMilkStatisticalTableVO.class);
        }
    }

    @Override
    public List<Long> expirePlan() {
        List<Integer> planStatus = Arrays.asList(PlanStatusEnum.PAUSE.getCode());
        List<Integer> planNotExpireStatus = Arrays.asList(PlanStatusEnum.PLAN.getCode(),PlanStatusEnum.DISTRIBUTION.getCode(),
                PlanStatusEnum.SIGNED.getCode(),PlanStatusEnum.PAUSE.getCode(), PlanStatusEnum.DELIVER.getCode(), PlanStatusEnum.AFTER.getCode());
        List<MilkDispatchPlan> plans = milkDispatchPlanDao.findAllPlanByStatus(planStatus);
        Map<Long, Date> planIdDateMap = plans.stream().collect(Collectors.toMap(MilkDispatchPlan::getId, MilkDispatchPlan::getDispatchDate));
        Map<Long, MilkDispatchPlan> longMilkDispatchPlanMap = plans.stream().collect(Collectors.toMap(MilkDispatchPlan::getId, p -> p));
        List<Long> idList = new ArrayList<>();
        for (Map.Entry<Long, Date> entry : planIdDateMap.entrySet()) {
            if((new Date()).after(entry.getValue())){//过期
                milkDispatchPlanManager.expireByPlanId(entry.getKey());//过期状态更新
                //过期后更新商品库存
                MilkDispatchPlan plan = longMilkDispatchPlanMap.get(entry.getKey());
                StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
                stockCalculateDTO.setSkuId(plan.getSkuId());
                stockCalculateDTO.setStockNum(plan.getMilkAmount().longValue());
                if(!stockManager.increase(Collections.singletonList(stockCalculateDTO))){
                    throw new BusinessException("库存信息更新失败");
                }
                idList.add(entry.getKey());
                //当所有计划均变为过期状态，修改其规则状态为已完成
                List<MilkDispatchPlan> byRuleIdAndExpireStatus = milkDispatchPlanDao.findByRuleIdAndStatus(plan.getRuleId(), planNotExpireStatus);
                if(CollectionUtils.isEmpty(byRuleIdAndExpireStatus)){//规则下全都是过期的计划
                    milkDispatchRuleManager.finish(plan.getRuleId());
                }
            }
        }
        return idList;
    }

    @Override
    public void exportOrder(DispatchPlanSearchDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        Assert.notNull(dto.getExportType(), "导出类型不能为空");
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(dto.getExportType());
        if (null == enumByCode){
            throw new BusinessException("导出类型不存在");
        }
        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(dto.getExportType());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());
        dto.setFileName(enumByCode.getFileName() + date);
        dataExportTaskDao.saveOne(dataExportTask);
        // 异步执行
        labelTask.execute(() -> {
            try {
                export(dto, dataExportTask, detailDTO);
            } catch (Exception e) {
                log.error("导出异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }

    @Value("${local.tmp}")
    private String localDir;

    private void export(DispatchPlanSearchDTO dto, DataExportTask exportTask, UserDetailDTO detailDTO) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        File localTmpDirectory = new File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            List excelVOS = null;
//            List<Integer> mergeColumnRegion = Lists.newArrayList(0,1,2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19);

            File exportFile = new File(localTmpDirectory, dto.getFileName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), DispatchPlanListOrderExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();
            Integer pageNum = 1;
            Integer pageSize = 5000;
//            int lineCount = 1;
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            while(true){
                dto.setPageNum(pageNum);
                dto.setPageSize(pageSize);
                PageVO<DispatchPlanListVO> voPageVOS = this.searchForExport(dto, detailDTO);
                List<DispatchPlanListVO> vos = voPageVOS.getList();
                Integer pages = voPageVOS.getPages();
                excelVOS = this.convert2ExcelList(vos);
//                // 计算需要合并的区域
//                List<CellRangeAddress> mergeRegions = calculateMergeRegions(excelVOS, mergeColumnRegion, lineCount);
                excelWriter.write(excelVOS, writeSheet);
                pageNum ++;
                if(pageNum > pages){
                    break;
                }
            }
            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input,
                    fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }

    public List<DispatchPlanListOrderExcelVO> convert2ExcelList(List<DispatchPlanListVO> vos) {
        List<DispatchPlanListOrderExcelVO> excelVOS = new ArrayList<>();
        for (DispatchPlanListVO vo : vos) {
            DispatchPlanListOrderExcelVO excelVO = new DispatchPlanListOrderExcelVO();
            if(null != vo.getId()){
                excelVO.setId(vo.getId());
                excelVO.setCardNumber(vo.getCardNumber());
                excelVO.setCardName(vo.getCardName());
                excelVO.setPlanOrderNo(vo.getPlanOrderNo());
                excelVO.setRuleId(vo.getRuleId());
                excelVO.setUserPhone(vo.getUserPhone());
                excelVO.setDispatchDate(vo.getDispatchDate());
                excelVO.setMilkAmount(vo.getMilkAmount());
                // 拼接配送产品 = productName + skuDesc + (milkAmount + unit)
                StringBuilder dispatchProductDesc = new StringBuilder();
                dispatchProductDesc.append(vo.getProductName()).append(" ").append(vo.getSkuDesc()).append("(共")
                        .append(vo.getMilkAmount()).append(vo.getUnit()).append(")");
                excelVO.setDispatchProductDesc(dispatchProductDesc.toString());
                excelVO.setProductName(vo.getProductName());
                excelVO.setSkuCode(vo.getSkuCode());

                Integer number = vo.getNumber();
                excelVO.setSkuEcode(StringUtils.isNotBlank(vo.getSkuEcode()) ?vo.getSkuEcode() : vo.getSkuCode());
                // 中台商品数量 = milkAmount * number
                if (null != number) {
                    excelVO.setCalculatedNumber(vo.getMilkAmount() / vo.getDivisor() * number);
                } else {
                    excelVO.setCalculatedNumber(vo.getMilkAmount() / vo.getDivisor());
                }
                excelVO.setNumber(vo.getMilkAmount() / vo.getDivisor());
                excelVO.setPlanStatusDesc(vo.getPlanStatusDesc());
                // 物流公司 = packageList 中的expressCompany 用英文逗号拼接
                excelVO.setExpressCompanies(vo.getPackageList().stream().map(PlanPackageVO::getExpressCompany).collect(Collectors.joining(",")));
                excelVO.setTrackingNos(vo.getPackageList().stream().map(PlanPackageVO::getTrackingNo).collect(Collectors.joining(",")));
                excelVO.setPushFlagDesc(vo.getPushFlagDesc());
                excelVO.setPushFailReason(vo.getPushFailReason());
                excelVO.setAddress("收货人：" + vo.getReceiverName() + " " + "手机号：" + vo.getReceiverPhone() + " " + "省：" + vo.getProvince() + " " + "市：" + vo.getCity() + " " + "区：" + vo.getDistrict() + " " + "收货详细地址：" + vo.getAddressDetail());
            }else{
                Integer number = vo.getNumber();
                excelVO.setSkuEcode(StringUtils.isNotBlank(vo.getSkuEcode()) ?vo.getSkuEcode() : vo.getSkuCode());
                if (null != number) {
                    excelVO.setCalculatedNumber(vo.getMilkAmount() / vo.getDivisor() * number);
                } else {
                    excelVO.setCalculatedNumber(vo.getMilkAmount() / vo.getDivisor());
                }
            }
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }

    private List<CellRangeAddress> calculateMergeRegions(List<DispatchPlanListOrderExcelVO> vos, List<Integer> mergeColumnRegion, int lineCount) {
        if(CollectionUtils.isEmpty(vos)){
            return new ArrayList<>();
        }
//        int lineCount = 1; // 起始行（0表示表头，1表示数据行）
        List<CellRangeAddress> rangeCellList = new ArrayList<>();
        Map<Long, Long> groupMap = vos.stream().collect(Collectors.groupingBy(DispatchPlanListOrderExcelVO::getId, LinkedHashMap::new, Collectors.counting()));
        for (Map.Entry<Long, Long> entry : groupMap.entrySet()) {
            int count = entry.getValue().intValue();
            if(count > 1){
                int startRowIndex = lineCount;
                // 如合并第2到4行，共3行，行索引从1到3
                int endRowIndex = lineCount + count - 1;
                for (int columnIndex : mergeColumnRegion) {
                    rangeCellList.add(new CellRangeAddress(startRowIndex, endRowIndex, columnIndex, columnIndex));
                }
            }
            lineCount += count;
        }
        return rangeCellList;
    }
}
