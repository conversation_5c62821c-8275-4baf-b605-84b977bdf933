package com.hengtiansoft.operation.dispatch.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 */
@Data
public class DispatchRuleDetailVO {

    @ApiModelProperty("配送规则ID")
    private Long id;

    @ApiModelProperty("奶卡卡号")
    private String cardNumber;

    @ApiModelProperty("奶卡类别id")
    private Long categoryId;

    @ApiModelProperty("商品skuId")
    private Long skuId;

    @ApiModelProperty("商品spuId")
    private Long productId;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("商品规格描述")
    private String skuDesc;

    @ApiModelProperty("商品单位")
    private String unit;

    @ApiModelProperty("单次配送提数")
    private Integer milkAmount;

    @ApiModelProperty("配送次数")
    private Integer times;

    @ApiModelProperty("配送模式 0-工作日送达 1-周末送达")
    private Integer dispatchMode;

    @ApiModelProperty("发货类型（1：按每周几，2：按每月几号, 3:  按周（根据首次发货开始计算） 4，按天（根据首次发货开始计算））")
    private Integer dispatchType;

    @ApiModelProperty("发货日期（1~31）可表示周一，1号")
    private Integer dispatchDate;

    @ApiModelProperty("间隔时长")
    private Integer intervalTime;

    @ApiModelProperty("发货日期描述（每月X号/星期X）")
    private String dispatchDateDesc;

    @ApiModelProperty("规则状态（1：服务中，2：已结束，3：暂停中，4：服务完成）")
    private Integer ruleStatus;

    @ApiModelProperty("类型 0- 普通提货 1-普通特权提货 2-低温奶提货  3- 低温奶特权提货")
    private Integer ruleType;

    @ApiModelProperty("规则状态描述")
    private String ruleStatusDesc;

    @ApiModelProperty("收货人名称")
    private String receiverName;

    @ApiModelProperty("收货人电话")
    private String receiverPhone;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("省code")
    private String provinceCode;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("市code")
    private String cityCode;

    @ApiModelProperty("区")
    private String district;

    @ApiModelProperty("区code")
    private String districtCode;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("用户备注")
    private String userRemark;
}
