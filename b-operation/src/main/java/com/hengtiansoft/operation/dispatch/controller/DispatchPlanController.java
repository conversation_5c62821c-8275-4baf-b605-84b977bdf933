package com.hengtiansoft.operation.dispatch.controller;

import com.alibaba.excel.EasyExcel;
import com.hengtiansoft.common.encrypt.rsa.RSACipher;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.config.RequestLogger;
import com.hengtiansoft.operation.dispatch.entity.vo.DispatchPlanListVO;
import com.hengtiansoft.operation.dispatch.service.DispatchPlanService;
import com.hengtiansoft.operation.dispatch.util.LogisticsInfoUploadDataListener;
import com.hengtiansoft.operation.logistics.service.LogisticsInfoService;
import com.hengtiansoft.order.entity.dto.DispatchPlanSaveDTO;
import com.hengtiansoft.order.entity.dto.DispatchPlanSearchDTO;
import com.hengtiansoft.order.entity.dto.LogisticsInfoExcelDTO;
import com.hengtiansoft.order.entity.vo.ExpressInfoVO;
import com.hengtiansoft.order.entity.vo.PlanedMilkStatisticalTableVO;
import com.hengtiansoft.order.entity.vo.milkdispatch.DispatchPlanActVO;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.enums.PushFlagEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Author: <EMAIL>
 */
@Api(tags = "配送计划")
@RestController
@RequestMapping("/dispatch/plan")
public class DispatchPlanController {

    @Value("${rsa.dispatch-private-key}")
    private String rsaDispatchPrivateKey;

    @Autowired
    private DispatchPlanService dispatchPlanService;

    @Resource
    private LogisticsInfoService logisticsInfoService;

    @ApiOperation(value = "查询物流")
    @GetMapping("/queryExpress")
    public Response<ExpressInfoVO> search(@RequestParam String expressCode,
                                          @RequestParam(required = false) String orderNo,
                                          @RequestParam(required = false) Long planId,
                                          @RequestParam(required = false) String expressCompany) {
        return ResponseFactory.success(dispatchPlanService.queryExpress(expressCode, orderNo, planId, expressCompany));
    }

    @ApiOperation(value = "列表")
    @PostMapping("/search")
    public Response<PageVO<DispatchPlanListVO>> search(@RequestBody DispatchPlanSearchDTO searchDTO) {
        return ResponseFactory.success(dispatchPlanService.search(searchDTO));
    }

    @ApiOperation(value = "列表")
    @PostMapping("/searchPublic")
    public Response<PageVO<DispatchPlanListVO>> search(@RequestBody DispatchPlanSearchDTO searchDTO, HttpServletRequest request) {
        if(StringUtils.isBlank(searchDTO.getSign())){
            throw new BusinessException("签名不能为空!");
        }
        String phone = RSACipher.decrypt(searchDTO.getSign(), rsaDispatchPrivateKey);

        if (StringUtils.isBlank(phone) || StringUtils.isBlank(searchDTO.getPhone())) {
            throw new BusinessException("用户手机号码不能为空!");
        }
        if(!phone.equals(searchDTO.getPhone())){
            throw new BusinessException("手机号码错误!");
        }

        searchDTO.setPushFlag(PushFlagEnum.COMPLETED.getCode());
        return ResponseFactory.success(dispatchPlanService.search(searchDTO));
    }

    @ApiOperation(value = "暂停")
    @GetMapping("/pause")
    @RequestLogger(value = "配送计划暂停")
    public Response<Object> pause(@RequestParam Long id) {
        dispatchPlanService.pause(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    @RequestLogger(value = "配送计划删除")
    public Response<Object> delete(@RequestParam Long id) {
        dispatchPlanService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "激活")
    @GetMapping("/activate")
    @RequestLogger(value = "配送计划激活")
    public Response<DispatchPlanActVO> activate(@RequestParam Long id) {
        return ResponseFactory.success(dispatchPlanService.activate(id));
    }

    @ApiOperation(value = "编辑")
    @PostMapping("/update")
    @RequestLogger(value = "配送计划编辑")
    public Response<Object> update(@RequestBody DispatchPlanSaveDTO param) {
        dispatchPlanService.update(param);
        return ResponseFactory.success();
    }

    @Deprecated
    @ApiOperation(value = "编辑所有")
    @PostMapping("/updateAll")
    @RequestLogger(value = "配送计划编辑")
    public Response<Object> updateAll(@RequestBody DispatchPlanSaveDTO param) {
        dispatchPlanService.updateAll(param);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "推送")
    @GetMapping("/push")
    public Response<Object> push(@RequestParam Long id) {
        dispatchPlanService.push(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "计划提奶数量")
    @GetMapping("/dataStatistics")
    public Response<PlanedMilkStatisticalTableVO> dataStatistics() {
        return ResponseFactory.success(dispatchPlanService.dataStatistics());
    }

    @ApiOperation(value = "test-cron")
    @GetMapping("/expirePlan")
    public Response<List<Long>> expirePlan() {
        List<Long> planIds = dispatchPlanService.expirePlan();
        return ResponseFactory.success(planIds);
    }

    /**
     * 文件上传
     */
    @PostMapping("upload")
    @ApiOperation("物流信息导入")
    @ResponseBody
    public Response<Object> synLogistics(MultipartFile file){
        try {
            EasyExcel.read(file.getInputStream(), LogisticsInfoExcelDTO.class, new LogisticsInfoUploadDataListener(logisticsInfoService)).sheet().doRead();
        } catch (IOException e) {
            return ResponseFactory.failure(e.getMessage());
        }
        return ResponseFactory.success();
    }

    @ApiOperation(value = "配送计划订单导出")
    @PostMapping("/order/export")
    public Response<Void> exportOrder(@RequestBody DispatchPlanSearchDTO searchDTO) {
        searchDTO.setExportType(FileExportCenterEnum.milk_dispatch_plan_order_list.getCode());
        dispatchPlanService.exportOrder(searchDTO);
        return ResponseFactory.success();
    }
}
