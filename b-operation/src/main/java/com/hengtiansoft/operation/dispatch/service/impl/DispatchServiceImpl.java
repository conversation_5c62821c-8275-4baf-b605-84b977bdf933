package com.hengtiansoft.operation.dispatch.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengtiansoft.address.entity.dto.CardReceiveAddressDTO;
import com.hengtiansoft.address.entity.po.CardReceiveAddress;
import com.hengtiansoft.address.manager.CardReceiveAddressManager;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.DefaultEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.content.dao.BaseDataDao;
import com.hengtiansoft.content.entity.po.BaseData;
import com.hengtiansoft.content.enums.BaseDataTypeEnum;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.po.CardCategory;
import com.hengtiansoft.item.enumeration.CardTypeEnum;
import com.hengtiansoft.item.manager.CardCategoryManager;
import com.hengtiansoft.operation.dispatch.dto.RuleRoleDTO;
import com.hengtiansoft.operation.dispatch.service.DispatchService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.entity.dto.MilkDispatchDTO;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.manager.CustomerUserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class DispatchServiceImpl implements DispatchService {

    @Resource
    private BaseDataDao baseDataDao;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private CardReceiveAddressManager cardReceiveAddressManager;
    @Resource
    private CardCategoryManager cardCategoryManager;


    @Override
    public void verifyRole(MilkDispatchDTO newAddress, MilkDispatchDTO oldAddress, Card card, Integer roleType){
        UserDetailDTO detailDTO = UserUtil.getDetails();
        CustomerUser user = customerUserManager.findById(card.getUserId());
        if(null == user){
            throw new BusinessException("用户不存在!");
        }

        boolean flag = verify(user, card, roleType);
        if(null != oldAddress){
            if(!flag){
                throw new BusinessException("该奶卡有提奶地址数限制，您无权限修改收货信息!");
            }
            //编辑不占用地址数
/*            if(null == address){
                insertCardReceiveAddress(newAddress, card, detailDTO);
            }*/
        }else{
            CardReceiveAddressDTO checkDto = new CardReceiveAddressDTO();
            checkDto.setUserId(card.getUserId());
            checkDto.setAddressId(Long.valueOf(newAddress.getDistrictCode()));
            checkDto.setReceiveName(newAddress.getReceiverName());
            checkDto.setPhone(newAddress.getReceiverPhone());
            checkDto.setCardNumber(card.getCardNumber());
            checkDto.setDetailAddress(newAddress.getAddressDetail());
            CardReceiveAddress address = cardReceiveAddressManager.check(checkDto);
            if(null == address){
                if(!flag){
                    checkAddressLimit(card);
                }
                insertCardReceiveAddress(newAddress, card, detailDTO);
            }
        }
    }

    private void insertCardReceiveAddress(MilkDispatchDTO param, Card card, UserDetailDTO detailDTO) {
        CardReceiveAddress insertAdd = new CardReceiveAddress();
        insertAdd.setUserId(card.getUserId());
        insertAdd.setCardNumber(card.getCardNumber());
        insertAdd.setReceiveName(param.getReceiverName());
        insertAdd.setPhone(param.getReceiverPhone());
        insertAdd.setAddressId(Long.valueOf(param.getDistrictCode()));
        insertAdd.setDetailAddress(param.getAddressDetail());
        insertAdd.setIsDefault(DefaultEnum.NO.getCode());
        insertAdd.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        insertAdd.setCreateTime(new Date());
        insertAdd.setUpdateTime(new Date());
        insertAdd.setOperater(detailDTO.getUserName());
        cardReceiveAddressManager.insert(insertAdd);
    }

    private void checkAddressLimit(Card card) {
        int addressCount = cardReceiveAddressManager.countCardAddress(card.getCardNumber());
        CardCategory cardCategory = cardCategoryManager.selectById(card.getCategoryId());
        if (addressCount >= cardCategory.getAddressLimit()) {
            throw new BusinessException("超出地址数量限制");
        }
    }

    private boolean verify(CustomerUser user, Card card, Integer type){
        //白名单无需校验
        if(BasicFlagEnum.YES.getKey().equals(user.getWhiteList())){
            return true;
        }
        //客服权限校验
        BaseData ruleRole = baseDataDao.selectByType(type);
        if(null != ruleRole){
            List<SimpleGrantedAuthority> authList = UserUtil.getAuthorities();
            List<String> roleList = StreamUtils.toList(authList, SimpleGrantedAuthority::getAuthority);
            String roleStr = ruleRole.getValue();
            RuleRoleDTO dto = null;
            try {
                dto = JSON.parseObject(roleStr, RuleRoleDTO.class);
            }catch (Exception e){
                log.error(BaseDataTypeEnum.getDescByCode(type) + " 配置有误, value:{}", roleStr);
                return false;
            }
            // 收件人姓名权限/联系号码权限/收货地址权限
            if(roleList.contains(dto.getReceiverName())
                    && roleList.contains(dto.getReceiverPhone())
                    && roleList.contains(dto.getAddress())){
                return true;
            }
        }
        //奶卡类型校验
        if(CardTypeEnum.CYCLE_CARD.getCode().equals(card.getCardType())){
            return true;
        }
        //类别地址数限制
        CardCategory category = cardCategoryManager.selectById(card.getCategoryId());
        if(null == category || null == category.getAddressLimit()){
            return true;
        }
        return false;
    }
}
