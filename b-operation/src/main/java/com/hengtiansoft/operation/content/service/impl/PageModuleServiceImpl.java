package com.hengtiansoft.operation.content.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.content.entity.dto.PageModuleSaveDTO;
import com.hengtiansoft.content.entity.po.PageModule;
import com.hengtiansoft.content.entity.vo.PageModuleVO;
import com.hengtiansoft.content.manager.PageModuleManager;
import com.hengtiansoft.operation.content.service.PageModuleService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-02 17:21
 **/
@Service
public class PageModuleServiceImpl implements PageModuleService {

    @Resource
    private PageModuleManager pageModuleManager;
    @Override
    public void update(PageModuleSaveDTO saveDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PageModule pageModule = pageModuleManager.getByPageCodeAndModuleCode(saveDTO.getPageCode(), saveDTO.getModuleCode());
        Asserts.notNull(pageModule, "该配置为空");
        JSONObject config = saveDTO.getConfig();
        if (Objects.isNull(config)) {
            pageModule.setConfig(null);
        } else {
            pageModule.setConfig(config.toJSONString());
        }
        pageModule.setDisplay(saveDTO.getDisplay());
        pageModule.setUpdateTime(new Date());
        pageModule.setOperator(customerUser.getUserName());
        pageModuleManager.update(pageModule);
    }

    @Override
    public PageModuleVO getModule(String pageCode, String moduleCode) {
        boolean logged = UserUtil.isLogged();
        if (!logged) {
            throw new BusinessException("请登录重试");
        }
        PageModule pageModule = pageModuleManager.getByPageCodeAndModuleCode(pageCode, moduleCode);
        Asserts.notNull(pageModule, "该配置为空");
        PageModuleVO moduleVO = BeanUtils.copy(pageModule, PageModuleVO::new);
        moduleVO.setConfig(StringUtils.isNotEmpty(pageModule.getConfig())? JSON.parseObject(pageModule.getConfig()) : null);
        return moduleVO;
    }

    @Override
    public List<PageModuleVO> getModuleList(String pageCode) {
        boolean logged = UserUtil.isLogged();
        if (!logged) {
            throw new BusinessException("请登录重试");
        }
        List<PageModule> modules = pageModuleManager.getByPageCode(pageCode);
        if (CollectionUtils.isEmpty(modules)) return Collections.emptyList();
        List<PageModuleVO> moduleVOList = Lists.newArrayList();
        for (PageModule module : modules) {
            PageModuleVO moduleVO = BeanUtils.copy(module, PageModuleVO::new);
            moduleVO.setConfig(StringUtils.isNotEmpty(module.getConfig())? JSON.parseObject(module.getConfig()) : null);
            moduleVOList.add(moduleVO);
        }
        moduleVOList = moduleVOList.stream().sorted(Comparator.comparing(PageModuleVO::getSort)).collect(Collectors.toList());
        return moduleVOList;
    }
}
