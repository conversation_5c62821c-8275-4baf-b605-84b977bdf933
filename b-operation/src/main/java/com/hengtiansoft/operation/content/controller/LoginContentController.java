package com.hengtiansoft.operation.content.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.LoginContentAddDTO;
import com.hengtiansoft.content.entity.dto.LoginContentIssueStatusUpdateDTO;
import com.hengtiansoft.content.entity.dto.LoginContentSearchDTO;
import com.hengtiansoft.content.entity.dto.LoginContentUpdateDTO;
import com.hengtiansoft.content.entity.vo.LoginContentListVO;
import com.hengtiansoft.content.entity.vo.LoginContentVO;
import com.hengtiansoft.operation.content.service.LoginContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Description: 登录内容管理
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "登录内容管理")
@RequestMapping("loginContent")
public class LoginContentController {

    @Autowired
    private LoginContentService loginContentService;

    @ApiOperation(value = "新增登录内容")
    @PostMapping("/add")
    public Response<Long> add(@ApiParam("登录内容新增信息") @RequestBody @Validated LoginContentAddDTO loginContentAddDTO) {
        loginContentService.insert(loginContentAddDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "修改登录内容")
    @PostMapping("/update")
    public Response<Object> update(@ApiParam("登录内容修改信息") @RequestBody @Validated LoginContentUpdateDTO loginContentUpdateDTO) {
        loginContentService.update(loginContentUpdateDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "新增登录内容")
    @PostMapping("/sort")
    public Response<Long> sort(@ApiParam("登录内容新增信息") @RequestBody LoginContentUpdateDTO loginContentUpdateDTO) {
        loginContentService.sort(loginContentUpdateDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除登录内容")
    @DeleteMapping("/delete")
    public Response<Object> delete(@ApiParam("登录内容id") @RequestParam Long id) {
        loginContentService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "登录内容详情")
    @GetMapping("/info")
    public Response<LoginContentVO> one(@ApiParam("登录内容id") @RequestParam Long id) {
        return ResponseFactory.success(loginContentService.one(id));
    }

    @ApiOperation(value = "登录内容列表")
    @PostMapping("/list")
    public Response<PageVO<LoginContentListVO>> list(@ApiParam("登录内容查询信息") @RequestBody LoginContentSearchDTO loginContentSearchDTO) {
        return ResponseFactory.success(loginContentService.list(loginContentSearchDTO));
    }

    @ApiOperation(value = "登录内容发布状态修改")
    @PostMapping("/issueStatusUpdate")
    public Response<Object> issue(@ApiParam("登录内容发布状态") @RequestBody LoginContentIssueStatusUpdateDTO loginContentIssueStatusUpdateDTO) {
        loginContentService.issueStatusUpdate(loginContentIssueStatusUpdateDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "结束")
    @GetMapping("/end")
    public Response<Object> end(@ApiParam("登录内容id") @RequestParam Long id) {
        loginContentService.end(id);
        return ResponseFactory.success();
    }

}
