package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.hengtiansoft.common.enumeration.CommonLinkTypeEnum;
import com.hengtiansoft.content.entity.dto.PageModuleContentSaveDTO;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.thirdpart.interfaces.FileManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniSmsManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-09 11:20
 **/
public abstract class AbstractPageModuleCommonTemplate {

    @Resource
    private WeChatMiniSmsManager weChatMiniSmsManager;
    @Resource
    private FileManager fileManager;


    public PageModuleContent assembleContent(PageModuleContentSaveDTO saveDTO, UserDetailDTO customerUser) {
        PageModuleContent content = assembleCommonContent(saveDTO, customerUser.getUserName());
        return assembleCustomContent(content, saveDTO);
    }

    public void assembleUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO, UserDetailDTO customerUser) {
        assembleCommonUpdateContent(content, saveDTO, customerUser.getUserName());
        assembleCustomUpdateContent(content, saveDTO);
    }

    protected abstract void assembleCustomUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO);

    private void assembleCommonUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO, String userName) {
        content.setName(saveDTO.getName());
        content.setImageUrl(saveDTO.getImageUrl());
        content.setDisplay(saveDTO.getDisplay() == null? content.getDisplay() : saveDTO.getDisplay());
        content.setSort(saveDTO.getSort());
        content.setStartTime(saveDTO.getStartTime());
        content.setEndTime(saveDTO.getEndTime());
        content.setPeopleLimit(saveDTO.getPeopleLimit());
        if (CollectionUtils.isNotEmpty(saveDTO.getGradeList())) {
            String gradeStr = String.join(",", saveDTO.getGradeList());
            content.setGrade(gradeStr);
        } else {
            content.setGrade(null);
        }
        content.setBirthdayUser(saveDTO.getBirthdayUser());
        content.setFirstJoinDuration(saveDTO.getFirstJoinDuration());
//        content.setLabelId(saveDTO.getLabelId());
        List<Long> labelIds = saveDTO.getLabelIds();
        if (CollectionUtils.isNotEmpty(labelIds)) {
            content.setLabelId(labelIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        } else {
            content.setLabelId(null);
        }
        if (saveDTO.getLinkType().equals(CommonLinkTypeEnum.INEEER_LINK.getCode())) {
            if (!Objects.equals(content.getLink(), saveDTO.getLink())) {
                content.setMiniQrcode(createMiniQrcode(saveDTO.getLink()));
            }
        } else {
            content.setMiniQrcode("");
        }
        if (saveDTO.getLinkType().equals(CommonLinkTypeEnum.WEI.getCode())) {
            content.setMiniQrcode(saveDTO.getMiniQrcode());
        }
        content.setLinkType(saveDTO.getLinkType());
        content.setLink(saveDTO.getLink());
        content.setAppId(saveDTO.getAppId());
        content.setUpdateTime(new Date());
        content.setOperator(userName);
    }

    protected abstract PageModuleContent assembleCustomContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO);

    private PageModuleContent assembleCommonContent(PageModuleContentSaveDTO saveDTO, String userName) {
        PageModuleContent content = new PageModuleContent();
        content.setPageCode(saveDTO.getPageCode());
        content.setModuleCode(saveDTO.getModuleCode());
        content.setName(saveDTO.getName());
        content.setImageUrl(saveDTO.getImageUrl());
        content.setDisplay(1);
        content.setSort(saveDTO.getSort());
        content.setStartTime(saveDTO.getStartTime());
        content.setEndTime(saveDTO.getEndTime());
        content.setPeopleLimit(saveDTO.getPeopleLimit());
        if (CollectionUtils.isNotEmpty(saveDTO.getGradeList())) {
            String gradeStr = String.join(",", saveDTO.getGradeList());
            content.setGrade(gradeStr);
        }
        content.setLinkType(saveDTO.getLinkType());
        content.setLink(saveDTO.getLink());
        if (saveDTO.getLinkType().equals(CommonLinkTypeEnum.INEEER_LINK.getCode())) {
            content.setMiniQrcode(createMiniQrcode(saveDTO.getLink()));
        }
        if (saveDTO.getLinkType().equals(CommonLinkTypeEnum.WEI.getCode())) {
            content.setMiniQrcode(saveDTO.getMiniQrcode());
        }
        content.setFirstJoinDuration(saveDTO.getFirstJoinDuration());
        List<Long> labelIds = saveDTO.getLabelIds();
        if (CollectionUtils.isNotEmpty(labelIds)) {
            content.setLabelId(labelIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        } else {
            content.setLabelId(null);
        }
        content.setAppId(saveDTO.getAppId());
        content.setOperator(userName);
        content.setCreateTime(new Date());
        content.setUpdateTime(new Date());
        content.setDelflag(0);
        return content;
    }


    public String createMiniQrcode(String link) {
        if(StringUtils.startsWith(link, "/")){
            link = StringUtils.substring(link, 1);
        }
        String[] split = StringUtils.split(link, "?");
        String page = split[0];
        String scene = split.length > 1 ? split[1] : "random=1";
        byte[] bytes = weChatMiniSmsManager.miniCode(scene, page, 800);
        String miniQrcode = fileManager.uploadQrcode(bytes);
        return miniQrcode;
    }
}
