package com.hengtiansoft.operation.content.service.impl;

import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.PeopleLimitEnum;
import com.hengtiansoft.common.enumeration.PlatformEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.content.entity.dto.AdvertisementAddDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementSearchDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementUpdateDTO;
import com.hengtiansoft.content.entity.po.Advertisement;
import com.hengtiansoft.content.entity.vo.AdvertisementVO;
import com.hengtiansoft.content.entity.vo.TimeVO;
import com.hengtiansoft.content.enums.AdvertisementPublicEnum;
import com.hengtiansoft.content.enums.AdvertisementStatus;
import com.hengtiansoft.content.manager.AdvertisementManager;
import com.hengtiansoft.operation.content.service.AdvertisementService;
import com.hengtiansoft.privilege.manager.PeopleLabelManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniCouponManager;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 广告位管理实现类
 *
 * <AUTHOR>
 * @since 27.03.2020
 */
@Service
public class AdvertisementServiceImpl implements AdvertisementService {

    @Autowired
    private AdvertisementManager advertisementManager;

    @Autowired
    private WeChatMiniCouponManager weChatMiniCouponManager;
    @Resource
    private PeopleLabelManager peopleLabelManager;


    @Override
    public void add(AdvertisementAddDTO dto) {
        Advertisement advertisement = new Advertisement();
        BeanUtils.copy(dto, advertisement);
        advertisement.setPlatform(dto.getPlatform().getDesc());
        advertisement.setStatus(getStatus(dto.getOnlineTime(), dto.getOfflineTime()).getCode());
        if(!CollectionUtils.isEmpty(dto.getLabelIds())){
            advertisement.setLabelId(StringUtils.join(dto.getLabelIds(), ","));
        }else{
            advertisement.setLabelId(Strings.EMPTY);
        }
        checkInfo(dto);
        advertisementManager.add(advertisement);


        //非公开生成链接
        if (Objects.nonNull(dto.getIsPublic()) && AdvertisementPublicEnum.PRIVATE.getCode().equals(dto.getIsPublic())) {
            String urlScheme = weChatMiniCouponManager.getUrl("/packageA/pages/priBannerDetail/priBannerDetailDetail","id="+advertisement.getId());
            if (StringUtils.isNotBlank(urlScheme)) {
                Advertisement updateAd = new Advertisement();
                updateAd.setId(advertisement.getId());
                updateAd.setPrivateUrl(urlScheme);
                advertisementManager.update(updateAd);
            }
        }

        String httpUrl = weChatMiniCouponManager.getUrlLink("/packageA/pages/bannerDetail/bannerDetailDetail","id="+advertisement.getId()+"&dl=1");
        if (StringUtils.isNotBlank(httpUrl)) {
            Advertisement updateAd = new Advertisement();
            updateAd.setId(advertisement.getId());
            updateAd.setHttpUrl(httpUrl);
            advertisementManager.update(updateAd);
        }
    }

    @Override
    public void update(AdvertisementUpdateDTO dto) {
        Advertisement advertisement = new Advertisement();
        BeanUtils.copy(dto, advertisement);
        advertisement.setPlatform(dto.getPlatform().getDesc());
        AdvertisementAddDTO addDTO = BeanUtils.copy(dto, AdvertisementAddDTO::new);
        advertisement.setStatus(getStatus(dto.getOnlineTime(), dto.getOfflineTime()).getCode());
        if(!CollectionUtils.isEmpty(dto.getLabelIds())){
            advertisement.setLabelId(StringUtils.join(dto.getLabelIds(), ","));
        }else{
            advertisement.setLabelId(Strings.EMPTY);
        }
        checkInfo(addDTO);

        if(Objects.nonNull(dto.getIsPublic())){
            if(AdvertisementPublicEnum.PRIVATE.getCode().equals(dto.getIsPublic())){
                String urlScheme = weChatMiniCouponManager.getUrl("/packageA/pages/priBannerDetail/priBannerDetailDetail","id="+advertisement.getId());
                advertisement.setPrivateUrl(urlScheme);
            }else{
                advertisement.setPrivateUrl("-");
            }
        }

        String httpUrl = weChatMiniCouponManager.getUrlLink("/packageA/pages/bannerDetail/bannerDetailDetail","id="+advertisement.getId()+"&dl=1");
        if (StringUtils.isNotBlank(httpUrl)) {
            advertisement.setHttpUrl(httpUrl);
        }

        advertisementManager.update(advertisement);
    }

    @Override
    public void delete(Long id) {
        advertisementManager.delete(id);
    }

    @Override
    public AdvertisementVO one(Long id) {
        AdvertisementVO advertisementVO = advertisementManager.one(id);
        if(StringUtils.isNotBlank(advertisementVO.getLabelId())){
            advertisementVO.setLabelIds(Arrays.stream(advertisementVO.getLabelId().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }
        return advertisementVO;
    }

    @Override
    public PageVO<AdvertisementVO> list(AdvertisementSearchDTO dto) {
        return advertisementManager.findList(dto);
    }


    private Integer checkTime(List<TimeVO> newTimeVOS, List<TimeVO> oldTimeVOS) {
        Integer num = 0;  //没有冲突返回null,有冲突返回冲突的时间段
        for (int i = 0; i < newTimeVOS.size(); i++) {
            Date newStart = newTimeVOS.get(i).getOnlineTime();
            Date newEnd = newTimeVOS.get(i).getOfflineTime();
            for (int j = 0; j < oldTimeVOS.size(); j++) {
                Date oldStart = oldTimeVOS.get(j).getOnlineTime();
                Date oldEnd = oldTimeVOS.get(j).getOfflineTime();

                //compareTo返回结果-1 0 1 表示前者比后者<,=,>关系 ,下面的if判断涉及具体的怎样比较可以自行优化
                if ((oldStart.compareTo(newStart) == -1 && newStart.compareTo(oldEnd) == -1)
                        || (oldStart.compareTo(newEnd) == -1 && newEnd.compareTo(oldEnd) == -1)
                        || (newStart.compareTo(oldStart) == -1 && oldStart.compareTo(newEnd) == -1)   //新加部分
                        || (newStart.compareTo(oldEnd) == -1 && oldEnd.compareTo(newEnd) == -1)   //新加部分
                        || oldEnd.compareTo(newStart) == 0 || oldStart.compareTo(newEnd) == 0
                        || oldEnd.compareTo(newEnd) == 0 || oldStart.compareTo(newStart) == 0) {
                    num = num + 1;
                }
            }
        }
        return num;

    }

    private List<TimeVO> prepareTime(AdvertisementAddDTO dto, List<AdvertisementVO> advertisementVOS) {
        List<TimeVO> timeVOS = new ArrayList<>();
        if (dto != null) {
            TimeVO timeVO = new TimeVO();
            timeVO.setOnlineTime(dto.getOnlineTime());
            timeVO.setOfflineTime(dto.getOfflineTime());
            timeVOS.add(timeVO);
        }
        if (!CollectionUtils.isEmpty(advertisementVOS)) {
            advertisementVOS.stream().forEach(data -> {
                TimeVO timeVO = new TimeVO();
                timeVO.setOnlineTime(data.getOnlineTime());
                timeVO.setOfflineTime(data.getOfflineTime());
                timeVOS.add(timeVO);
            });
        }
        return timeVOS;
    }

    private void checkInfo(AdvertisementAddDTO dto) {
        if(StringUtils.isBlank(dto.getPicUrl()) && dto.getPlatform() != PlatformEnum.HOMEPAGE_TIP){
            throw new BusinessException("请务必填写[广告图片URL]");
        }
        if(StringUtils.isBlank(dto.getTipContent()) && dto.getPlatform() == PlatformEnum.HOMEPAGE_TIP){
            throw new BusinessException("请务必填写[提示内容]");
        }

        if(Objects.isNull(dto.getPeopleLimit())){
            throw new BusinessException("人群限制不能为空！");
        }

        if(PeopleLimitEnum.LABEL.getCode().equals(dto.getPeopleLimit()) && CollectionUtils.isEmpty(dto.getLabelIds())){
            throw new BusinessException("标签不能为空！");
        }

        AdvertisementUpdateDTO updateDTO = new AdvertisementUpdateDTO();
        updateDTO.setPlatform(dto.getPlatform());
        if (dto.getId() != null) {
            updateDTO.setId(dto.getId());
        }
        List<AdvertisementVO> advertisementVOS = advertisementManager.findByExample(updateDTO);
        List<TimeVO> newTimeVOS = prepareTime(dto, null);
        List<TimeVO> oldTimeVOS = prepareTime(null, advertisementVOS);
        Integer checkTimeNum = checkTime(newTimeVOS, oldTimeVOS);
        if (checkTimeNum > 0 && dto.getPlatform() == PlatformEnum.HOMEPAGE_GIFTCOUPON) {
            throw new BusinessException("当前时间已存在福利券广告位");
        }

        if (checkTimeNum > 0 && dto.getPlatform() == PlatformEnum.MEMBER_BENEFITS) {
            throw new BusinessException("当前时间已存在会员权益banner");
        }

        if (checkTimeNum > 0 && dto.getPlatform() == PlatformEnum.RIBBON_CYCLE) {
            throw new BusinessException("当前时间已存在功能区-周期购");
        }

        if (checkTimeNum > 0 && dto.getPlatform() == PlatformEnum.RIBBON_CARD) {
            throw new BusinessException("当前时间已存在功能区-奶卡");
        }
    }

    private AdvertisementStatus getStatus(Date onTime, Date offTime) {
        Date nowTime = new Date();
        if (null != onTime && nowTime.before(onTime)) {
            return AdvertisementStatus.STAY_ONLINE;
        } else if (null != onTime && null != offTime && nowTime.after(onTime) && nowTime.before(offTime)) {
            return AdvertisementStatus.UNDER_WAY;
        } else {
            return AdvertisementStatus.FINISHED;
        }

    }
}
