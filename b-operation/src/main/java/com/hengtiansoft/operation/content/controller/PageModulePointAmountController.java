package com.hengtiansoft.operation.content.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountItemSaveDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountListDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountSortDTO;
import com.hengtiansoft.content.entity.vo.PageModuleItemSelectedVO;
import com.hengtiansoft.content.entity.vo.PageModulePointAmountListVO;
import com.hengtiansoft.operation.content.service.PageModulePointAmountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "积分抵现推荐位B端")
@RequestMapping("page/module/pointAmount")
public class PageModulePointAmountController {

    @Resource
    private PageModulePointAmountService pageModulePointAmountService;

    @ApiOperation("列表")
    @PostMapping("/list")
    public Response<PageVO<PageModulePointAmountListVO>> list(@RequestBody PageModulePointAmountListDTO dto) {
        return ResponseFactory.success(pageModulePointAmountService.list(dto));
    }

    @ApiOperation("移除")
    @GetMapping("/delete")
    public Response<Void> delete(@RequestParam Long id) {
        pageModulePointAmountService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("排序")
    @PostMapping("/sort")
    public Response<Void> sort(@RequestBody @Validated PageModulePointAmountSortDTO dto) {
        pageModulePointAmountService.sort(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("已选择推荐位商品")
    @GetMapping("/item/selected")
    public Response<List<PageModuleItemSelectedVO>> selected(@RequestParam Integer suggestType) {
        return ResponseFactory.success(pageModulePointAmountService.selected(suggestType));
    }

    @ApiOperation("添加")
    @PostMapping("/item/save")
    public Response<String> save(@RequestBody PageModulePointAmountItemSaveDTO dto) {
        return ResponseFactory.success(pageModulePointAmountService.save(dto));
    }
}
