package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.hengtiansoft.operation.content.service.impl.pageStrategy.PageModuleOperateStrategy;
import org.apache.http.util.Asserts;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-27 11:10
 **/
public class PageModuleStrategyFactory {

    private static final Map<String, PageModuleOperateStrategy> services = new ConcurrentHashMap<>();

    // pageModuleCode  = pageCode + "_" + moduleCode
    public static PageModuleOperateStrategy getByPageModuleCode(String pageModuleCode) {
        return services.get(pageModuleCode);
    }

    public static void register(String pageModuleCode, PageModuleOperateStrategy service) {
        Asserts.notEmpty(pageModuleCode, "pageCode can not be empty");
        services.put(pageModuleCode, service);
    }
}
