package com.hengtiansoft.operation.content.service.impl;

import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.PeopleLimitEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.content.entity.dto.QuestionnaireDTO;
import com.hengtiansoft.content.entity.po.Questionnaire;
import com.hengtiansoft.content.entity.vo.QuestionnaireVO;
import com.hengtiansoft.content.entity.vo.TimeVO;
import com.hengtiansoft.content.enums.AdvertisementStatus;
import com.hengtiansoft.content.manager.QuestionnaireManager;
import com.hengtiansoft.operation.content.service.QuestionnaireService;
import com.hengtiansoft.privilege.entity.po.PeopleLabel;
import com.hengtiansoft.privilege.manager.PeopleLabelManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Service
public class QuestionnaireServiceImpl implements QuestionnaireService {

    @Autowired
    private QuestionnaireManager questionnaireManager;
    @Resource
    private PeopleLabelManager peopleLabelManager;


    @Override
    public void add(QuestionnaireDTO dto) {
        Questionnaire questionnaire = new Questionnaire();
        BeanUtils.copy(dto, questionnaire);
        checkInfo(dto);
        questionnaireManager.add(questionnaire);
    }

    @Override
    public void update(QuestionnaireDTO dto) {
        Questionnaire questionnaire = new Questionnaire();
        BeanUtils.copy(dto, questionnaire);
        checkInfo(dto);
        questionnaireManager.update(questionnaire);
    }

    @Override
    public void delete(Long id) {
        questionnaireManager.delete(id);
    }

    @Override
    public QuestionnaireVO one(Long id) {
        QuestionnaireVO quest = questionnaireManager.one(id);
        PeopleLimitEnum peopleLimitEnum = PeopleLimitEnum.getEnum(quest.getPeopleLimit());
        if(peopleLimitEnum == PeopleLimitEnum.LABEL){
            PeopleLabel peopleLabel = peopleLabelManager.get(quest.getLabelId(), true);
            if(peopleLabel != null){
                quest.setLabelName(peopleLabel.getName());
            }
        }
        return quest;
    }

    @Override
    public PageVO<QuestionnaireVO> list(QuestionnaireDTO dto) {
        return questionnaireManager.findListPage(dto);
    }

    @Override
    public void offline(Long id) {
        questionnaireManager.offline(id);
    }


    private Integer checkTime(List<TimeVO> newTimeVOS, List<TimeVO> oldTimeVOS) {
        Integer num = 0;  //没有冲突返回null,有冲突返回冲突的时间段
        for (int i = 0; i < newTimeVOS.size(); i++) {
            Date newStart = newTimeVOS.get(i).getOnlineTime();
            Date newEnd = newTimeVOS.get(i).getOfflineTime();
            for (int j = 0; j < oldTimeVOS.size(); j++) {
                Date oldStart = oldTimeVOS.get(j).getOnlineTime();
                Date oldEnd = oldTimeVOS.get(j).getOfflineTime();

                //compareTo返回结果-1 0 1 表示前者比后者<,=,>关系 ,下面的if判断涉及具体的怎样比较可以自行优化
                if ((oldStart.compareTo(newStart) == -1 && newStart.compareTo(oldEnd) == -1)
                        || (oldStart.compareTo(newEnd) == -1 && newEnd.compareTo(oldEnd) == -1)
                        || (newStart.compareTo(oldStart) == -1 && oldStart.compareTo(newEnd) == -1)   //新加部分
                        || (newStart.compareTo(oldEnd) == -1 && oldEnd.compareTo(newEnd) == -1)   //新加部分
                        || oldEnd.compareTo(newStart) == 0 || oldStart.compareTo(newEnd) == 0
                        || oldEnd.compareTo(newEnd) == 0 || oldStart.compareTo(newStart) == 0) {
                    num = num + 1;
                }
            }
        }
        return num;

    }

    private List<TimeVO> prepareTime(QuestionnaireDTO dto, List<QuestionnaireVO> questionnaireVOS) {
        List<TimeVO> timeVOS = new ArrayList<>();
        if (dto != null) {
            TimeVO timeVO = new TimeVO();
            timeVO.setOnlineTime(dto.getOnlineTime());
            timeVO.setOfflineTime(dto.getOfflineTime());
            timeVOS.add(timeVO);
        }
        if (!CollectionUtils.isEmpty(questionnaireVOS)) {
            questionnaireVOS.stream().forEach(data -> {
                TimeVO timeVO = new TimeVO();
                timeVO.setOnlineTime(data.getOnlineTime());
                timeVO.setOfflineTime(data.getOfflineTime());
                timeVOS.add(timeVO);
            });
        }
        return timeVOS;
    }

    private void checkInfo(QuestionnaireDTO dto) {
        if(dto.getOnlineTime().after(dto.getOfflineTime())){
            throw new BusinessException("上线时间不能晚于下线时间");
        }
        if(Objects.isNull(dto.getPeopleLimit())){
            throw new BusinessException("人群限制不能为空！");
        }
        if(PeopleLimitEnum.LABEL.getCode().equals(dto.getPeopleLimit()) && Objects.isNull(dto.getLabelId())){
            throw new BusinessException("标签不能为空！");
        }

        List<QuestionnaireVO> questionnaireVOS = questionnaireManager.findByExample(dto);
        List<TimeVO> newTimeVOS = prepareTime(dto, null);
        List<TimeVO> oldTimeVOS = prepareTime(null, questionnaireVOS);
        Integer checkTimeNum = checkTime(newTimeVOS, oldTimeVOS);
        if (checkTimeNum > 0 ) {
            throw new BusinessException("当前时间已存在问卷");
        }

    }

    private AdvertisementStatus getStatus(Date onTime, Date offTime) {
        Date nowTime = new Date();
        if (null != onTime && nowTime.before(onTime)) {
            return AdvertisementStatus.STAY_ONLINE;
        } else if (null != onTime && null != offTime && nowTime.after(onTime) && nowTime.before(offTime)) {
            return AdvertisementStatus.UNDER_WAY;
        } else {
            return AdvertisementStatus.FINISHED;
        }

    }
}
