package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonLinkTypeEnum;
import com.hengtiansoft.common.enumeration.CommonPeopleLimitEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.content.entity.dto.*;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.content.entity.po.PageModuleExtraContent;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleContentManager;
import com.hengtiansoft.content.manager.PageModuleExtraContentManager;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: haiyang
 * @Date: 2024-11-04 14:53
 * @Desc:
 */
@Service
public class NewUserAreaOperateService extends AbstractPageModuleCommonTemplate implements PageModuleOperateStrategy, InitializingBean {
    @Resource
    private PageModuleContentManager pageModuleContentManager;
    @Resource
    private PageModuleExtraContentManager pageModuleExtraContentManager;

    @Override
    public void save(PageModuleContentSaveDTO saveDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        validateParam(saveDTO);
        PageModuleContent content;
        if (Objects.nonNull(saveDTO.getId())) {
            content = pageModuleContentManager.getById(saveDTO.getId());
            Asserts.notNull(content, "该新人专区配置不存在");
            this.assembleUpdateContent(content, saveDTO, customerUser);
            pageModuleContentManager.updateById(content);
            pageModuleExtraContentManager.deleteByPageModuleContentId(content.getId(), customerUser.getUserName());
        } else {
            content = assembleContent(saveDTO, customerUser);
            pageModuleContentManager.save(content);
        }
        PageCommonQueryDTO queryDTO = new PageCommonQueryDTO();
        queryDTO.setPageModuleCode(PageModuleCodeEnum.NEW_USER_AREA.getCode());
        queryDTO.setPageCode(PageCodeEnum.NEW_USER_AREA.getCode());
        List<PageModuleContent> greaterSortContents = pageModuleContentManager.getGreaterSortRecords(saveDTO.getSort() ,queryDTO);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<PageModuleContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(content.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setOperator(customerUser.getUserName());
                });
                pageModuleContentManager.updateSort(exceptSelfRecords);
            }
        }
        List<PageModuleExtraContent> extraContentList = assembleContentExtra(content.getId(), saveDTO.getPopImageUrl(), customerUser);
        pageModuleExtraContentManager.save(extraContentList);

    }

    @Override
    public PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO) {
        PageModuleContent content = pageModuleContentManager.getById(queryDTO.getPageModuleContentId());
        if (Objects.isNull(content)) return new PageModuleContentVO();
        List<PageModuleExtraContent> extraContents = pageModuleExtraContentManager.getByPageModuleContentId(queryDTO.getPageModuleContentId());
        return assembleContentVO(content, extraContents);
    }

    @Override
    public PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO) {
        PageHelper.startPage(pageQryDTO.getPageNum(), pageQryDTO.getPageSize());
        List<PageModuleContent> memberRights = pageModuleContentManager.getListByCondition(pageQryDTO);
        if (CollectionUtils.isEmpty(memberRights)) {
            return PageUtils.emptyPage();
        }
        List<Long> pageModuleContentIds = memberRights.stream().map(PageModuleContent::getId).collect(Collectors.toList());
        List<PageModuleExtraContent> extraContents = pageModuleExtraContentManager.getByPageModuleContentIds(pageModuleContentIds);
        Map<Long, PageModuleExtraContent> extraContentMap = extraContents.stream().collect(Collectors.toMap(PageModuleExtraContent::getPageModuleContentId, Function.identity(), (v1,v2) -> v1));
        List<PageModuleContentListVO> listVOS = Lists.newArrayListWithCapacity(memberRights.size());
        memberRights.forEach(e -> {
            PageModuleContentListVO listVO = new PageModuleContentListVO();
            listVO.setId(e.getId());
            listVO.setPageCode(e.getPageCode());
            listVO.setPageModuleCode(e.getModuleCode());
            listVO.setImageUrl(e.getImageUrl());
            listVO.setName(e.getName());
            listVO.setDisplay(e.getDisplay());
            listVO.setStartTime(e.getStartTime());
            listVO.setEndTime(e.getEndTime());
            listVO.setSort(e.getSort());
            listVO.setLinkType(e.getLinkType());
            listVO.setLink(e.getLink());
            listVO.setAppId(e.getAppId());
            listVO.setLabelId(e.getLabelId());
            listVO.setUpdateTime(e.getUpdateTime());

            String labelId = e.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                listVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            } else {
                listVO.setLabelIds(null);
            }

            listVO.setMiniQrcode(e.getMiniQrcode());
            PageModuleExtraContent currentExtraContent = extraContentMap.get(e.getId());
            if (Objects.nonNull(currentExtraContent)) {
                PopImageExtraContent extraContent = JSON.parseObject(currentExtraContent.getExtraContent(), PopImageExtraContent.class);
                listVO.setPopImageUrl(extraContent.getPopImageUrl());
            }
            listVOS.add(listVO);
        });
        return PageUtils.toPageVO(listVOS);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        PageModuleStrategyFactory.register(PageCodeEnum.NEW_USER_AREA.getCode() + "_" + PageModuleCodeEnum.NEW_USER_AREA.getCode(), this);

    }

    @Override
    protected void assembleCustomUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {

    }

    @Override
    protected PageModuleContent assembleCustomContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {
        return content;
    }

    private void validateParam(PageModuleContentSaveDTO saveDTO) {
        String name = saveDTO.getName();
        Integer peopleLimit = saveDTO.getPeopleLimit();
        List<String> gradeList = saveDTO.getGradeList();
        String imageUrl = saveDTO.getImageUrl();
        Integer linkType = saveDTO.getLinkType();
        String link = saveDTO.getLink();
        String appId = saveDTO.getAppId();
        Integer sort = saveDTO.getSort();
        Asserts.notEmpty(name, "名称不能为空");
        Asserts.notEmpty(imageUrl, "专区图片不能为空");
        Asserts.notNull(sort, "排序不能为空");
        if (Objects.nonNull(peopleLimit) && peopleLimit.equals(CommonPeopleLimitEnum.GRADE.getCode())) {
            if (CollectionUtils.isEmpty(gradeList)) {
                throw new BusinessException("会员限制不能为空");
            }
        }
        CommonLinkTypeEnum linkTypeEnum = CommonLinkTypeEnum.getEnum(linkType);
        linkTypeEnum.check(link, appId);
    }

    private List<PageModuleExtraContent> assembleContentExtra(Long pageModuleContentId, String popImageUrl, UserDetailDTO customerUser) {
        List<PageModuleExtraContent> extraContentList = Lists.newArrayListWithExpectedSize(1);
        PageModuleExtraContent content = new PageModuleExtraContent();
        content.setPageModuleContentId(pageModuleContentId);
        content.setOperator(customerUser.getUserName());
        content.setCreateTime(new Date());
        content.setUpdateTime(new Date());
        content.setDelflag(0);
        PopImageExtraContent popContent = new PopImageExtraContent();
        popContent.setPopImageUrl(popImageUrl);
        content.setExtraContent(JSON.toJSONString(popContent));
        extraContentList.add(content);
        return extraContentList;
    }

    private PageModuleContentVO assembleContentVO(PageModuleContent content, List<PageModuleExtraContent> extraContents) {
        PageModuleContentVO contentVO = new PageModuleContentVO();
        contentVO.setId(content.getId());
        contentVO.setPageCode(content.getPageCode());
        contentVO.setPageModuleCode(content.getModuleCode());
        contentVO.setImageUrl(content.getImageUrl());
        contentVO.setName(content.getName());
        contentVO.setPeopleLimit(content.getPeopleLimit());
        contentVO.setFirstJoinDuration(content.getFirstJoinDuration());
        contentVO.setBirthdayUser(content.getBirthdayUser());
        String grade = content.getGrade();
        if (StringUtils.isNotEmpty(grade)) {
            contentVO.setGradeList(Arrays.stream(grade.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        }

        PopImageExtraContent extraContent = JSON.parseObject(extraContents.get(0).getExtraContent(), PopImageExtraContent.class);
        contentVO.setPopImageUrl(extraContent.getPopImageUrl());
        contentVO.setStartTime(content.getStartTime());
        contentVO.setEndTime(content.getEndTime());
        contentVO.setSort(content.getSort());
        contentVO.setLinkType(content.getLinkType());
        contentVO.setLink(content.getLink());
        contentVO.setAppId(content.getAppId());
        String labelId = content.getLabelId();
        if (StringUtils.isNotEmpty(labelId)) {
            contentVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        } else {
            contentVO.setLabelIds(null);
        }
        contentVO.setUpdateTime(content.getUpdateTime());
        contentVO.setMiniQrcode(content.getMiniQrcode());
        return contentVO;

    }

    @Data
    public static class PopImageExtraContent {
        private String popImageUrl;
    }
}
