package com.hengtiansoft.operation.content.service.impl;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.content.entity.dto.IndexModuleCommonConfigDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointMallSaveDTO;
import com.hengtiansoft.content.entity.po.PageModule;
import com.hengtiansoft.content.entity.po.PageModuleItem;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.operation.content.entity.PageModulePointMallVO;
import com.hengtiansoft.content.manager.PageModuleItemManager;
import com.hengtiansoft.content.manager.PageModuleManager;
import com.hengtiansoft.operation.content.service.PageModulePointMallService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.privilege.entity.po.PointMall;
import com.hengtiansoft.privilege.entity.po.PointMallItem;
import com.hengtiansoft.privilege.entity.vo.PointMallVO;
import com.hengtiansoft.privilege.manager.PointMallItemManager;
import com.hengtiansoft.privilege.manager.PointMallManager;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PageModulePointMallServiceImpl implements PageModulePointMallService {

    @Resource
    private PageModuleManager pageModuleManager;
    @Resource
    private PageModuleItemManager pageModuleItemManager;
    @Resource
    private PointMallManager pointMallManager;
    @Resource
    private PointMallItemManager pointMallItemManager;

    private static final String PAGE_CODE = PageCodeEnum.MEMBER_PAGE.getCode();

    private static final String MODULE_CODE = PageModuleCodeEnum.POINT_MALL.getCode();

    @Override
    public PageModulePointMallVO get() {
        PageModule pageModule = pageModuleManager.findByCode(PAGE_CODE, MODULE_CODE);
        if(null == pageModule){
            return new PageModulePointMallVO();
        }
        PageModulePointMallVO vo = new PageModulePointMallVO();
        vo.setConfig(pageModule.getConfig());
        IndexModuleCommonConfigDTO config = JSON.parseObject(vo.getConfig(), IndexModuleCommonConfigDTO.class);
        vo.setCols(config.getCols());
        vo.setRows(config.getRows());

        List<PageModuleItem> pageModuleItemList =  pageModuleItemManager.findByCode(PAGE_CODE, MODULE_CODE);
        List<Long> activityIds = StreamUtils.toList(pageModuleItemList, PageModuleItem::getActivityId);
        List<PointMall> pointMallList = pointMallManager.findByIds(activityIds);
        List<PointMallItem> pointMallItemList = pointMallItemManager.findByPointMallIds(activityIds);
        Map<Long, List<PointMallItem>> pointMallItemMap = StreamUtils.group(pointMallItemList, PointMallItem::getPointMallId);
        List<PointMallVO> pointMallVOList = new ArrayList<>();
        for (PointMall pointMall : pointMallList) {
            PointMallVO pointMallVO = new PointMallVO();
            pointMallVO.setId(pointMall.getId());
            pointMallVO.setName(pointMall.getName());
            pointMallVO.setSwapType(pointMall.getSwapType());
            pointMallVO.setStatus(pointMall.getStatus());
            pointMallVO.setTotalCnt(pointMall.getTotalCnt());
            List<PointMallItem> items = pointMallItemMap.get(pointMall.getId());
            if(CollectionUtils.isEmpty(items)){
                continue;
            }
            PointMallItem item = StreamUtils.getFirst(items);
            pointMallVO.setPoint(item.getPoint());
            pointMallVOList.add(pointMallVO);
        }
        vo.setPointMallList(pointMallVOList);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PageModulePointMallSaveDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        IndexModuleCommonConfigDTO config = new IndexModuleCommonConfigDTO();
        config.setCols(dto.getCols());
        config.setRows(dto.getRows());
        String configStr = JSON.toJSONString(config);
        PageModule update = new PageModule();
        update.setConfig(configStr);
        pageModuleManager.updateByCode(update, PAGE_CODE, MODULE_CODE);
        List<Long> activityIds = dto.getIds();
        if(CollectionUtils.isEmpty(activityIds)){
            pageModuleItemManager.deleteByCode(PAGE_CODE, MODULE_CODE);
            return;
        }

        List<PageModuleItem> pageModuleItemList =  pageModuleItemManager.findByCode(PAGE_CODE, MODULE_CODE);
        List<Long> selectedIds = StreamUtils.toList(pageModuleItemList, PageModuleItem::getActivityId);

        List<Long> addIds = activityIds.stream().filter(e -> !selectedIds.contains(e)).collect(Collectors.toList());
        List<Long> deleteIds = selectedIds.stream().filter(e -> !activityIds.contains(e)).collect(Collectors.toList());

        pageModuleItemManager.deleteByActivityIds(deleteIds);
        if(CollectionUtils.isNotEmpty(dto.getIds())){
            List<Long> pointMallIds = StreamUtils.distinct(addIds, Long::compareTo);
            List<PageModuleItem> list = new ArrayList<>();
            for (Long id: pointMallIds) {
                PageModuleItem pageModuleItem = new PageModuleItem();
                pageModuleItem.setPageCode(PAGE_CODE);
                pageModuleItem.setModuleCode(MODULE_CODE);
                pageModuleItem.setActivityId(id);
                pageModuleItem.setIsHidden(BasicFlagEnum.NO.getKey());
                pageModuleItem.setOperator(detailDTO.getUserName());
                pageModuleItem.setCreateTime(new Date());
                pageModuleItem.setUpdateTime(new Date());
                pageModuleItem.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
                list.add(pageModuleItem);
            }
            if(CollectionUtils.isNotEmpty(list)){
                pageModuleItemManager.batchInsert(list);
            }
        }
    }
}
