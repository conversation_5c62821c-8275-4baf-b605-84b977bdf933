package com.hengtiansoft.operation.content.service.impl;

import cn.hutool.core.lang.Assert;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.ContentTypeEnum;
import com.hengtiansoft.common.enumeration.FileOwnerEnum;
import com.hengtiansoft.common.enumeration.FileTypeEnum;
import com.hengtiansoft.common.enumeration.PeopleLimitEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.content.entity.dto.LoginContentAddDTO;
import com.hengtiansoft.content.entity.dto.LoginContentIssueStatusUpdateDTO;
import com.hengtiansoft.content.entity.dto.LoginContentSearchDTO;
import com.hengtiansoft.content.entity.dto.LoginContentUpdateDTO;
import com.hengtiansoft.content.entity.po.FilesContent;
import com.hengtiansoft.content.entity.po.LoginContent;
import com.hengtiansoft.content.entity.vo.LoginContentListVO;
import com.hengtiansoft.content.entity.vo.LoginContentVO;
import com.hengtiansoft.content.enums.LoginContentIssueStatusEnum;
import com.hengtiansoft.content.enums.LoginContentPushRateEnum;
import com.hengtiansoft.content.manager.FilesContentManager;
import com.hengtiansoft.content.manager.LoginContentManager;
import com.hengtiansoft.operation.content.service.LoginContentService;
import com.hengtiansoft.privilege.enums.ActivityStatusEnum;
import com.hengtiansoft.privilege.manager.PeopleLabelManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;


/**
 * Description: 登录内容管理实现类
 *
 * <AUTHOR>
 */
@Service
public class  LoginContentServiceImpl implements LoginContentService {

    @Autowired
    private LoginContentManager loginContentManager;

    @Autowired
    private FilesContentManager filesContentManager;

    @Autowired
    private PeopleLabelManager peopleLabelManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(LoginContentAddDTO dto) {
        check(dto);
        LoginContent loginContent = new LoginContent();
        BeanUtils.copy(dto, loginContent);
        Date now = new Date();
        loginContent.setIssueStatus(LoginContentIssueStatusEnum.NOT_ISSUE.getCode());
        if (dto.getOfflineTime().before(now)) {
            loginContent.setIssueStatus(ActivityStatusEnum.OFFLINE.getCode());
        } else if (dto.getOnlineTime().after(now)) {
            loginContent.setIssueStatus(ActivityStatusEnum.NOT_ONLINE.getCode());
        } else {
            loginContent.setIssueStatus(ActivityStatusEnum.ONLINE.getCode());
        }
        loginContent.setType(ContentTypeEnum.LOGIN.getCode());
        FilesContent filesContent = new FilesContent();
        BeanUtils.copy(dto, filesContent);
        filesContent.setFileOwner(FileOwnerEnum.LOGIN_CONTENT.getCode());
        filesContent.setFileType(FileTypeEnum.PICTURE.getCode());
        if (CollectionUtils.isNotEmpty(dto.getLabelIds())) {
            loginContent.setLabelId(dto.getLabelIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
//        loginContentManager.checkInfo2(loginContent);
        loginContentManager.add(loginContent);
        filesContent.setOwnerId(loginContent.getId());
        filesContentManager.add(filesContent);

        List<LoginContent> greaterSortContents = loginContentManager.getGreaterSortRecords(dto.getSort(), dto.getShowPageModule());
        if (CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<LoginContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(loginContent.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setUpdateTime(now);
                });
                loginContentManager.updateSort(exceptSelfRecords);
            }
        }
    }

    private static void check(LoginContentAddDTO dto) {
        if(Objects.isNull(dto.getPeopleLimit())){
            throw new BusinessException("人群限制不能为空");
        }

        if(PeopleLimitEnum.LABEL.getCode().equals(dto.getPeopleLimit()) && CollectionUtils.isEmpty(dto.getLabelIds())){
            throw new BusinessException("标签不能为空");
        }
        if(Objects.isNull(dto.getPushRate())){
            throw new BusinessException("推送频率不能为空");
        }
        if(Objects.isNull(dto.getSort())){
            throw new BusinessException("弹窗排序不能为空");
        }
        if(StringUtils.isBlank(dto.getShowPageModule())){
            throw new BusinessException("展示位置不能为空");
        }
        LoginContentPushRateEnum pushRateEnum = LoginContentPushRateEnum.getEnum(dto.getPushRate());
        if(pushRateEnum == LoginContentPushRateEnum.INTERVAL){
            if(Objects.isNull(dto.getIntervalDays())){
                throw new BusinessException("间隔天数不能为空");
            }
        }else{
            if(Objects.nonNull(dto.getIntervalDays())){
                throw new BusinessException("间隔天数不用传值");
            }
        }
        if(Objects.isNull(dto.getOnlineTime()) ||  Objects.isNull(dto.getOfflineTime())){
            throw new BusinessException("请设置上下线时间");
        }
        if(dto.getOnlineTime().getTime() > dto.getOfflineTime().getTime()){
            throw new BusinessException("下线时间不能小于当前时间");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(LoginContentUpdateDTO dto) {
        check(dto);
        // LoginContent loginContent = new LoginContent();
        // BeanUtils.copy(dto, loginContent);
        LoginContent loginContentFromDb = loginContentManager.findById(dto.getId());
        BeanUtils.copy(dto, loginContentFromDb);
        FilesContent filesContent = new FilesContent();
        BeanUtils.copy(dto, filesContent);
        filesContent.setOwnerId(loginContentFromDb.getId());
        filesContent.setFileType(FileTypeEnum.PICTURE.getCode());
        filesContent.setFileOwner(FileOwnerEnum.LOGIN_CONTENT.getCode());
        if (CollectionUtils.isNotEmpty(dto.getLabelIds())) {
            loginContentFromDb.setLabelId(dto.getLabelIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
//        loginContentManager.checkInfo2(loginContentFromDb);
        loginContentManager.updateAll(loginContentFromDb);
        filesContentManager.update(filesContent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        FilesContent filesContent = new FilesContent();
        filesContent.setOwnerId(id);
        filesContent.setFileOwner(FileOwnerEnum.LOGIN_CONTENT.getCode());
        filesContent.setFileType(FileTypeEnum.PICTURE.getCode());
        loginContentManager.delete(id);
        filesContentManager.delete(filesContent);
    }

    @Override
    public void issueStatusUpdate(LoginContentIssueStatusUpdateDTO dto) {
        LoginContent loginContent = new LoginContent();
        BeanUtils.copy(dto, loginContent);
        // loginContent.setType(ContentTypeEnum.LOGIN.getCode());
        // if(loginContent.getIssueStatus().equals(LoginContentIssueStatusEnum.ISSUE.getCode())) {
        //     loginContentManager.checkInfo2(loginContent);
        // }
        // loginContentManager.update(loginContent);
        loginContentManager.issueStatusUpdate(loginContent);
    }

    @Override
    public LoginContentVO one(Long id) {
        LoginContentVO loginContentVO = loginContentManager.one(id);
        PeopleLimitEnum peopleLimitEnum = PeopleLimitEnum.getEnum(loginContentVO.getPeopleLimit());
        if(peopleLimitEnum == PeopleLimitEnum.LABEL){
            String labelId = loginContentVO.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                loginContentVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        }
        return loginContentVO;
    }

    @Override
    public PageVO<LoginContentListVO> list(LoginContentSearchDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<LoginContent> list = loginContentManager.findList(dto);

        if (CollectionUtils.isEmpty(list)) {
            return PageUtils.emptyPage();
        }
        return PageUtils.convert(list, data -> {
            LoginContentListVO loginContentListVO = new LoginContentListVO();
            BeanUtils.copy(data, loginContentListVO);
            String labelId = data.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                loginContentListVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
            return loginContentListVO;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(LoginContentUpdateDTO loginContentUpdateDTO) {
        if(null == loginContentUpdateDTO.getId() || null == loginContentUpdateDTO.getSort()){
            throw new BusinessException("参数缺失");
        }
        LoginContent loginContent = loginContentManager.findById(loginContentUpdateDTO.getId());
        if(null == loginContent){
            throw new BusinessException("弹窗不存在");
        }
        if (loginContentUpdateDTO.getSort().equals(loginContent.getSort())) return;
        LoginContent update = new LoginContent();
        update.setSort(loginContentUpdateDTO.getSort());
        update.setUpdateTime(new Date());
        update.setId(loginContentUpdateDTO.getId());
        loginContentManager.update(update);
        List<LoginContent> greaterSortContents = loginContentManager.getGreaterSortRecords(loginContentUpdateDTO.getSort(), loginContent.getShowPageModule());
        if (CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<LoginContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(loginContent.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setUpdateTime(new Date());
                });
                loginContentManager.updateSort(exceptSelfRecords);
            }
        }
    }

    @Override
    public void end(Long id) {
        LoginContent old = loginContentManager.findById(id);
        Assert.notNull(old, "弹窗不存在");
        if(Objects.equals(ActivityStatusEnum.OFFLINE.getCode(), old.getIssueStatus())){
            throw new BusinessException("该弹窗已经是已结束状态");
        }
        LoginContent update = new LoginContent();
        update.setUpdateTime(new Date());
        update.setIssueStatus(ActivityStatusEnum.OFFLINE.getCode());
        update.setOfflineTime(new Date());
        update.setId(id);
        loginContentManager.update(update);
    }


}
