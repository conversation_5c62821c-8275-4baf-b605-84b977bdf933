package com.hengtiansoft.operation.content.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.HomepageOperationSaveDTO;
import com.hengtiansoft.content.entity.vo.HomepageOperationVO;
import com.hengtiansoft.operation.content.service.HomepageOperationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * Description: 首页运营位管理
 *
 * <AUTHOR>
 * @since 08.04.2020
 */
@RestController
@Api(tags = "首页运营位管理")
@RequestMapping("homepageOperation")
public class HomepageOperationController {

    @Autowired
    private HomepageOperationService homepageOperationService;


    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public Response<Long> add(@ApiParam("广告新增信息") @RequestBody List<HomepageOperationSaveDTO> dtos) {
        homepageOperationService.add(dtos);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/info")
    public Response<List<HomepageOperationVO>> one() {
        return ResponseFactory.success(homepageOperationService.one());
    }


}
