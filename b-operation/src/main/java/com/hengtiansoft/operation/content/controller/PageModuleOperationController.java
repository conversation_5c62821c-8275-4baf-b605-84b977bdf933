package com.hengtiansoft.operation.content.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.*;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.entity.vo.PageModuleVO;
import com.hengtiansoft.operation.content.service.PageModuleContentService;
import com.hengtiansoft.operation.content.service.PageModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-27 10:40
 **/

@RestController
@Api(tags = "页面配置管理")
@RequestMapping("page/module/")
public class PageModuleOperationController {

    @Autowired
    private PageModuleContentService pageModuleContentService;

    @Autowired
    private PageModuleService pageModuleService;

    @ApiOperation(value = "页面配置内容保存")
    @PostMapping("/content/save")
    public Response<Void> save(@Validated @RequestBody PageModuleContentSaveDTO saveDTO) {
        pageModuleContentService.save(saveDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "页面配置内容返回")
    @PostMapping("/content/get")
    public Response<PageModuleContentVO> getContent(@Validated @RequestBody PageModuleContentQueryDTO queryDTO) {
        PageModuleContentVO contentVO = pageModuleContentService.getContent(queryDTO);
        return ResponseFactory.success(contentVO);
    }

    @ApiOperation(value = "页面配置内容分页查询")
    @PostMapping("/content/list")
    public Response<PageVO<PageModuleContentListVO>> getContentPage(@Validated @RequestBody PageModuleContentPageQryDTO pageQryDTO) {
        PageVO<PageModuleContentListVO> pageVO = pageModuleContentService.pageContent(pageQryDTO);
        return ResponseFactory.success(pageVO);
    }

    @ApiOperation(value = "排序")
    @PostMapping("/content/sort")
    public Response<Void> sort(@RequestBody @Validated PageModuleContentSortDTO sortDTO) {
        pageModuleContentService.sort(sortDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "结束")
    @PostMapping("/content/end")
    public Response<Void> end(@RequestBody @Validated PageModuleContentEndDTO endDTO) {
        pageModuleContentService.end(endDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/content/delete")
    public Response<Void> delete(@RequestBody @Validated PageModuleContentEndDTO endDTO) {
        pageModuleContentService.delete(endDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "是否隐藏")
    @PostMapping("/content/display")
    public Response<Void> display(@RequestBody @Validated PageModuleContentDisplayDTO displayDTO) {
        pageModuleContentService.display(displayDTO);
        return ResponseFactory.success();
    }




    @ApiOperation(value = "配置保存")
    @PostMapping("/save")
    public Response<Void> saveModule(@Validated @RequestBody PageModuleSaveDTO saveDTO) {
        pageModuleService.update(saveDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "配置获取")
    @GetMapping("/get")
    public Response<PageModuleVO> getModule(@RequestParam("pageCode") String pageCode, @RequestParam("moduleCode") String moduleCode) {
        PageModuleVO moduleVO = pageModuleService.getModule(pageCode, moduleCode);
        return ResponseFactory.success(moduleVO);
    }

    @ApiOperation(value = "模块全部配置获取")
    @GetMapping("/list")
    public Response<List<PageModuleVO>> getModuleList(@RequestParam("pageCode") String pageCode) {
        List<PageModuleVO> moduleVOList = pageModuleService.getModuleList(pageCode);
        return ResponseFactory.success(moduleVOList);
    }

}
