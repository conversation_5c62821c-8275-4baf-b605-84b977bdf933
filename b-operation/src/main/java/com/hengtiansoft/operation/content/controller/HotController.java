package com.hengtiansoft.operation.content.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.HotAddDTO;
import com.hengtiansoft.content.entity.dto.ProductSearchDTO;
import com.hengtiansoft.content.entity.vo.HotListVO;
import com.hengtiansoft.content.entity.vo.ProductListVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;
import com.hengtiansoft.operation.content.service.HotService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * Description: 热门推荐管理
 *
 * <AUTHOR>
 * @since 19.03.2020
 */
@RestController
@Api(tags = "热门推荐管理")
@RequestMapping("hot")
public class HotController {

    @Autowired
    private HotService hotService;

    @ApiOperation(value = "保存热门商品配置", response = HotAddDTO.class)
    @PostMapping("/save")
    public Response<Long> add(@ApiParam("新增热门商品") @RequestBody DisplayCaseProSaveDTO dto) {
        hotService.add(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "热门商品列表", response = HotListVO.class)
    @GetMapping("/list")
    public Response<List<HotListVO>> list() {
        return ResponseFactory.success(hotService.list());
    }

    @ApiOperation(value = "商品列表", response = ProductListVO.class)
    @PostMapping("product/list")
    public Response<List<ProductListVO>> productList(@RequestBody ProductSearchDTO dto) {
        return ResponseFactory.success(hotService.productList(dto));
    }


}
