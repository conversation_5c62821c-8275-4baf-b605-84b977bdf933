package com.hengtiansoft.operation.content.entity;

import com.hengtiansoft.privilege.entity.vo.PointMallVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("页面配置模块表")
public class PageModulePointMallVO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 会员页-memberPage
     */
    @ApiModelProperty("会员页-memberPage")
    private String pageCode;

    /**
     * 模块code: carousel,gold,func
     */
    @ApiModelProperty("模块code: carousel,gold,func")
    private String moduleCode;

    /**
     * 模块类型：1自定义，2固定，3顶部
     */
    @ApiModelProperty("模块类型：1自定义，2固定，3顶部")
    private Integer moduleType;

    /**
     * 模块名称
     */
    @ApiModelProperty("模块名称")
    private String moduleName;

    /**
     * 模式说明
     */
    @Column(name = "`module_desc`")
    @ApiModelProperty("模式说明")
    private String moduleDesc;

    /**
     * 是否显示：0否1是
     */
    @Column(name = "`display`")
    @ApiModelProperty("是否显示：0否1是")
    private Integer display;

    /**
     * 排序
     */
    @Column(name = "`sort`")
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 图片
     */
    @Column(name = "`image_url`")
    @ApiModelProperty("图片")
    private String imageUrl;

    /**
     * 操作人
     */
    @Column(name = "`operator`")
    @ApiModelProperty("操作人")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "`create_time`")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "`update_time`")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    @ApiModelProperty("删除标记：0-未删除，1-已删除")
    private Integer delflag;

    /**
     * 个性化配置
     */
    @ApiModelProperty("个性化配置")
    private String config;

    @ApiModelProperty("行数")
    private Integer rows;

    @ApiModelProperty("列数")
    private Integer cols;

    private List<PointMallVO> pointMallList;

    private static final long serialVersionUID = 1L;
}