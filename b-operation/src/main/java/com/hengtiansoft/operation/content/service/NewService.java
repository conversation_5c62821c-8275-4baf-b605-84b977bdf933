package com.hengtiansoft.operation.content.service;

import com.hengtiansoft.content.entity.dto.ProductSearchDTO;
import com.hengtiansoft.content.entity.vo.NewListVO;
import com.hengtiansoft.content.entity.vo.ProductListVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;

import java.util.List;

/**
 * Description: 新品上新Service
 *
 * <AUTHOR>
 * @since 08.04.2020
 */
public interface NewService {


    List<NewListVO> list();

    List<ProductListVO> productList(ProductSearchDTO dto);

    void add(DisplayCaseProSaveDTO dto);

    List<NewListVO> resetList();

}
