package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonLinkTypeEnum;
import com.hengtiansoft.common.enumeration.CommonPeopleLimitEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.content.entity.dto.*;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.content.entity.po.PageModuleExtraContent;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleContentManager;
import com.hengtiansoft.content.manager.PageModuleExtraContentManager;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-27 11:18
 **/
@Service
public class MemberRightsOperateService extends AbstractPageModuleCommonTemplate implements PageModuleOperateStrategy, InitializingBean {

    @Resource
    private PageModuleContentManager pageModuleContentManager;

    @Resource
    private PageModuleExtraContentManager pageModuleExtraContentManager;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(PageModuleContentSaveDTO saveDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        validateParam(saveDTO);
        PageModuleContent content;
        if (Objects.nonNull(saveDTO.getId())) {
            content = pageModuleContentManager.getById(saveDTO.getId());
            Asserts.notNull(content, "该会员权益不存在");
            assembleUpdateContent(content, saveDTO, customerUser);
            pageModuleExtraContentManager.deleteByPageModuleContentId(content.getId(), customerUser.getUserName());
            pageModuleContentManager.updateById(content);
        } else {
            content = this.assembleContent(saveDTO, customerUser);
            pageModuleContentManager.save(content);
        }
        // 更新sort
        PageCommonQueryDTO queryDTO = new PageCommonQueryDTO();
        queryDTO.setPageModuleCode(PageModuleCodeEnum.MEMBER_RIGHTS.getCode());
        queryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        List<PageModuleContent> greaterSortContents = pageModuleContentManager.getGreaterSortRecords(saveDTO.getSort() ,queryDTO);
        if (CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<PageModuleContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(content.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setOperator(customerUser.getUserName());
                    e.setUpdateTime(new Date());
                });
                pageModuleContentManager.updateSort(exceptSelfRecords);
            }
        }
        // 保存内页配置
        List<PageModuleExtraContent> extraContentList = assembleContentExtra(content.getId(), saveDTO.getExtraContents(), customerUser);
        pageModuleExtraContentManager.save(extraContentList);
    }

    @Override
    public PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO) {
        PageModuleContent content = pageModuleContentManager.getById(queryDTO.getPageModuleContentId());
        if (Objects.isNull(content)) return new PageModuleContentVO();
        List<PageModuleExtraContent> extraContents = pageModuleExtraContentManager.getByPageModuleContentId(queryDTO.getPageModuleContentId());
        return assembleContentVO(content, extraContents);
    }

    @Override
    public PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO) {
        Asserts.notBlank(pageQryDTO.getGrade(), "会员等级参数不能为空");
        List<PageModuleContent> memberRights = pageModuleContentManager.getListByCondition(pageQryDTO);
        if (CollectionUtils.isEmpty(memberRights)) {
            return PageUtils.emptyPage();
        }
        List<Long> pageModuleContentIds = memberRights.stream().map(PageModuleContent::getId).collect(Collectors.toList());
        List<PageModuleExtraContent> extraContents = pageModuleExtraContentManager.getByPageModuleContentIds(pageModuleContentIds);
        Map<Long, List<PageModuleExtraContent>> extraContentMap = extraContents.stream().collect(Collectors.groupingBy(PageModuleExtraContent::getPageModuleContentId));
        List<PageModuleContentListVO> listVOS = Lists.newArrayListWithCapacity(memberRights.size());
        memberRights.forEach(e -> {
            PageModuleContentListVO listVO = new PageModuleContentListVO();
            listVO.setId(e.getId());
            listVO.setPageCode(e.getPageCode());
            listVO.setPageModuleCode(e.getModuleCode());
            listVO.setImageUrl(e.getImageUrl());
            listVO.setName(e.getName());
            listVO.setDisplay(e.getDisplay());
            listVO.setStartTime(e.getStartTime());
            listVO.setEndTime(e.getEndTime());
            listVO.setSort(e.getSort());
            listVO.setLinkType(e.getLinkType());
            listVO.setLink(e.getLink());
            listVO.setAppId(e.getAppId());
            listVO.setLabelId(e.getLabelId());

            String labelId = e.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                listVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            } else {
                listVO.setLabelIds(null);
            }
            
            listVO.setMiniQrcode(e.getMiniQrcode());
            List<PageModuleExtraContent> currentExtraContent = extraContentMap.get(e.getId());
            if (CollectionUtils.isNotEmpty(currentExtraContent)) {
                for (PageModuleExtraContent el : currentExtraContent) {
                    ExtraInnerPageDTO extraInnerPageDTO = JSON.parseObject(el.getExtraContent(), ExtraInnerPageDTO.class);
                    if (extraInnerPageDTO.getGrade().equals(pageQryDTO.getGrade())) {
                        listVO.setSubTitle(extraInnerPageDTO.getSubTitle());
                        listVO.setInnerPageUrl(extraInnerPageDTO.getInnerPageUrl());
                        break;
                    }
                }
            }
            listVOS.add(listVO);
        });
        return PageUtils.toPageVO(listVOS);
    }


    private PageModuleContentVO assembleContentVO(PageModuleContent content, List<PageModuleExtraContent> extraContents) {
        PageModuleContentVO contentVO = new PageModuleContentVO();
        contentVO.setId(content.getId());
        contentVO.setPageCode(content.getPageCode());
        contentVO.setPageModuleCode(content.getModuleCode());
        contentVO.setImageUrl(content.getImageUrl());
        contentVO.setName(content.getName());
        contentVO.setPeopleLimit(content.getPeopleLimit());
        contentVO.setFirstJoinDuration(content.getFirstJoinDuration());
        contentVO.setBirthdayUser(content.getBirthdayUser());
        String grade = content.getGrade();
        if (StringUtils.isNotEmpty(grade)) {
            contentVO.setGradeList(Arrays.stream(grade.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        }
        List<PageModuleContentSaveDTO.ExtraContent> extraContentVOS = Lists.newArrayListWithExpectedSize(extraContents.size());
        extraContents.forEach(e -> {
            PageModuleContentSaveDTO.ExtraContent extraContent = JSON.parseObject(e.getExtraContent(), PageModuleContentSaveDTO.ExtraContent.class);
            extraContentVOS.add(extraContent);
        });
        contentVO.setExtraContents(extraContentVOS);
        contentVO.setStartTime(content.getStartTime());
        contentVO.setEndTime(content.getEndTime());
        contentVO.setSort(content.getSort());
        contentVO.setLinkType(content.getLinkType());
        contentVO.setLink(content.getLink());
        contentVO.setAppId(content.getAppId());
        String labelId = content.getLabelId();
        if (StringUtils.isNotEmpty(labelId)) {
            contentVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        } else {
            contentVO.setLabelIds(null);
        }
        contentVO.setUpdateTime(content.getUpdateTime());
        contentVO.setMiniQrcode(content.getMiniQrcode());
        return contentVO;

    }

    private List<PageModuleExtraContent> assembleContentExtra(Long pageModuleContentId, List<PageModuleContentSaveDTO.ExtraContent> extraContents, UserDetailDTO customerUser) {
        List<PageModuleExtraContent> extraContentList = Lists.newArrayListWithExpectedSize(extraContents.size());
        extraContents.forEach(e -> {
            PageModuleExtraContent content = new PageModuleExtraContent();
            content.setPageModuleContentId(pageModuleContentId);
            content.setOperator(customerUser.getUserName());
            content.setCreateTime(new Date());
            content.setUpdateTime(new Date());
            content.setDelflag(0);
            content.setExtraContent(JSON.toJSONString(e));
            extraContentList.add(content);
        });
        return extraContentList;
    }


    private void validateParam(PageModuleContentSaveDTO saveDTO) {
        String name = saveDTO.getName();
        Integer peopleLimit = saveDTO.getPeopleLimit();
        List<String> gradeList = saveDTO.getGradeList();
        String imageUrl = saveDTO.getImageUrl();
        Integer linkType = saveDTO.getLinkType();
        String link = saveDTO.getLink();
        String appId = saveDTO.getAppId();
        Integer sort = saveDTO.getSort();
        Asserts.notEmpty(name, "权益名称不能为空");
        if (Objects.isNull(peopleLimit) || !peopleLimit.equals(CommonPeopleLimitEnum.GRADE.getCode())) {
            throw new BusinessException("人群限制不能为空或人群限制为非会员");
        }
        Asserts.notEmpty(imageUrl, "权益图片不能为空");

        List<PageModuleContentSaveDTO.ExtraContent> extraContents = saveDTO.getExtraContents();
        if (CollectionUtils.isEmpty(extraContents) || extraContents.size() != gradeList.size()) {
            throw new BusinessException("内页图内容为空或与勾选会员限制长度不一致");
        }
        boolean emptyExistFlag = extraContents.stream().anyMatch(e -> Objects.isNull(e.getGrade()) || e.getSubTitle().isEmpty() || e.getInnerPageUrl().isEmpty());
        if (emptyExistFlag) {
            throw new BusinessException("内页图配置存在配置项为空");
        }
        if (Objects.isNull(sort)) {
            throw new BusinessException("排序不能为空");
        }
        CommonLinkTypeEnum linkTypeEnum = CommonLinkTypeEnum.getEnum(linkType);
        linkTypeEnum.check(link, appId);
        if(linkTypeEnum == CommonLinkTypeEnum.WEI){
            if(StringUtils.isBlank(saveDTO.getMiniQrcode())){
                throw new BusinessException("跳珠类型为微页面时请上传小程序太阳码");
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        PageModuleStrategyFactory.register(PageCodeEnum.MEMBER_PAGE.getCode() + "_" + PageModuleCodeEnum.MEMBER_RIGHTS.getCode(), this);
    }

    @Override
    protected void assembleCustomUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {
        return;
    }

    @Override
    protected PageModuleContent assembleCustomContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {
        return content;
    }
}
