package com.hengtiansoft.operation.content.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.ProductSearchDTO;
import com.hengtiansoft.content.entity.vo.NewListVO;
import com.hengtiansoft.content.entity.vo.ProductListVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;
import com.hengtiansoft.operation.content.service.NewService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Description: 新品上新管理
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
@RestController
@Api(tags = "新品上新管理")
@RequestMapping("new")
public class NewProController {

    @Autowired
    NewService newService;

    @ApiOperation(value = "新品上新列表", response = NewListVO.class)
    @GetMapping("/list")
    public Response<List<NewListVO>> list() {
        return ResponseFactory.success(newService.list());
    }

    @ApiOperation(value = "商品列表", response = ProductListVO.class)
    @PostMapping("product/list")
    public Response<List<ProductListVO>> productList(@RequestBody ProductSearchDTO dto) {
        return ResponseFactory.success(newService.productList(dto));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public Response<Long> save(@RequestBody @Validated DisplayCaseProSaveDTO dto) {
        newService.add(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "重置后的新品上新列表", response = NewListVO.class)
    @GetMapping("reset/list")
    public Response<List<NewListVO>> resetList() {
        return ResponseFactory.success(newService.resetList());
    }









}
