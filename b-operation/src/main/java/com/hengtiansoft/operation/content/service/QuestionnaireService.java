package com.hengtiansoft.operation.content.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.AdvertisementAddDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementSearchDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementUpdateDTO;
import com.hengtiansoft.content.entity.dto.QuestionnaireDTO;
import com.hengtiansoft.content.entity.vo.AdvertisementVO;
import com.hengtiansoft.content.entity.vo.QuestionnaireVO;


public interface QuestionnaireService {

    void add (QuestionnaireDTO dto);

    void update(QuestionnaireDTO dto);

    void delete(Long id);

    QuestionnaireVO one(Long id);

    PageVO<QuestionnaireVO> list(QuestionnaireDTO dto);

    void offline(Long id);
}
