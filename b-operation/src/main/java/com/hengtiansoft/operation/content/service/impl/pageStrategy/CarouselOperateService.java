package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonLinkTypeEnum;
import com.hengtiansoft.common.enumeration.CommonPeopleLimitEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.content.entity.dto.PageCommonQueryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentPageQryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentQueryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentSaveDTO;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleContentManager;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-01 15:14
 **/
@Service
public class CarouselOperateService extends AbstractPageModuleCommonTemplate implements PageModuleOperateStrategy, InitializingBean {

    @Resource
    private PageModuleContentManager pageModuleContentManager;
    @Override
    public void save(PageModuleContentSaveDTO saveDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        validateParam(saveDTO);
        PageModuleContent content;
        if (Objects.nonNull(saveDTO.getId())) {
            content = pageModuleContentManager.getById(saveDTO.getId());
            Asserts.notNull(content, "该轮播配置不存在");
            assembleUpdateContent(content, saveDTO, customerUser);
            pageModuleContentManager.updateById(content);
        } else {
            content = this.assembleContent(saveDTO, customerUser);
            pageModuleContentManager.save(content);
        }
        // 更新sort
        PageCommonQueryDTO queryDTO = new PageCommonQueryDTO();
        queryDTO.setPageModuleCode(PageModuleCodeEnum.CAROUSEL.getCode());
        queryDTO.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        List<PageModuleContent> greaterSortContents = pageModuleContentManager.getGreaterSortRecords(saveDTO.getSort() ,queryDTO);
        if (CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<PageModuleContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(content.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setOperator(customerUser.getUserName());
                    e.setUpdateTime(new Date());
                });
                pageModuleContentManager.updateSort(exceptSelfRecords);
            }
        }
    }

    @Override
    public PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO) {
        PageModuleContent content = pageModuleContentManager.getById(queryDTO.getPageModuleContentId());
        Asserts.notNull(content, "该轮播图不存在");
        PageModuleContentVO contentVO = new PageModuleContentVO();
        contentVO.setId(content.getId());
        contentVO.setPageCode(content.getPageCode());
        contentVO.setPageModuleCode(content.getModuleCode());
        contentVO.setImageUrl(content.getImageUrl());
        contentVO.setName(content.getName());
        contentVO.setStyle(content.getStyle());
        contentVO.setPeopleLimit(content.getPeopleLimit());
        contentVO.setFirstJoinDuration(content.getFirstJoinDuration());
        contentVO.setBirthdayUser(content.getBirthdayUser());
        String grade = content.getGrade();
        if (StringUtils.isNotEmpty(grade)) {
            contentVO.setGradeList(Arrays.stream(grade.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        }
        contentVO.setStartTime(content.getStartTime());
        contentVO.setEndTime(content.getEndTime());
        contentVO.setSort(content.getSort());
        contentVO.setLinkType(content.getLinkType());
        contentVO.setLink(content.getLink());
        contentVO.setAppId(content.getAppId());
        String labelId = content.getLabelId();
        if (StringUtils.isNotEmpty(labelId)) {
            contentVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        } else {
            contentVO.setLabelIds(null);
        }
        contentVO.setMiniQrcode(content.getMiniQrcode());
        return contentVO;
    }

    @Override
    public PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO) {
        PageHelper.startPage(pageQryDTO.getPageNum(), pageQryDTO.getPageSize());
        List<PageModuleContent> contents = pageModuleContentManager.getListByCondition(pageQryDTO);
        if (CollectionUtils.isEmpty(contents)) {
            return PageUtils.emptyPage();
        }
        return PageUtils.convert(contents, data -> {
            PageModuleContentListVO pageModuleContentListVO = new PageModuleContentListVO();
            pageModuleContentListVO.setId(data.getId());
            pageModuleContentListVO.setPageCode(data.getPageCode());
            pageModuleContentListVO.setPageModuleCode(data.getModuleCode());
            pageModuleContentListVO.setImageUrl(data.getImageUrl());
            pageModuleContentListVO.setName(data.getName());
            pageModuleContentListVO.setStyle(data.getStyle());
            pageModuleContentListVO.setStartTime(data.getStartTime());
            pageModuleContentListVO.setEndTime(data.getEndTime());
            pageModuleContentListVO.setSort(data.getSort());
            pageModuleContentListVO.setLinkType(data.getLinkType());
            pageModuleContentListVO.setLink(data.getLink());
            pageModuleContentListVO.setAppId(data.getAppId());
            pageModuleContentListVO.setLabelId(data.getLabelId());

            String labelId = data.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                pageModuleContentListVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            } else {
                pageModuleContentListVO.setLabelIds(null);
            }

            pageModuleContentListVO.setMiniQrcode(data.getMiniQrcode());
            pageModuleContentListVO.setUpdateTime(data.getUpdateTime());
            return pageModuleContentListVO;
        });
    }


    @Override
    protected void assembleCustomUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {
        content.setStyle(saveDTO.getStyle());
    }

    @Override
    protected PageModuleContent assembleCustomContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {
        content.setStyle(saveDTO.getStyle());
        return content;
    }

    private void validateParam(PageModuleContentSaveDTO saveDTO) {
        String name = saveDTO.getName();
        Integer peopleLimit = saveDTO.getPeopleLimit();
        List<String> gradeList = saveDTO.getGradeList();
        String imageUrl = saveDTO.getImageUrl();
        Integer linkType = saveDTO.getLinkType();
        String link = saveDTO.getLink();
        String appId = saveDTO.getAppId();
        Integer sort = saveDTO.getSort();
        Asserts.notEmpty(name, "轮播图名称不能为空");
        Asserts.notEmpty(imageUrl, "轮播图不能为空");
        Asserts.notNull(sort, "排序不能为空");
        Asserts.notNull(saveDTO.getStyle(), "样式不能为空");
        if (Objects.nonNull(peopleLimit) && peopleLimit.equals(CommonPeopleLimitEnum.GRADE.getCode())) {
            if (CollectionUtils.isEmpty(gradeList)) {
                throw new BusinessException("会员限制不能为空");
            }
        }
        CommonLinkTypeEnum linkTypeEnum = CommonLinkTypeEnum.getEnum(linkType);
        linkTypeEnum.check(link, appId);
        if(linkTypeEnum == CommonLinkTypeEnum.WEI){
            if(StringUtils.isBlank(saveDTO.getMiniQrcode())){
                throw new BusinessException("跳珠类型为微页面时请上传小程序太阳码");
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        PageModuleStrategyFactory.register(PageCodeEnum.MEMBER_PAGE.getCode() + "_" + PageModuleCodeEnum.CAROUSEL.getCode(), this);
    }
}
