package com.hengtiansoft.operation.content.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.QuestionnaireDTO;
import com.hengtiansoft.content.entity.vo.QuestionnaireVO;
import com.hengtiansoft.operation.content.service.QuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



@RestController
@Api(tags = "问卷管理")
@RequestMapping("question")
public class QuestionnaireController {

    @Autowired
    private QuestionnaireService questionnaireService;

    @ApiOperation(value = "新增问卷")
    @PostMapping("/add")
    public Response<Long> add(@ApiParam("问卷新增信息") @RequestBody @Validated QuestionnaireDTO questionnaireDTO) {
        questionnaireService.add(questionnaireDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "修改问卷")
    @PostMapping("/update")
    public Response<Object> update(@ApiParam("问卷更新信息") @RequestBody @Validated QuestionnaireDTO questionnaireDTO) {
        questionnaireService.update(questionnaireDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除问卷")
    @DeleteMapping("/delete")
    public Response<Object> delete(@ApiParam("问卷ID") @RequestParam Long id) {
        questionnaireService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "问卷详情")
    @GetMapping("/info")
    public Response<QuestionnaireVO> one(@ApiParam("问卷ID") @RequestParam Long id) {
        return ResponseFactory.success(questionnaireService.one(id));
    }

    @ApiOperation(value = "问卷列表")
    @PostMapping("/list")
    public Response<PageVO<QuestionnaireVO>> list(@ApiParam("问卷查询信息") @RequestBody QuestionnaireDTO questionnaireDTO) {
        return ResponseFactory.success(questionnaireService.list(questionnaireDTO));
    }

    @ApiOperation(value = "下线")
    @GetMapping("/offline")
    public Response<Object> offline(@RequestParam Long id) {
        questionnaireService.offline(id);
        return ResponseFactory.success();
    }
}
