package com.hengtiansoft.operation.content.service.impl;

import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.*;
import com.hengtiansoft.content.entity.po.PageModule;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleContentManager;
import com.hengtiansoft.operation.content.service.PageModuleContentService;
import com.hengtiansoft.operation.content.service.impl.pageStrategy.PageModuleOperateStrategy;
import com.hengtiansoft.operation.content.service.impl.pageStrategy.PageModuleStrategyFactory;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-27 15:29
 **/
@Service
public class PageModuleContentServiceImpl implements PageModuleContentService {

    @Resource
    private PageModuleContentManager pageModuleContentManager;

    @Override
    public void save(PageModuleContentSaveDTO saveDTO) {
        String pageCode = saveDTO.getPageCode();
        String moduleCode = saveDTO.getModuleCode();
        String key = pageCode + "_" + moduleCode;
        PageModuleOperateStrategy pageModuleService = PageModuleStrategyFactory.getByPageModuleCode(key);
        pageModuleService.save(saveDTO);
    }

    @Override
    public PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO) {
        String pageCode = queryDTO.getPageCode();
        String moduleCode = queryDTO.getModuleCode();
        String key = pageCode + "_" + moduleCode;
        PageModuleOperateStrategy pageModuleService = PageModuleStrategyFactory.getByPageModuleCode(key);
        return pageModuleService.getContent(queryDTO);
    }

    @Override
    public PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO) {
        String pageCode = pageQryDTO.getPageCode();
        String moduleCode = pageQryDTO.getModuleCode();
        String key = pageCode + "_" + moduleCode;
        PageModuleOperateStrategy pageModuleService = PageModuleStrategyFactory.getByPageModuleCode(key);
        return pageModuleService.pageContent(pageQryDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(PageModuleContentSortDTO sortDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PageModuleContent content = pageModuleContentManager.getById(sortDTO.getId());
        if (sortDTO.getSort().equals(content.getSort())) return;
        content.setSort(sortDTO.getSort());
        content.setUpdateTime(new Date());
        content.setOperator(customerUser.getUserName());
        pageModuleContentManager.updateById(content);
        PageCommonQueryDTO queryDTO = new PageCommonQueryDTO();
        queryDTO.setPageModuleCode(content.getModuleCode());
        queryDTO.setPageCode(content.getPageCode());
        List<PageModuleContent> greaterSortContents = pageModuleContentManager.getGreaterSortRecords(sortDTO.getSort() ,queryDTO);
        if (CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<PageModuleContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(content.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setOperator(customerUser.getUserName());
                    e.setUpdateTime(new Date());
                });
                pageModuleContentManager.updateSort(exceptSelfRecords);
            }
        }
    }

    @Override
    public void end(PageModuleContentEndDTO endDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PageModuleContent content = pageModuleContentManager.getById(endDTO.getId());
        Asserts.notNull(content, "该配置不存在");
        Date date = new Date();
        if (Objects.isNull(content.getStartTime())) {
            content.setStartTime(content.getCreateTime());
        }
        content.setOperator(customerUser.getUserName());
        content.setUpdateTime(date);
        content.setEndTime(date);
        pageModuleContentManager.updateById(content);
    }

    @Override
    public void delete(PageModuleContentEndDTO endDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PageModuleContent content = pageModuleContentManager.getById(endDTO.getId());
        Asserts.notNull(content, "该配置不存在");
        Date endTime = content.getEndTime();
        Date now = new Date();
        if (Objects.nonNull(endTime) && endTime.before(now)) {
            content.setDelflag(1);
            content.setUpdateTime(now);
            content.setOperator(customerUser.getUserName());
            pageModuleContentManager.updateById(content);
        } else {
            throw new BusinessException("非结束状态无法删除");
        }
    }

    @Override
    public void display(PageModuleContentDisplayDTO displayDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        PageModuleContent content = pageModuleContentManager.getById(displayDTO.getId());
        Asserts.notNull(content, "该配置不存在");
        Integer dbDisplay = content.getDisplay();
        Date now = new Date();
        if (!dbDisplay.equals(displayDTO.getDisplay())) {
            content.setDisplay(displayDTO.getDisplay());
            content.setUpdateTime(now);
            content.setOperator(customerUser.getUserName());
            pageModuleContentManager.updateById(content);
        }
    }
}
