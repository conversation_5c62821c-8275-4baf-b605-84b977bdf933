package com.hengtiansoft.operation.content.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.AdvertisementAddDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementSearchDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementUpdateDTO;
import com.hengtiansoft.content.entity.vo.AdvertisementVO;

/**
 * Description: 广告位管理Service
 *
 * <AUTHOR>
 * @since 27.03.2020
 */
public interface AdvertisementService {

    void add (AdvertisementAddDTO dto);

    void update(AdvertisementUpdateDTO dto);

    void delete(Long id);

    AdvertisementVO one(Long id);

    PageVO<AdvertisementVO> list(AdvertisementSearchDTO dto);




}
