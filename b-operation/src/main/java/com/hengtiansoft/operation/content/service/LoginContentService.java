package com.hengtiansoft.operation.content.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.LoginContentAddDTO;
import com.hengtiansoft.content.entity.dto.LoginContentIssueStatusUpdateDTO;
import com.hengtiansoft.content.entity.dto.LoginContentSearchDTO;
import com.hengtiansoft.content.entity.dto.LoginContentUpdateDTO;
import com.hengtiansoft.content.entity.vo.LoginContentListVO;
import com.hengtiansoft.content.entity.vo.LoginContentVO;

/**
 * Description: 登录内容管理
 *
 * <AUTHOR>
 */
public interface LoginContentService {

    void insert(LoginContentAddDTO dto);

    void update(LoginContentUpdateDTO dto);

    void delete(Long id);

    void issueStatusUpdate(LoginContentIssueStatusUpdateDTO dto);

    LoginContentVO one(Long id);

    PageVO<LoginContentListVO> list(LoginContentSearchDTO dto);

    void sort(LoginContentUpdateDTO loginContentUpdateDTO);

    void end(Long id);
}
