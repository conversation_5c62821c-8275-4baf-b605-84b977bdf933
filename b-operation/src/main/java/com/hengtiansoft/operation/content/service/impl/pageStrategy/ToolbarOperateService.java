package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonLinkTypeEnum;
import com.hengtiansoft.common.enumeration.CommonPeopleLimitEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.content.entity.dto.PageCommonQueryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentPageQryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentQueryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentSaveDTO;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleContentManager;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: haiyang
 * @Date: 2024-10-16 16:25
 * @Desc:
 */
@Service
public class ToolbarOperateService extends AbstractPageModuleCommonTemplate implements PageModuleOperateStrategy, InitializingBean {

    @Resource
    private PageModuleContentManager pageModuleContentManager;

    @Override
    protected void assembleCustomUpdateContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {

    }

    @Override
    protected PageModuleContent assembleCustomContent(PageModuleContent content, PageModuleContentSaveDTO saveDTO) {
        return content;
    }

    @Override
    public void save(PageModuleContentSaveDTO saveDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        validateParam(saveDTO);
        PageModuleContent content;
        if (Objects.nonNull(saveDTO.getId())) {
            content = pageModuleContentManager.getById(saveDTO.getId());
            Asserts.notNull(content, "该广告位配置不存在");
            this.assembleUpdateContent(content, saveDTO, customerUser);
            pageModuleContentManager.updateById(content);
        } else {
            content = assembleContent(saveDTO, customerUser);
            pageModuleContentManager.save(content);
        }
        PageCommonQueryDTO queryDTO = new PageCommonQueryDTO();
        queryDTO.setPageModuleCode(PageModuleCodeEnum.TOOLBAR.getCode());
        queryDTO.setPageCode(PageCodeEnum.PERSONAL_CENTER.getCode());
        List<PageModuleContent> greaterSortContents = pageModuleContentManager.getGreaterSortRecords(saveDTO.getSort() ,queryDTO);
        if (CollectionUtils.isNotEmpty(greaterSortContents)) {
            List<PageModuleContent> exceptSelfRecords = greaterSortContents.stream().filter(e -> !e.getId().equals(content.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exceptSelfRecords)) {
                exceptSelfRecords.forEach(e -> {
                    e.setSort(e.getSort() + 1);
                    e.setOperator(customerUser.getUserName());
                    e.setUpdateTime(new Date());
                });
                pageModuleContentManager.updateSort(exceptSelfRecords);
            }
        }
    }

    @Override
    public PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO) {
        PageModuleContent content = pageModuleContentManager.getById(queryDTO.getPageModuleContentId());
        Asserts.notNull(content, "该工具栏配置不存在");
        PageModuleContentVO contentVO = new PageModuleContentVO();
        contentVO.setId(content.getId());
        contentVO.setPageCode(content.getPageCode());
        contentVO.setPageModuleCode(content.getModuleCode());
        contentVO.setImageUrl(content.getImageUrl());
        contentVO.setName(content.getName());
        contentVO.setPeopleLimit(content.getPeopleLimit());
        String grade = content.getGrade();
        if (StringUtils.isNotEmpty(grade)) {
            contentVO.setGradeList(Arrays.stream(grade.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        }
        contentVO.setStartTime(content.getStartTime());
        contentVO.setEndTime(content.getEndTime());
        contentVO.setSort(content.getSort());
        contentVO.setLinkType(content.getLinkType());
        contentVO.setLink(content.getLink());
        contentVO.setAppId(content.getAppId());
        String labelId = content.getLabelId();
        if (StringUtils.isNotEmpty(labelId)) {
            contentVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        } else {
            contentVO.setLabelIds(null);
        }
        contentVO.setMiniQrcode(content.getMiniQrcode());
        return contentVO;
    }

    @Override
    public PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO) {
        PageHelper.startPage(pageQryDTO.getPageNum(), pageQryDTO.getPageSize());
        List<PageModuleContent> contents = pageModuleContentManager.getListByCondition(pageQryDTO);
        if (CollectionUtils.isEmpty(contents)) {
            return PageUtils.emptyPage();
        }
        return PageUtils.convert(contents, data -> {
            PageModuleContentListVO pageModuleContentListVO = new PageModuleContentListVO();
            pageModuleContentListVO.setId(data.getId());
            pageModuleContentListVO.setPageCode(data.getPageCode());
            pageModuleContentListVO.setPageModuleCode(data.getModuleCode());
            pageModuleContentListVO.setImageUrl(data.getImageUrl());
            pageModuleContentListVO.setName(data.getName());
            pageModuleContentListVO.setStartTime(data.getStartTime());
            pageModuleContentListVO.setEndTime(data.getEndTime());
            pageModuleContentListVO.setSort(data.getSort());
            pageModuleContentListVO.setLinkType(data.getLinkType());
            pageModuleContentListVO.setLink(data.getLink());
            pageModuleContentListVO.setAppId(data.getAppId());
            pageModuleContentListVO.setLabelId(data.getLabelId());
            String labelId = data.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                pageModuleContentListVO.setLabelIds(Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            } else {
                pageModuleContentListVO.setLabelIds(null);
            }
            pageModuleContentListVO.setUpdateTime(data.getUpdateTime());
            pageModuleContentListVO.setMiniQrcode(data.getMiniQrcode());
            return pageModuleContentListVO;
        });
    }

    private void validateParam(PageModuleContentSaveDTO saveDTO) {
        String name = saveDTO.getName();
        Integer peopleLimit = saveDTO.getPeopleLimit();
        List<String> gradeList = saveDTO.getGradeList();
        String imageUrl = saveDTO.getImageUrl();
        Integer linkType = saveDTO.getLinkType();
        String link = saveDTO.getLink();
        String appId = saveDTO.getAppId();
        Integer sort = saveDTO.getSort();
        Asserts.notEmpty(name, "工具栏名称不能为空");
        Asserts.notEmpty(imageUrl, "工具栏icon不能为空");
        Asserts.notNull(sort, "排序不能为空");
        if (Objects.nonNull(peopleLimit) && peopleLimit.equals(CommonPeopleLimitEnum.GRADE.getCode())) {
            if (CollectionUtils.isEmpty(gradeList)) {
                throw new BusinessException("会员限制不能为空");
            }
        }
        CommonLinkTypeEnum linkTypeEnum = CommonLinkTypeEnum.getEnum(linkType);
        linkTypeEnum.check(link, appId);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        PageModuleStrategyFactory.register(PageCodeEnum.PERSONAL_CENTER.getCode() + "_" + PageModuleCodeEnum.TOOLBAR.getCode(), this);
    }
}
