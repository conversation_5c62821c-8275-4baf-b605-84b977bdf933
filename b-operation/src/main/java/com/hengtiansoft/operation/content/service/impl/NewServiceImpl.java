package com.hengtiansoft.operation.content.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.content.entity.dto.ProductSearchDTO;
import com.hengtiansoft.content.entity.vo.NewListVO;
import com.hengtiansoft.content.entity.vo.ProductListVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSearchDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseProListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseVO;
import com.hengtiansoft.item.enumeration.HomePageDisplayEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.manager.ItemDisplayCaseManager;
import com.hengtiansoft.item.manager.ItemDisplayCaseProManager;
import com.hengtiansoft.operation.content.service.NewService;

/**
 * Description: 新品上新Service实现类
 *
 * <AUTHOR>
 * @since 08.04.2020
 */
@Service
public class NewServiceImpl implements NewService {

    @Autowired
    private ProductManager productManager;

    @Autowired
    private ItemDisplayCaseProManager itemDisplayCaseProManager;

    @Autowired
    private ItemDisplayCaseManager itemDisplayCaseManager;


    @Override
    public List<NewListVO> list() {
        ItemDisplayCaseVO itemDisplayCaseVO = itemDisplayCaseManager.selectByType(HomePageDisplayEnum.NEW.getCode());
        DisplayCaseProSearchDTO dto = new DisplayCaseProSearchDTO();
        dto.setDisplayCaseId(itemDisplayCaseVO.getId());
        List<ItemDisplayCaseProListVO> listVOS = itemDisplayCaseProManager.list(dto);
        if (!CollectionUtils.isEmpty(listVOS)) {
            return BeanUtils.copyList(listVOS, NewListVO::new);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProductListVO> productList(ProductSearchDTO dto) {
        List<ProductListVO> newProductList = new ArrayList<>();
        ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
        if (dto.getProductName() != null) {
            searchDTO.setProductName(dto.getProductName());
        }
        if (dto.getCateId() != null) {
            List<Long> cateIds = new ArrayList<>();
            cateIds.add(dto.getCateId());
        }
        List<ProductBaseDTO> list = productManager.search(searchDTO).getList();
        list.stream().forEach(data -> {
            ProductListVO hotProductListVO = new ProductListVO();
            BeanUtils.copy(data, hotProductListVO);
            newProductList.add(hotProductListVO);
        });
        return newProductList;
    }

    @Override
    public void add(DisplayCaseProSaveDTO dto) {
        ItemDisplayCaseVO itemDisplayCaseVO = itemDisplayCaseManager.selectByType(1);
        dto.setDisplayCaseId(itemDisplayCaseVO.getId());
        itemDisplayCaseProManager.delete(dto.getDisplayCaseId());
        itemDisplayCaseProManager.add(dto);
    }

    @Override
    public List<NewListVO> resetList() {
        List<NewListVO> newListVOS = new ArrayList<>();
        ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
        searchDTO.setPageSize(999);
        List<ProductBaseDTO> list = productManager.search(searchDTO).getList();
        List<ProductBaseDTO> collect = list.stream().filter(n -> Objects.nonNull(n.getOnShelvesTime()) && n.getSaleStatus()==1).sorted(Comparator.comparing(ProductBaseDTO::getOnShelvesTime).reversed()).limit(10).collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            NewListVO newListVO = BeanUtils.deepCopy(collect.get(i), NewListVO.class);
            newListVO.setSort(i + 1);
            newListVOS.add(newListVO);
        }
        return newListVOS;
    }
}
