package com.hengtiansoft.operation.content.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.CommonBuyTypeEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountItemSaveDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountListDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountSortDTO;
import com.hengtiansoft.content.entity.po.PageModuleItem;
import com.hengtiansoft.content.entity.vo.PageModuleItemResultVO;
import com.hengtiansoft.content.entity.vo.PageModuleItemSelectedVO;
import com.hengtiansoft.content.entity.vo.PageModulePointAmountExcelVO;
import com.hengtiansoft.content.entity.vo.PageModulePointAmountListVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleItemManager;
import com.hengtiansoft.content.util.PageModuleItemUtil;
import com.hengtiansoft.item.dao.CategoryDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.dao.SkuDao;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.enumeration.ItemCategoryLevelEnum;
import com.hengtiansoft.item.enumeration.ProductSuggestItemTypeEnum;
import com.hengtiansoft.item.enumeration.ProductTypeEnum;
import com.hengtiansoft.item.utils.CateNameContext;
import com.hengtiansoft.operation.content.service.PageModulePointAmountService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.util.TransactionUtils;
import com.hengtiansoft.privilege.entity.po.PointAmount;
import com.hengtiansoft.privilege.entity.po.PointAmountItem;
import com.hengtiansoft.privilege.enums.PointAmountRangeEnum;
import com.hengtiansoft.privilege.manager.PointAmountItemManager;
import com.hengtiansoft.privilege.manager.PointAmountManager;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

@Service
public class PageModulePointAmountServiceImpl implements PageModulePointAmountService {

    @Resource
    private CategoryDao categoryDao;
    @Resource
    private CateNameContext cateNameContext;
    @Resource
    private SkuDao skuDao;
    @Resource
    private PageModuleItemManager pageModuleItemManager;
    @Resource
    private ProductDao productDao;
    @Resource
    private PointAmountManager pointAmountManager;
    @Resource
    private PointAmountItemManager pointAmountItemManager;
    @Resource
    private Executor sortTask;

    private static final String PAGE_CODE = PageCodeEnum.MEMBER_PAGE.getCode();

    private static final String MODULE_CODE = PageModuleCodeEnum.POINT_AMOUNT.getCode();

    @Override
    public PageVO<PageModulePointAmountListVO> list(PageModulePointAmountListDTO dto) {
        if (CollectionUtils.isNotEmpty(dto.getCateIds())) {
            List<Long> cateIdsList = new ArrayList<>();
            if(Objects.isNull(dto.getCateLevel())){
                dto.getCateIds().forEach(cateId -> cateIdsList.addAll(categoryDao.selectAllSubIds(cateId)));
            }else{
                if(ItemCategoryLevelEnum.FIRSTCATE.getCode() ==  dto.getCateLevel()){
                    cateIdsList.addAll(categoryDao.selectByParId(dto.getCateIds()));
                }else{
                    cateIdsList.addAll(dto.getCateIds());
                }
            }
            if (CollectionUtils.isEmpty(cateIdsList)) {
                return PageUtils.emptyPage(dto);
            }
            dto.setCateIds(cateIdsList);
        }
        dto.setPageCode(PAGE_CODE);
        dto.setModuleCode(MODULE_CODE);
        startPage(dto.getPageNum(), dto.getPageSize());
        List<PageModuleItemResultVO> resultVOS = pageModuleItemManager.findByCondition(dto);
        if (CollectionUtils.isEmpty(resultVOS)) {
            return PageUtils.emptyPage(dto);
        }
        List<Long> productIds = resultVOS.stream().map(PageModuleItemResultVO::getTargetId).distinct().collect(Collectors.toList());
        List<Sku> skus = skuDao.findByProductIds(productIds);
        Map<Long, List<Sku>> skuMap = skus.stream().collect(Collectors.groupingBy(Sku::getProductId));

        resultVOS.forEach(e -> e.setCateName(cateNameContext.getName(e.getCateId())));
        return PageUtils.convert(resultVOS, data-> {
            PageModulePointAmountListVO vo = new PageModulePointAmountListVO();
            vo.setId(data.getId());
            vo.setProductId(data.getTargetId());
            vo.setCateId(data.getCateId());
            vo.setCateName(data.getCateName());
            vo.setProductType(data.getProductType());
            vo.setProductTypeName(ProductTypeEnum.getDescByCode(data.getProductType()));
            vo.setProductName(data.getProductName());
            vo.setPicUrl(data.getPicUrl());
            vo.setSaleStatus(data.getSaleStatus());
            vo.setCycleFlag(data.getCycleFlag());
            vo.setEnableShow(data.getEnableShow());
            vo.setCreateTime(data.getCreateTime());
            vo.setUpdateTime(data.getUpdateTime());
            vo.setSort(data.getSort());
            List<PageModulePointAmountListVO.SkuVO> skuVOS = Lists.newArrayList();
            List<Sku> currentSkus = skuMap.getOrDefault(data.getTargetId(), Collections.emptyList());
            currentSkus.forEach(e -> {
                PageModulePointAmountListVO.SkuVO skuVO = new PageModulePointAmountListVO.SkuVO();
                skuVO.setId(e.getId());
                skuVO.setProductId(e.getProductId());
                skuVO.setSkuCode(e.getSkuCode());
                skuVO.setSalePrice(e.getSalePrice());
                skuVO.setStock(e.getStock());
                skuVOS.add(skuVO);
            });
            vo.setSkus(skuVOS);
            return vo;
        });
    }

    @Override
    public void delete(Long id) {
        PageModuleItem item = pageModuleItemManager.findByIdAndCode(PAGE_CODE, MODULE_CODE, id);
        if (Objects.isNull(item)) {
            throw new BusinessException("明细不存在");
        }
        pageModuleItemManager.deleteById(id);
    }

    @Override
    public void sort(PageModulePointAmountSortDTO dto) {
        PageModuleItem item = pageModuleItemManager.findByIdAndCode(PAGE_CODE, MODULE_CODE, dto.getId());
        if (Objects.isNull(item)) {
            throw new BusinessException("明细不存在");
        }
        Integer newSort = dto.getSort();
        Integer sort = item.getSort();
        if (sort.equals(newSort)) return;

        List<PageModuleItem> greaterThanCurrentSortItems = pageModuleItemManager.selectGreaterThanSortList(PAGE_CODE, MODULE_CODE, item.getSuggestType(), newSort);
        pageModuleItemManager.updateSortById(dto.getId(), dto.getSort());
        greaterThanCurrentSortItems = greaterThanCurrentSortItems.stream().filter(e -> !e.getId().equals(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(greaterThanCurrentSortItems)) {
            greaterThanCurrentSortItems.forEach(e -> e.setSort(e.getSort() + 1));
            pageModuleItemManager.batchUpdateSort(greaterThanCurrentSortItems);
        }
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .head(PageModulePointAmountExcelVO.class)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(1, Arrays.asList(0,1,2,4,5,6,7,8,9,12,13)))
                .build();
        PageModulePointAmountListDTO queryDTO = new PageModulePointAmountListDTO();
        queryDTO.setProductId(dto.getProductId());
        queryDTO.setProductType(dto.getProductType());
        queryDTO.setProductName(dto.getProductName());
        queryDTO.setCateIds(dto.getCateIds());
        queryDTO.setCateLevel(dto.getCateLevel());
        queryDTO.setSuggestType(dto.getSuggestType());
        queryDTO.setSaleStatus(dto.getSaleStatus());

        if (dto.getSuggestType().equals(ProductSuggestItemTypeEnum.APPOINT_RECOMMEND.getCode())) {
            dto.setFileName(ProductSuggestItemTypeEnum.APPOINT_RECOMMEND.getDesc() + "_" + dto.getFileName());
        } else {
            dto.setFileName(ProductSuggestItemTypeEnum.AUTO_RECOMMEND.getDesc() + "_" + dto.getFileName());
        }
        int pageNum = 1;
        int pageSize = 5000;
        int pages;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<PageModulePointAmountListVO> listVOPageVO = this.list(queryDTO);
            Pagination pagination = listVOPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<PageModulePointAmountExcelVO> excelVOS = PageModuleItemUtil.convert2ItemExcelVO(listVOPageVO.getList());
            excelWriter.write(excelVOS, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public List<PageModuleItemSelectedVO> selected(Integer suggestType) {
        List<PageModuleItem> selectedItems = pageModuleItemManager.findItemsByCodeAndType(PAGE_CODE, MODULE_CODE, suggestType);
        List<Long> selectedProductIds = selectedItems.stream().map(PageModuleItem::getTargetId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selectedProductIds)) {
            return Collections.emptyList();
        }
        PointAmount inProgress = StreamUtils.getFirst(pointAmountManager.getInProgressList());
        if(null == inProgress){
            return Collections.emptyList();
        }
        List<Long> spuIds = StreamUtils.toList(pointAmountItemManager.findByPointAmountId(inProgress.getId()), PointAmountItem::getProductId);
        if(Objects.equals(inProgress.getRange(), PointAmountRangeEnum.APPOINT_PRODUCT.getCode())){
            selectedProductIds = selectedProductIds.stream().filter(e -> spuIds.contains(e)).collect(Collectors.toList());
        }else if(Objects.equals(inProgress.getRange(), PointAmountRangeEnum.EXCLUDE_PRODUCT.getCode())){
            selectedProductIds = selectedProductIds.stream().filter(e -> !spuIds.contains(e)).collect(Collectors.toList());
        }
        List<Product> products = productDao.findNotDeleteByIds(selectedProductIds);
        if(Objects.equals(inProgress.getBuyType(), CommonBuyTypeEnum.CYCLE.getCode())){
            products = products.stream().filter(e -> FlagEnum.YES.getCode().equals(e.getCycleFlag())).collect(Collectors.toList());
        }

        Map<Long, Product> productMap = StreamUtils.toMap(products, Product::getId);
        List<PageModuleItemSelectedVO> list = new ArrayList<>();
        for (PageModuleItem item: selectedItems) {
            Product product = productMap.get(item.getTargetId());
            if(null == product){
                continue;
            }
            PageModuleItemSelectedVO selectedVO = new PageModuleItemSelectedVO();
            selectedVO.setProductId(product.getId());
            selectedVO.setProductName(product.getProductName());
            selectedVO.setId(item.getId());
            selectedVO.setSort(item.getSort());
            list.add(selectedVO);
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String save(PageModulePointAmountItemSaveDTO dto) {
        UserDetailDTO detailDTO = Objects.requireNonNull(UserUtil.getDetails(),"登录后重试");
        String tips = "";
        if (CollectionUtils.isEmpty(dto.getProductIds())) {
            pageModuleItemManager.deleteByType(PAGE_CODE, MODULE_CODE, dto.getSuggestType());
            return tips;
        }
        List<Long> productIds = dto.getProductIds();
        PointAmount inProgress = StreamUtils.getFirst(pointAmountManager.getInProgressList());
        if(null == inProgress){
            throw new BusinessException("当前没有进行中的积分抵现活动!");
        }
        List<Long> rangeSpuIds = StreamUtils.toList(pointAmountItemManager.findByPointAmountId(inProgress.getId()), PointAmountItem::getProductId);
        tips = validateMatch(productIds, inProgress, rangeSpuIds);
        if (CollectionUtils.isEmpty(productIds)) {
            return tips;
        }
        tips = tips + validateRepeatOpposite(dto.getSuggestType(), productIds);
        if (CollectionUtils.isEmpty(productIds)) {
            return tips;
        }
        //已选中的商品
        List<PageModuleItem> selectedItems = pageModuleItemManager.findItemsByCodeAndType(PAGE_CODE, MODULE_CODE, dto.getSuggestType());
        List<Long> selectedSpuIds = StreamUtils.toList(selectedItems, PageModuleItem::getTargetId);

        List<Long> addProductIds = productIds.stream().filter(e -> !selectedSpuIds.contains(e)).collect(Collectors.toList());
        List<Long> deleteProductIds = selectedSpuIds.stream().filter(e -> !productIds.contains(e)).collect(Collectors.toList());
        doBatchInsert(inProgress.getId(), addProductIds, dto.getSuggestType(), detailDTO.getUserName());
        doUpdateSort(selectedItems, inProgress.getId(), addProductIds.size());
        pageModuleItemManager.deleteByTypeAndProductIds(PAGE_CODE, MODULE_CODE,
                deleteProductIds, dto.getSuggestType(), detailDTO.getUserName());

        TransactionUtils.afterCommitAsyncExecute(sortTask, () ->
            pageModuleItemManager.autoRecommendProduct(PAGE_CODE, MODULE_CODE)
        );
        return tips;
    }

    private void doBatchInsert(Long activityId, List<Long> addProductIds, Integer suggestType, String userName) {
        if (CollectionUtils.isEmpty(addProductIds)) return;
        List<PageModuleItem> addItems = new ArrayList<>();
        int sort = 0;
        for (Long addProductId : addProductIds) {
            sort ++;
            PageModuleItem item = new PageModuleItem();
            item.setPageCode(PAGE_CODE);
            item.setModuleCode(MODULE_CODE);
            item.setActivityId(activityId);
            item.setTargetId(addProductId);
            item.setSort(sort);
            item.setSuggestType(suggestType);
            item.setIsHidden(BasicFlagEnum.NO.getKey());
            item.setOperator(userName);
            item.setCreateTime(new Date());
            item.setUpdateTime(new Date());
            item.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            addItems.add(item);
        }
        pageModuleItemManager.batchInsert(addItems);
    }

    private void doUpdateSort(List<PageModuleItem> selectedItems, Long activityId, int offset) {
        if(CollectionUtils.isEmpty(selectedItems)) return;
        List<PageModuleItem> updateItems = new ArrayList<>();
        for (PageModuleItem selectedItem: selectedItems) {
            PageModuleItem item = new PageModuleItem();
            item.setId(selectedItem.getId());
            item.setSort(selectedItem.getSort() + offset);
            item.setActivityId(activityId);
            updateItems.add(item);
        }
        pageModuleItemManager.batchUpdate(updateItems);
    }

    private String validateMatch(List<Long> productIds, PointAmount inProgress, List<Long> rangeSpuIds) {
        String tips = "";
        List<Long> mismatchProductIds = new ArrayList<>();
        if(Objects.equals(inProgress.getRange(), PointAmountRangeEnum.APPOINT_PRODUCT.getCode())){
            mismatchProductIds = productIds.stream().filter(e -> !rangeSpuIds.contains(e)).collect(Collectors.toList());
        }else if(Objects.equals(inProgress.getRange(), PointAmountRangeEnum.EXCLUDE_PRODUCT.getCode())){
            mismatchProductIds = productIds.stream().filter(e -> rangeSpuIds.contains(e)).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(mismatchProductIds)){
            productIds.removeAll(mismatchProductIds);
            tips = "SPUID:" + mismatchProductIds+"与当前进行中的积分抵现活动范围不匹配，已过滤;";
        }
        return tips;
    }

    private String validateRepeatOpposite(Integer suggestType, List<Long> productIds) {
        String tips = "";
        // 查询相反type的选中记录
        List<PageModuleItem> oppositeSuggestItems = pageModuleItemManager.findItemsByCodeAndType(PAGE_CODE, MODULE_CODE, suggestType == 1 ? 2 : 1);
        if (CollectionUtils.isNotEmpty(oppositeSuggestItems)) {
            List<Long> sameSelectedProductIds = oppositeSuggestItems.stream().filter(e -> productIds.contains(e.getTargetId())).map(PageModuleItem::getTargetId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sameSelectedProductIds)) {
                productIds.removeAll(sameSelectedProductIds);
                tips = "SPUID:" + sameSelectedProductIds+"已被" + (suggestType == 1?"自动推荐":"指定推荐") + "选中，已过滤";
            }
        }
        return tips;
    }


}
