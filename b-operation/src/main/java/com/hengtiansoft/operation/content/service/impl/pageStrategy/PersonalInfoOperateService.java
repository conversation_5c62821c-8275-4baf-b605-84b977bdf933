package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.content.entity.dto.PageModuleContentPageQryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentQueryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentSaveDTO;
import com.hengtiansoft.content.entity.po.PageModuleContent;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;
import com.hengtiansoft.content.enums.PageCodeEnum;
import com.hengtiansoft.content.enums.PageModuleCodeEnum;
import com.hengtiansoft.content.manager.PageModuleContentManager;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-01 15:11
 **/
@Slf4j
@Service
public class PersonalInfoOperateService implements PageModuleOperateStrategy, InitializingBean {

    @Resource
    private PageModuleContentManager pageModuleContentManager;
    @Override
    public void save(PageModuleContentSaveDTO saveDTO) {
        UserDetailDTO customerUser = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        String imageUrl = saveDTO.getImageUrl();
        Assert.notNull(imageUrl, "图片地址不能为空");
        List<PageModuleContent> personalInfos = pageModuleContentManager.getByPageCodeAndModuleCode(PageCodeEnum.MEMBER_PAGE.getCode(), PageModuleCodeEnum.PERSONAL_INFO.getCode());
        if (CollectionUtils.isNotEmpty(personalInfos)) {
            PageModuleContent content = personalInfos.get(0);
            content.setImageUrl(imageUrl);
            content.setOperator(customerUser.getUserName());
            content.setUpdateTime(new Date());
            pageModuleContentManager.updateById(content);
            return;
        }
        PageModuleContent content = new PageModuleContent();
        content.setPageCode(PageCodeEnum.MEMBER_PAGE.getCode());
        content.setModuleCode(PageModuleCodeEnum.PERSONAL_INFO.getCode());
        content.setImageUrl(imageUrl);
        content.setOperator(customerUser.getUserName());
        content.setCreateTime(new Date());
        content.setUpdateTime(new Date());
        content.setDelflag(0);
        pageModuleContentManager.save(content);
    }

    @Override
    public PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO) {
        List<PageModuleContent> contents = pageModuleContentManager.getByPageCodeAndModuleCode(queryDTO.getPageCode(), queryDTO.getModuleCode());
        PageModuleContentVO contentVO = new PageModuleContentVO();
        if (CollectionUtils.isEmpty(contents)) {
            return contentVO;
        }
        PageModuleContent content = contents.get(0);
        contentVO.setPageModuleCode(content.getModuleCode());
        contentVO.setPageCode(content.getPageCode());
        contentVO.setImageUrl(content.getImageUrl());
        return contentVO;
    }

    @Override
    public PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO) {
        return PageUtils.emptyPage();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        PageModuleStrategyFactory.register(PageCodeEnum.MEMBER_PAGE.getCode() + "_" + PageModuleCodeEnum.PERSONAL_INFO.getCode(), this);
    }
}
