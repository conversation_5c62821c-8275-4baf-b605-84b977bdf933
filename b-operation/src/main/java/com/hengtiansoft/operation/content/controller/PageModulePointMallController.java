package com.hengtiansoft.operation.content.controller;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.PageModulePointMallSaveDTO;
import com.hengtiansoft.operation.content.entity.PageModulePointMallVO;
import com.hengtiansoft.operation.content.service.PageModulePointMallService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "积分商城推荐位B端")
@RequestMapping("page/module/pointMall")
public class PageModulePointMallController {

    @Resource
    private PageModulePointMallService pageModulePointMallService;

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<PageModulePointMallVO> get() {
        return ResponseFactory.success(pageModulePointMallService.get());
    }


    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Void> save(@RequestBody @Validated PageModulePointMallSaveDTO dto) {
        pageModulePointMallService.save(dto);
        return ResponseFactory.success();
    }

}
