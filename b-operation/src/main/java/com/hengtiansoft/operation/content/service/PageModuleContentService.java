package com.hengtiansoft.operation.content.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.*;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-27 15:28
 **/
public interface PageModuleContentService {
    void save(PageModuleContentSaveDTO saveDTO);

    PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO);

    PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO);

    void sort(PageModuleContentSortDTO sortDTO);

    void end(PageModuleContentEndDTO endDTO);

    void delete(PageModuleContentEndDTO endDTO);

    void display(PageModuleContentDisplayDTO displayDTO);
}
