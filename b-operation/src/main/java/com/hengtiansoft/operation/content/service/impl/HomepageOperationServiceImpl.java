package com.hengtiansoft.operation.content.service.impl;

import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.content.entity.dto.HomepageOperationSaveDTO;
import com.hengtiansoft.content.entity.vo.HomepageOperationVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseUpdateDTO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseVO;
import com.hengtiansoft.item.manager.ItemDisplayCaseManager;
import com.hengtiansoft.operation.content.service.HomepageOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description: 首页运营位管理Service实现类
 *
 * <AUTHOR>
 * @since 08.04.2020
 */
@Service
public class HomepageOperationServiceImpl implements HomepageOperationService {

    @Autowired
    private ItemDisplayCaseManager itemDisplayCaseManager;

    @Override
    public void add(List<HomepageOperationSaveDTO> dtos) {
        dtos.stream().forEach(data -> {
            DisplayCaseUpdateDTO dto = new DisplayCaseUpdateDTO();
            BeanUtils.copy(data, dto);
            itemDisplayCaseManager.update(dto);
        });
    }

    @Override
    public List<HomepageOperationVO> one() {
        List<ItemDisplayCaseVO> itemDisplayCaseVOS = itemDisplayCaseManager.selectIndexType(Boolean.TRUE);
        return BeanUtils.copyList(itemDisplayCaseVOS, HomepageOperationVO::new);
    }
}
