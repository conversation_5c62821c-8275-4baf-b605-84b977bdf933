package com.hengtiansoft.operation.content.service.impl.pageStrategy;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.PageModuleContentPageQryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentQueryDTO;
import com.hengtiansoft.content.entity.dto.PageModuleContentSaveDTO;
import com.hengtiansoft.content.entity.vo.PageModuleContentListVO;
import com.hengtiansoft.content.entity.vo.PageModuleContentVO;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-27 11:08
 **/
public interface PageModuleOperateStrategy {

    void save(PageModuleContentSaveDTO saveDTO);

    PageModuleContentVO getContent(PageModuleContentQueryDTO queryDTO);

    PageVO<PageModuleContentListVO> pageContent(PageModuleContentPageQryDTO pageQryDTO);
}
