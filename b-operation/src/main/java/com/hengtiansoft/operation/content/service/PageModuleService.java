package com.hengtiansoft.operation.content.service;

import com.hengtiansoft.content.entity.dto.PageModuleSaveDTO;
import com.hengtiansoft.content.entity.vo.PageModuleVO;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-02 17:19
 **/
public interface PageModuleService {

    void update(PageModuleSaveDTO saveDTO);

    PageModuleVO getModule(String pageCode, String moduleCode);

    List<PageModuleVO> getModuleList(String pageCode);
}
