package com.hengtiansoft.operation.content.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountItemSaveDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountListDTO;
import com.hengtiansoft.content.entity.dto.PageModulePointAmountSortDTO;
import com.hengtiansoft.content.entity.vo.PageModuleItemSelectedVO;
import com.hengtiansoft.content.entity.vo.PageModulePointAmountListVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;

import java.util.List;

public interface PageModulePointAmountService {


    PageVO<PageModulePointAmountListVO> list(PageModulePointAmountListDTO dto);

    void delete(Long id);

    void sort(PageModulePointAmountSortDTO dto);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    List<PageModuleItemSelectedVO> selected(Integer suggestType);

    String save(PageModulePointAmountItemSaveDTO dto);
}
