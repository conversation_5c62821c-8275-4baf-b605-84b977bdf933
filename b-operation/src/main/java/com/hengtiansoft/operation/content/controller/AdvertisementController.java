package com.hengtiansoft.operation.content.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.content.entity.dto.AdvertisementAddDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementSearchDTO;
import com.hengtiansoft.content.entity.dto.AdvertisementUpdateDTO;
import com.hengtiansoft.content.entity.vo.AdvertisementVO;
import com.hengtiansoft.operation.content.service.AdvertisementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 广告位管理
 *
 * <AUTHOR>
 * @since 19.03.2020
 */

@RestController
@Api(tags = "广告位管理")
@RequestMapping("advertise")
public class AdvertisementController {

    @Autowired
    private AdvertisementService advertisementService;

    @ApiOperation(value = "新增广告")
    @PostMapping("/add")
    public Response<Long> add(@ApiParam("广告新增信息") @RequestBody @Validated AdvertisementAddDTO advertisementAddDTO) {
        advertisementService.add(advertisementAddDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "修改广告")
    @PostMapping("/update")
    public Response<Object> update(@ApiParam("广告更新信息") @RequestBody AdvertisementUpdateDTO advertisementUpdateDTO) {
        advertisementService.update(advertisementUpdateDTO);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除广告")
    @DeleteMapping("/delete")
    public Response<Object> delete(@ApiParam("广告ID") @RequestParam Long id) {
        advertisementService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "广告详情")
    @GetMapping("/info")
    public Response<AdvertisementVO> one(@ApiParam("广告ID") @RequestParam Long id) {
        return ResponseFactory.success(advertisementService.one(id));
    }

    @ApiOperation(value = "广告列表")
    @PostMapping("/list")
    public Response<PageVO<AdvertisementVO>> list(@ApiParam("广告查询信息") @RequestBody AdvertisementSearchDTO advertisementSearchDTO) {
        return ResponseFactory.success(advertisementService.list(advertisementSearchDTO));
    }
}
