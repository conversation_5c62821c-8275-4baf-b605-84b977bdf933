package com.hengtiansoft.operation.content.service;

import java.util.List;

import com.hengtiansoft.content.entity.dto.HotUpdateDTO;
import com.hengtiansoft.content.entity.dto.ProductSearchDTO;
import com.hengtiansoft.content.entity.vo.HotListVO;
import com.hengtiansoft.content.entity.vo.ProductListVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;

/**
 * Description: 热门商品Service
 *
 * <AUTHOR>
 * @since 07.04.2020
 */
public interface HotService {

    void add(DisplayCaseProSaveDTO dto);

    void update(HotUpdateDTO dto);

    void delete(Long id);

    List<HotListVO> list();

    List<ProductListVO> productList(ProductSearchDTO dto);
}
