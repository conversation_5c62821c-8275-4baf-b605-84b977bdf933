package com.hengtiansoft.operation.content.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.hengtiansoft.item.enumeration.HomePageDisplayEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.content.entity.dto.HotUpdateDTO;
import com.hengtiansoft.content.entity.dto.ProductSearchDTO;
import com.hengtiansoft.content.entity.vo.HotListVO;
import com.hengtiansoft.content.entity.vo.ProductListVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSearchDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProUpdateDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseProListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseVO;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.manager.ItemDisplayCaseManager;
import com.hengtiansoft.item.manager.ItemDisplayCaseProManager;
import com.hengtiansoft.operation.content.service.HotService;

/**
 * Description:  热销排行Service实现类
 *
 * <AUTHOR>
 * @since 07.04.2020
 */
@Service
public class HotServiceImpl implements HotService {

    @Autowired
    private ItemDisplayCaseProManager itemDisplayCaseProManager;

    @Autowired
    private ItemDisplayCaseManager itemDisplayCaseManager;

    @Autowired
    private ProductManager productManager;


    @Override
    public void add(DisplayCaseProSaveDTO dto) {
        ItemDisplayCaseVO itemDisplayCaseVO = itemDisplayCaseManager.selectByType(HomePageDisplayEnum.HOT.getCode());
        dto.setDisplayCaseId(itemDisplayCaseVO.getId());
        itemDisplayCaseProManager.delete(dto.getDisplayCaseId());
        itemDisplayCaseProManager.add(dto);
    }

    @Override
    public void update(HotUpdateDTO dto) {
        DisplayCaseProUpdateDTO updateDTO = BeanUtils.deepCopy(dto, DisplayCaseProUpdateDTO.class);
        itemDisplayCaseProManager.update(updateDTO);
    }

    @Override
    public void delete(Long id) {
        Set<Long> productIds = new HashSet<>();
        productIds.add(id);
        ItemDisplayCaseVO itemDisplayCaseVO = itemDisplayCaseManager.selectByType(HomePageDisplayEnum.SELFSUPPORT.getCode());
        itemDisplayCaseProManager.delete(itemDisplayCaseVO.getId());
    }

    @Override
    public List<HotListVO> list() {
        ItemDisplayCaseVO itemDisplayCaseVO = itemDisplayCaseManager.selectByType(HomePageDisplayEnum.HOT.getCode());
        if(itemDisplayCaseVO !=null){
            DisplayCaseProSearchDTO dto = new DisplayCaseProSearchDTO();
            dto.setDisplayCaseId(itemDisplayCaseVO.getId());
            List<ItemDisplayCaseProListVO> listVOS = itemDisplayCaseProManager.list(dto);
            if (!CollectionUtils.isEmpty(listVOS)) {
                List<HotListVO> list = BeanUtils.copyList(listVOS, HotListVO::new);
                return list;
            } else {
                return new ArrayList<>();
            }
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProductListVO> productList(ProductSearchDTO dto) {
        List<ProductListVO> hotProductList = new ArrayList<>();
        ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
        List<Integer> saleStatus = new ArrayList<>();
        saleStatus.add(1);
        searchDTO.setSaleStatus(saleStatus);
        if (dto.getProductName() != null) {
            searchDTO.setProductName(dto.getProductName());
        }
        if (dto.getCateId() != null) {
            List<Long> cateIds = new ArrayList<>();
            cateIds.add(dto.getCateId());
        }
        List<ProductBaseDTO> list = productManager.search(searchDTO).getList();
        list.stream().forEach(data -> {
            ProductListVO hotProductListVO = new ProductListVO();
            BeanUtils.copy(data, hotProductListVO);
            hotProductList.add(hotProductListVO);
        });
        return hotProductList;
    }
}
