package com.hengtiansoft.operation.pointAmount.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.vo.ProductVO;
import com.hengtiansoft.item.enumeration.ProductTypeEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.pointAmount.service.PointAmountService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.dto.OrderSearchDTO;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.entity.vo.OrderInfoV2VO;
import com.hengtiansoft.order.entity.vo.PointAmountOrderV2ExcelVO;
import com.hengtiansoft.order.entity.vo.PointAmountSkuV2ExcelVO;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.manager.OrderV2Manager;
import com.hengtiansoft.order.util.OrderInfoUtil;
import com.hengtiansoft.pay.config.WeChatMiniProgramConfig;
import com.hengtiansoft.privilege.entity.dto.PointAmountDTO;
import com.hengtiansoft.privilege.entity.dto.PointAmountExportDTO;
import com.hengtiansoft.privilege.entity.dto.PointAmountListDTO;
import com.hengtiansoft.privilege.entity.po.PointAmount;
import com.hengtiansoft.privilege.entity.po.PointAmountItem;
import com.hengtiansoft.privilege.entity.vo.PointAmountItemVO;
import com.hengtiansoft.privilege.entity.vo.PointAmountVO;
import com.hengtiansoft.privilege.enums.PointAmountRangeEnum;
import com.hengtiansoft.privilege.manager.PointAmountItemManager;
import com.hengtiansoft.privilege.manager.PointAmountManager;
import com.hengtiansoft.privilege.util.PointAmountUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.interfaces.MilkProducerManager;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static com.github.pagehelper.page.PageMethod.startPage;


@Slf4j
@Service
public class PointAmountServiceImpl implements PointAmountService {

    @Resource
    private PointAmountManager pointAmountManager;
    @Resource
    private PointAmountItemManager pointAmountItemManager;
    @Resource
    private ProductManager productManager;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private OrderV2Manager orderV2Manager;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private WeChatMiniProgramConfig weChatMiniProgramConfig;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private ProductDao productDao;

    @Resource
    private MilkProducerService milkProducerService;

    @Resource
    private Executor updateNotifyTask;


    @Override
    public PageVO<PointAmountVO> getList(PointAmountListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<PointAmount> pointAmountList = pointAmountManager.findByCondition(dto);
        if (CollectionUtils.isEmpty(pointAmountList)) {
            return PageUtils.emptyPage(dto);
        }
        return PageUtils.convert(pointAmountList, data -> {
            PointAmountVO vo = PointAmountUtil.convert2VO(data);
            return vo;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PointAmountDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        dto.setStatus(CommonOptUtil.getStatus(dto.getStartTime(), dto.getEndTime()).getCode());
        validParam(dto);
        // 校验活动时间
        if(Objects.nonNull(dto.getEndTime()) && Objects.nonNull(dto.getStartTime())){
            if(dto.getEndTime().before(dto.getStartTime())){
                throw new BusinessException("活动结束时间不能早于开始时间");
            }
        }
        Date now = new Date();
        if(dto.getEndTime().before(now)){
            throw new BusinessException("结束时间不能小于当前时间");
        }
        PointAmount po = PointAmountUtil.buildPO(dto);
        po.setOperator(user.getUserName());
        if(null == dto.getId()){
            // 无id，新增
            po.setCreator(user.getUserName());
            pointAmountManager.insert(po);
        }else{
            PointAmount old = pointAmountManager.findById(dto.getId());
            Assert.notNull(old, "活动不存在");
            BeanUtil.copyProperties(po, old, CopyOptions.create().setIgnoreNullValue(true));
            old.setBirthdayUser(dto.getBirthdayUser());
            pointAmountManager.updateAll(old);
            redisOperation.expire(PointAmountUtil.USERCOUNT + po.getId(), DateUtil.getRemainSeconds(new Date(), po.getEndTime()), TimeUnit.SECONDS);
        }

        List<PointAmountItem> itemPos = PointAmountUtil.buildItemPO(dto, po);
        // 删除旧明细
        pointAmountItemManager.deleteByPointAmountId(po.getId());
        // 明细新增
        if(CollectionUtils.isNotEmpty(itemPos)){
            pointAmountItemManager.insertList(itemPos);
        }
        TransactionUtils.afterCommitAsyncExecute(updateNotifyTask, () -> {
            log.info("异步发送更新活动商品消息");
            milkProducerService.syncMemberPagePointAmountActivityItem(po.getId());
        });
    }

    @Override
    public PointAmountVO get(Long id) {
        PointAmount po = pointAmountManager.findById(id);
        Assert.notNull(po, "活动不存在");
        PointAmountRangeEnum rangeEnum = PointAmountRangeEnum.getEnum(po.getRange());
        List<Long> spuIds = new ArrayList<>();
        if(PointAmountRangeEnum.APPOINT_PRODUCT == rangeEnum || PointAmountRangeEnum.EXCLUDE_PRODUCT == rangeEnum){
            List<PointAmountItem> itemList = pointAmountItemManager.findByPointAmountId(id);
            spuIds = StreamUtils.toList(itemList, PointAmountItem::getProductId);
        }
        List<Product> productList = productManager.findByIds(spuIds);
        PointAmountVO vo = PointAmountUtil.convert2VO(po);
        vo.setProductVoList(BeanUtils.copyList(productList, ProductVO::new));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        PointAmount old = pointAmountManager.findById(id);
        Assert.notNull(old, "活动不存在");
        if(CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(old.getStatus())){
            throw new BusinessException("进行中的活动不能被删除！");
        }
        pointAmountManager.deleteById(id);
        pointAmountItemManager.deleteByPointAmountId(id);
    }

    @Override
    public void start(Long id) {
        PointAmount old = pointAmountManager.findById(id);
        Assert.notNull(old, "活动不存在");
        if(!CommonActivityStatusEnum.NOT_STARTED.getCode().equals(old.getStatus())){
            throw new BusinessException("活动状态错误！");
        }
        PointAmountDTO dto = PointAmountUtil.convert2DTO(old);
        List<PointAmountItem> itemList = pointAmountItemManager.findByPointAmountId(id);
        List<Long> spuIds = StreamUtils.toList(itemList, PointAmountItem::getProductId);
        dto.setProductIds(spuIds);
        dto.setStartTime(new Date());
        validParam(dto);
        PointAmount po = new PointAmount();
        po.setId(id);
        po.setStartTime(new Date());
        po.setStatus(CommonActivityStatusEnum.IN_PROGRESS.getCode());
        pointAmountManager.update(po);
    }

    @Override
    public void end(Long id) {
        PointAmount old = pointAmountManager.findById(id);
        Assert.notNull(old, "活动不存在");
        if(!CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(old.getStatus())){
            throw new BusinessException("活动状态错误！");
        }

        PointAmount po = new PointAmount();
        po.setId(id);
        po.setEndTime(new Date());
        po.setStatus(CommonActivityStatusEnum.END.getCode());
        pointAmountManager.update(po);
        redisOperation.expire(PointAmountUtil.USERCOUNT + po.getId(), DateUtil.getRemainSeconds(new Date(), po.getEndTime()), TimeUnit.SECONDS);
    }

    @Override
    public void export(PointAmountExportDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();

        List<DataExportTask> dataExportTaskList = Lists.newArrayList();
        for (Integer exportType : dto.getExportTypeList()) {
            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportType);
            if (null == enumByCode){
                throw new BusinessException("导出类型不存在");
            }
            DataExportTask dataExportTask = new DataExportTask();
            String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
            dataExportTask.setExportName(enumByCode.getFileName() + date);
            dataExportTask.setExportParam(JSONObject.toJSONString(dto));
            dataExportTask.setExportType(exportType);
            dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
            dataExportTask.setOperation(detailDTO.getUserName());
            dataExportTaskDao.saveOne(dataExportTask);
            dataExportTaskList.add(dataExportTask);
        }
        // 异步执行
        CompletableFuture.runAsync(()-> {
            export(dto, dataExportTaskList);
        });
    }

    @Override
    public PageVO<PointAmountItemVO> itemList(PointAmountListDTO dto) {
        PointAmount pointAmount = pointAmountManager.findInProgressByGrade(dto.getGrade());
        if(null == pointAmount){
            return PageUtils.emptyPage(dto);
        }

        List<Long> spuIds = StreamUtils.toList(pointAmountItemManager.findByPointAmountId(pointAmount.getId()), PointAmountItem::getProductId);
        List<Product> productList = new ArrayList<>();
        ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
        searchDTO.setProductTypeList(Arrays.asList(ProductTypeEnum.PACKAGE.getCode(),ProductTypeEnum.SINGLE_PRODUCT.getCode(), ProductTypeEnum.ENTITY_CARD.getCode()));
        searchDTO.setCycleFlag(Objects.equals(2, pointAmount.getBuyType()) ? 1: null);
        if(Objects.equals(pointAmount.getRange(), PointAmountRangeEnum.ALL.getCode())){
            startPage(dto.getPageNum(), dto.getPageSize());
            searchDTO.setProductName(dto.getName());
            productList = productDao.findByCondition(searchDTO);
        }else if(Objects.equals(pointAmount.getRange(), PointAmountRangeEnum.APPOINT_PRODUCT.getCode())){
            startPage(dto.getPageNum(), dto.getPageSize());
            searchDTO.setProductName(dto.getName());
            searchDTO.setProductIds(spuIds);
            productList = productDao.findByCondition(searchDTO);
        }else if(Objects.equals(pointAmount.getRange(), PointAmountRangeEnum.EXCLUDE_PRODUCT.getCode())){
            startPage(dto.getPageNum(), dto.getPageSize());
            searchDTO.setProductName(dto.getName());
            searchDTO.setExcludeProductIds(spuIds);
            productList = productDao.findByCondition(searchDTO);
        }
        List<PointAmountItemVO> list = new ArrayList<>();
        for (Product product: productList) {
            PointAmountItemVO itemVO = new PointAmountItemVO();
            itemVO.setPointAmountId(pointAmount.getId());
            itemVO.setProductId(product.getId());
            itemVO.setProductName(product.getProductName());
            itemVO.setProductType(product.getProductType());
            itemVO.setEnableShow(product.getEnableShow());
            itemVO.setSaleStatus(product.getSaleStatus());
            itemVO.setGrade(pointAmount.getGrade());
            itemVO.setGradeList(Arrays.asList(pointAmount.getGrade().split(",")));
            itemVO.setBirthdayUser(pointAmount.getBirthdayUser());
            itemVO.setPointMin(pointAmount.getPointMin());
            itemVO.setPointRate(pointAmount.getPointRate());
            if(null != pointAmount.getPointRate()){
                itemVO.setPointRateStr(pointAmount.getPointRate().multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() + "%");
            }
            list.add(itemVO);
        }
        return PageUtils.toPageVO(PageUtils.extract(productList), list);
    }

    private void export(PointAmountExportDTO dto, List<DataExportTask> dataExportTaskList){
        PointAmountListDTO listDTO = new PointAmountListDTO();
        listDTO.setStatus(dto.getStatus());
        listDTO.setFuzzyName(dto.getFuzzyName());
        List<PointAmount> pointAmountList = pointAmountManager.findByCondition(listDTO);
        List<Long> pointAmountIds = StreamUtils.toList(pointAmountList, PointAmount::getId);
        List<OrderInfoV2VO> orderInfoV2VOS = new ArrayList<>();

        for (DataExportTask exportTask : dataExportTaskList) {
            DataExportTask dataExportTask = new DataExportTask();
            dataExportTask.setId(exportTask.getId());

            try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
                List excelVOS = new ArrayList<>();
                Class head = null;
                Integer mergeRowIndex = null;
                List<Integer> mergeColumnRegion = null;
                if(enumByCode == FileExportCenterEnum.point_amount_order){
                    head = PointAmountOrderV2ExcelVO.class;
                }else if(enumByCode == FileExportCenterEnum.point_amount_spu){
                    head = PointAmountSkuV2ExcelVO.class;
                    mergeRowIndex = 1;
                    mergeColumnRegion = Lists.newArrayList(0);
                }else{
                    throw new BusinessException("导出类型有误");
                }
                ExcelWriter excelWriter = EasyExcel.write(outputStream, head)
                        .autoCloseStream(Boolean.TRUE)
                        .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnRegion))
                        .build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                int pageNum = 1;
                int pageSize = 2000;
                Integer pages = 1;
                do {
                    if(CollectionUtils.isNotEmpty(pointAmountIds)){
                        OrderSearchDTO searchDTO = new OrderSearchDTO();
                        searchDTO.setPointAmountIds(pointAmountIds);
                        searchDTO.setPageNum(pageNum);
                        searchDTO.setPageSize(pageSize);
                        PageVO<OrderInfoV2VO> orderInfoV2S = orderV2Manager.cardList(searchDTO);
                        pages = orderInfoV2S.getPages();
                        orderInfoV2VOS = orderInfoV2S.getList();
                        if(enumByCode == FileExportCenterEnum.point_amount_order){
                            excelVOS = OrderInfoUtil.convert2PointAmountOrderExcelList(orderInfoV2VOS, weChatMiniProgramConfig.getMchID());
                        }else if(enumByCode == FileExportCenterEnum.point_amount_spu){
                            excelVOS = OrderInfoUtil.convert2PointAmountOrderSkuExcelList(orderInfoV2VOS, weChatMiniProgramConfig.getMchID());
                        }else{
                            throw new BusinessException("导出类型有误");
                        }
                    }
                    pageNum ++;
                    excelWriter.write(excelVOS, writeSheet);
                }while (pageNum <= pages);
                excelWriter.finish();
                String fileName = exportTask.getExportName() + enumByCode.getSubfix();
                String url = aliyunOssUtils.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()),
                        fileName);
                dataExportTask.setFileUrl(url);
                dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
            } catch (Exception e) {
                log.error("导出失败", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
            }
            dataExportTaskDao.updateByPrimaryKey(dataExportTask);
        }
    }

    private void validParam(PointAmountDTO dto) {
        // 起用量、使用上限校验
        if(dto.getPointMax() < dto.getPointMin()){
            throw new BusinessException("使用上限需要大于等于起用量！");
        }
        // 活动名重复校验
        PointAmountListDTO queryDto = new PointAmountListDTO();
        queryDto.setStatusList(Arrays.asList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()));
        queryDto.setName(dto.getName());
        queryDto.setNotId(dto.getId());
        if(CollectionUtils.isNotEmpty(pointAmountManager.findByCondition(queryDto))){
            throw new BusinessException("活动名称不能重复！");
        }
        // 校验商品范围
        if(PointAmountRangeEnum.APPOINT_PRODUCT.getCode().equals(dto.getRange()) || PointAmountRangeEnum.EXCLUDE_PRODUCT.getCode().equals(dto.getRange())){
            if(CollectionUtils.isEmpty(dto.getProductIds())){
                throw new BusinessException("商品范围不能为空！");
            }
        }
        // 时间交叉校验
        queryDto = new PointAmountListDTO();
        queryDto.setStatusList(Arrays.asList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()));
        queryDto.setNotId(dto.getId());
        List<PointAmount> pointAmountList = pointAmountManager.findByCondition(queryDto);
        for (PointAmount pointAmount : pointAmountList) {
            if(Objects.equals(pointAmount.getId(), dto.getId())){
                continue;
            }
            if (DateUtil.isOverlap(dto.getStartTime(), dto.getEndTime(), pointAmount.getStartTime(), pointAmount.getEndTime())) {
                throw new BusinessException("该活动【" + dto.getName() + "】的时间段与名称为【" + pointAmount.getName() + "】的有重复，请修改");
            }
        }
        //进行中判断
        if(CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(dto.getStatus())){
            queryDto = new PointAmountListDTO();
            queryDto.setStatus(CommonActivityStatusEnum.IN_PROGRESS.getCode());
            queryDto.setNotId(dto.getId());
            List<PointAmount> inProgressList = pointAmountManager.findByCondition(queryDto);
            if(CollectionUtils.isNotEmpty(inProgressList)){
                throw new BusinessException("已有进行中的活动！");
            }
        }
    }
}
