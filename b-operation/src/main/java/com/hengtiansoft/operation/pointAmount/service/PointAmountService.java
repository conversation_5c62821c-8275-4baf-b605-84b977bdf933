package com.hengtiansoft.operation.pointAmount.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.privilege.entity.dto.PointAmountDTO;
import com.hengtiansoft.privilege.entity.dto.PointAmountExportDTO;
import com.hengtiansoft.privilege.entity.dto.PointAmountListDTO;
import com.hengtiansoft.privilege.entity.vo.PointAmountItemVO;
import com.hengtiansoft.privilege.entity.vo.PointAmountVO;

public interface PointAmountService {

    PageVO<PointAmountVO> getList(PointAmountListDTO dto);

    void save(PointAmountDTO dto);

    PointAmountVO get(Long id);

    void delete(Long id);

    void start(Long id);

    void end(Long id);

    void export(PointAmountExportDTO dto);

    PageVO<PointAmountItemVO> itemList(PointAmountListDTO dto);
}
