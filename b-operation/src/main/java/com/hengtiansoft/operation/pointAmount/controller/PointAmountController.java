package com.hengtiansoft.operation.pointAmount.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.pointAmount.service.PointAmountService;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.privilege.entity.dto.PointAmountDTO;
import com.hengtiansoft.privilege.entity.dto.PointAmountExportDTO;
import com.hengtiansoft.privilege.entity.dto.PointAmountListDTO;
import com.hengtiansoft.privilege.entity.vo.PointAmountItemVO;
import com.hengtiansoft.privilege.entity.vo.PointAmountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

@RestController
@Api(tags = "积分抵现B端")
@RequestMapping("/pointAmount")
public class PointAmountController {

    @Autowired
    private PointAmountService pointAmountService;

    @ApiOperation("分页列表")
    @PostMapping("/getList")
    public Response<PageVO<PointAmountVO>> getList(@RequestBody PointAmountListDTO dto) {
        return ResponseFactory.success(pointAmountService.getList(dto));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Void> save(@RequestBody @Validated PointAmountDTO dto) {
        pointAmountService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<PointAmountVO> get(@RequestParam Long id) {
        return ResponseFactory.success(pointAmountService.get(id));
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Void> delete(@RequestParam Long id) {
        pointAmountService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("开始活动")
    @GetMapping("/start")
    public Response<Void> start(@RequestParam Long id) {
        pointAmountService.start(id);
        return ResponseFactory.success();
    }

    @ApiOperation("结束活动")
    @GetMapping("/end")
    public Response<Void> end(@RequestParam Long id) {
        pointAmountService.end(id);
        return ResponseFactory.success();
    }

    @ApiOperation("导出")
    @PostMapping("/export")
    public Response<Void> export(@RequestBody PointAmountExportDTO dto) {
        dto.setExportTypeList(Arrays.asList(FileExportCenterEnum.point_amount_order.getCode(),FileExportCenterEnum.point_amount_spu.getCode()));
        pointAmountService.export(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("进行中的积分抵现商品")
    @PostMapping("/item/list")
    public Response<PageVO<PointAmountItemVO>> itemList(@RequestBody PointAmountListDTO dto) {
        return ResponseFactory.success(pointAmountService.itemList(dto));
    }
}
