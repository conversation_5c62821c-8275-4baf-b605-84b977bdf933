package com.hengtiansoft.operation.password;

import com.hengtiansoft.common.encrypt.rsa.RSACipher;
import com.hengtiansoft.common.entity.exception.BusinessException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomerPasswordEncoder implements PasswordEncoder {


    @Value("${rsa.private-key}")
    private String rsaPrivateKey;

    // 密码正则表达式 包含大写字母、小写字母、数字、特殊符号（不是字母，数字，下划线，汉字的字符）的12位以上
    public static final String PASSWORD_PATTERN_STR = "^(?![A-Za-z0-9]+$)(?![a-z0-9\\W]+$)(?![A-Za-z\\W]+$)(?![A-Z0-9\\W]+$)[a-zA-Z0-9\\W]{12,}$";

    public static final Pattern PASSWORD_PATTERN = Pattern.compile(PASSWORD_PATTERN_STR);

    @Override
    public String encode(CharSequence rawPassword) {

        // 密码解码
        String password = RSACipher.decrypt(rawPassword.toString(), rsaPrivateKey);

        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

        return passwordEncoder.encode(password);
    }


    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {

        // 密码解码
        String password = RSACipher.decrypt(rawPassword.toString(), rsaPrivateKey);

        Matcher matcher = PASSWORD_PATTERN.matcher(rawPassword);

        if (!matcher.matches()) {
            throw new BusinessException("密码强度不符合规则");
        }

        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

        return passwordEncoder.matches(password, encodedPassword);


    }


    public static void main(String[] args) {


        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

        System.out.println(passwordEncoder.encode("123456"));


    }

}
