package com.hengtiansoft.operation.controller;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.enumeration.NascentFlagEnum;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.CouponRuleListDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.enumeration.ProductTypeEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.operation.order.service.OrderSkuBackService;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.dao.CouponRuleDao;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.privilege.util.MonitorPageUtil;
import com.hengtiansoft.thirdpart.entity.dto.nascent.CustomerSaveDTO;
import com.hengtiansoft.thirdpart.enumeration.BaiduLinkTypeEnum;
import com.hengtiansoft.thirdpart.enumeration.WeixinUrlLinkTypeEnum;
import com.hengtiansoft.thirdpart.interfaces.BaiduManager;
import com.hengtiansoft.thirdpart.interfaces.NascentCustomerManager;
import com.hengtiansoft.user.entity.dto.CustomerUserDTO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.manager.CustomerUserManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TestController
 */
@RestController
@Api(tags = "TestController")
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Resource
    private OrderSyncAdapter orderSyncAdapter;
    @Resource
    private OrderSkuBackService orderSkuBackService;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private NascentCustomerManager nascentCustomerManager;
    @Resource
    private CouponRuleManager couponRuleManager;

    @Value("${milk.local.h5Domain}")
    private String localH5Domain;
    @Resource
    private ProductDao productDao;
    @Resource
    private BaiduManager baiduManager;
    @Resource
    private ProductManager productManager;
    @Resource
    private CouponRuleDao couponRuleDao;

    @ApiOperation(value = "同步器注入测试")
    @PostMapping(value = "/syncer")
    public Response<Boolean> cardList() {
        return ResponseFactory.success(true);
    }

    @ApiOperation(value = "sku数据合并")
    @GetMapping(value = "/mergeSku")
    public Response<Boolean> mergeSku(Long startId, Long endId, Integer pageSize) {
        orderSkuBackService.mergeSku(startId, endId, pageSize);
        return ResponseFactory.success(true);
    }

    @ApiOperation(value = "redis数据清理")
    @PostMapping("/redis/clear")
    public Response<String> assistClear(@RequestBody JSONObject dto) {
        log.info("redis数据清理");
        String secret = dto.getString("secret");
        if(!Objects.equals(secret, "milk_assist_clear")){
            throw new BusinessException("非法操作");
        }
        String redisKey = dto.getString("redisKey");
        redisOperation.del(redisKey);
        return ResponseFactory.success(redisKey);
    }

    @ApiOperation(value = "南讯手机号补推")
    @GetMapping("/nascent/register")
    public Response<String> nascentRegister(String phone) {
        try {
            Assert.isTrue(StringUtils.isNotBlank(phone), "手机号不能为空");
            List<CustomerUser> users = customerUserManager.findByPhones(Lists.newArrayList(phone));
            if(CollectionUtils.isEmpty(users)){
                throw new BusinessException("用户不存在");
            }else if(users.size() > 1){
                throw new BusinessException("用户存在多个");
            }
            CustomerUser customerUser = users.get(0);
            if(Objects.equals(customerUser.getNascentFlag(), NascentFlagEnum.MEMBER.getCode())){
                throw new BusinessException("用户已经是南讯会员");
            }
            CustomerSaveDTO saveDTO = new CustomerSaveDTO();
            saveDTO.setMobile(customerUser.getPhone());
            String customerName = StringUtils.isBlank(customerUser.getNickName()) ? (StringUtils.isBlank(customerUser.getUserName()) ? customerUser.getPhone() : customerUser.getUserName()):customerUser.getNickName();
            saveDTO.setCustomerName(customerName);
            if (Objects.isNull(nascentCustomerManager.registerCustomer(saveDTO))) {
                log.error("登录时注册南讯会员失败！");
                throw new BusinessException("登录时注册南讯会员失败！");
            }else{
                CustomerUserDTO customerUserUpdateDTO = new CustomerUserDTO();
                customerUserUpdateDTO.setId(customerUser.getId());
                customerUserUpdateDTO.setNascentFlag(NascentFlagEnum.MEMBER.getCode());
                customerUserUpdateDTO.setJoinTime(new Date());
                customerUserManager.updateOne(customerUserUpdateDTO);
            }
            return ResponseFactory.success("入会成功");
        } catch (Exception e) {
            log.error("入会失败", e);
            return ResponseFactory.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "短链初始化")
    @PostMapping("/shortLink/init")
    public Response<String> shortLinkInit(@RequestBody JSONObject dto) {
        log.info("短链初始化");
        String secret = dto.getString("secret");
        if(!Objects.equals(secret, "cONUtRah")){
            throw new BusinessException("非法操作");
        }
        Integer type = dto.getInteger("type");
        WeixinUrlLinkTypeEnum typeEnum = WeixinUrlLinkTypeEnum.getEnum(type);
        if(typeEnum == WeixinUrlLinkTypeEnum.PRODUCT){
            List<Product> productList = productDao.findList();
            for (Product product : productList) {
                // 新增或修改时生成百度短链, 提奶商品不生成短链
                if (!ProductTypeEnum.NORMAL.getCode().equals(product.getProductType()) && StringUtils.isBlank(product.getShortLink())) {
                    String longLink = localH5Domain + MonitorPageUtil.H5_WECHAT_URL + "?id=" + product.getId()+"&type="+ WeixinUrlLinkTypeEnum.PRODUCT.getCode();
                    String shortLink = baiduManager.getShortLink(longLink, BaiduLinkTypeEnum.ONE_YEAR.getCode());
                    ProductBaseDTO updateDto = new ProductBaseDTO();
                    updateDto.setId(product.getId());
                    updateDto.setShortLink(shortLink);
                    updateDto.setUpdateTime(product.getUpdateTime());
                    productManager.singleUpdate(updateDto);
                }

            }
        }
        if(typeEnum == WeixinUrlLinkTypeEnum.COUPON){
            CouponRuleListDTO couponRuleListDTO = new CouponRuleListDTO();
            List<CouponRule> couponRuleList = couponRuleManager.couponRuleList(couponRuleListDTO);
            for (CouponRule couponRule : couponRuleList) {
                if (StringUtils.isBlank(couponRule.getShortLink())) {
                    String longLink = localH5Domain + MonitorPageUtil.H5_WECHAT_URL + "?id=" + couponRule.getId()+"&type="+ WeixinUrlLinkTypeEnum.COUPON.getCode();
                    String shortLink = baiduManager.getShortLink(longLink, BaiduLinkTypeEnum.ONE_YEAR.getCode());
                    CouponRule updateRule = new CouponRule();
                    updateRule.setId(couponRule.getId());
                    updateRule.setShortLink(shortLink);
                    updateRule.setUpdateTime(couponRule.getUpdateTime());
                    couponRuleDao.updateByIdSelective(updateRule);
                }
            }
        }
        return ResponseFactory.success();
    }
}