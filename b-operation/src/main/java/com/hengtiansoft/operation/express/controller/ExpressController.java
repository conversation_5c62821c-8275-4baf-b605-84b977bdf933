package com.hengtiansoft.operation.express.controller;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.order.entity.vo.ExpressNameVO;
import com.hengtiansoft.order.service.impl.ExpressServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "物流")
@RestController
@RequestMapping("/express")
public class ExpressController {

    @Autowired
    private ExpressServiceImpl expressService;

    @ApiOperation(value = "识别物流名称")
    @GetMapping("/queryExpressName")
    public Response<String> queryExpressName(@RequestParam String expressCode) {
        return ResponseFactory.success(expressService.queryExpressName(expressCode));
    }

    @ApiOperation(value = "搜索物流名称")
    @GetMapping("/searchExpressName")
    public Response<List<ExpressNameVO>> searchExpressName(@RequestParam(required = false) String name) {
        return ResponseFactory.success(expressService.searchExpressName(name));
    }
}
