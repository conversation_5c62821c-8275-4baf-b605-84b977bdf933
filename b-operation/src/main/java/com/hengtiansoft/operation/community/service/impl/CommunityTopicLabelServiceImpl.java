package com.hengtiansoft.operation.community.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelOrderDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelSaveDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelStatusDTO;
import com.hengtiansoft.operation.community.service.CommunityTopicLabelService;
import com.hengtiansoft.operation.community.vo.CommunityTopicLabelExcelVO;
import com.hengtiansoft.operation.community.vo.CommunityTopicLabelVO;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.dao.CommunityPostTopicLabelRelationDao;
import com.hengtiansoft.user.entity.dto.CommunityTopicLabelListDTO;
import com.hengtiansoft.user.entity.po.CommunityPostTopicLabelRelation;
import com.hengtiansoft.user.entity.po.CommunityTopicLabel;
import com.hengtiansoft.user.manager.CommunityTopicLabelManager;
import com.hengtiansoft.user.util.CommunityTopicLabelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 社区话题标签管理Service实现
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Slf4j
@Service
public class CommunityTopicLabelServiceImpl implements CommunityTopicLabelService {

    @Resource
    private CommunityTopicLabelManager communityTopicLabelManager;
    @Resource
    private CommunityPostTopicLabelRelationDao communityPostTopicLabelRelationDao;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private Executor labelTask;
    @Resource
    private RedisOperation redisOperation;
    @Value("${local.tmp}")
    private String localDir;

    @Override
    public PageVO<CommunityTopicLabelVO> getTopicLabelList(CommunityTopicLabelListDTO dto) {
        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 查询数据
        List<CommunityTopicLabel> labelList = communityTopicLabelManager.findByCondition(dto);

        if (CollectionUtils.isEmpty(labelList)) {
            return PageUtils.emptyPage(dto);
        }

        // 转换为VO
        List<CommunityTopicLabelVO> voList = labelList.stream().map(this::convertToVO).collect(Collectors.toList());

        // 构建分页结果
        return PageUtils.toPageVO(PageUtils.extract(labelList), voList);
    }

    @Override
    public void exportTopicLabelList(CommunityTopicLabelListDTO dto) {
        log.info("导出社区话题标签列表，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(FileExportCenterEnum.community_topic_label.getCode());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }

        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(FileExportCenterEnum.community_topic_label.getCode());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(user.getUserName());

        dataExportTaskDao.saveOne(dataExportTask);

        // 异步执行导出任务
        labelTask.execute(() -> {
            try {
                exportTopicLabelListTask(dto, dataExportTask);
            } catch (Exception e) {
                log.error("导出社区话题标签列表异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTopicLabel(CommunityTopicLabelSaveDTO dto) {
        log.info("保存社区话题标签，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 检查标签名是否已存在
        int count = communityTopicLabelManager.countByLabelName(dto.getLabelName(), dto.getId());
        if (count > 0) {
            throw new BusinessException("标签名称已存在");
        }

        // 获取当前操作人
        String operater = user.getUserName();

        if (dto.getId() == null) {
            // 新建标签
            CommunityTopicLabel label = new CommunityTopicLabel();
            label.setLabelName(dto.getLabelName());
            label.setPostCount(0);
            label.setParticipantCount(0);
            label.setType(1); // 1-运营创建
            label.setStatus(0); // 0-未上线
            label.setOperater(operater);
            label.setCreateTime(new Date());
            label.setUpdateTime(new Date());
            label.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());

            // 检查是否存在排序值为1的记录
            List<CommunityTopicLabel> existingLabels = communityTopicLabelManager.findBySortOrder(1);
            if (!CollectionUtils.isEmpty(existingLabels)) {
                // 如果存在排序值为1的记录，将所有记录的排序值往后移动1位
                log.info("存在排序值为1的记录，将所有记录的排序值往后移动1位");
                communityTopicLabelManager.updateSortOrderGreaterThanOrEqual(1);
            }

            // 设置新标签的排序值为1
            label.setSortOrder(1);

            communityTopicLabelManager.insert(label);

            redisOperation.del(CommunityTopicLabelUtil.SELECTABLE_LABELS_CACHE_KEY);
            log.info("发布标签创建事件，标签ID: {}", label.getId());
        } else {
            // 编辑标签
            CommunityTopicLabel label = communityTopicLabelManager.findById(dto.getId());
            if (label == null || DeleteFlagEnum.IS_DELETE.getCode().equals(label.getDelflag())) {
                throw new BusinessException("标签不存在");
            }

            CommunityTopicLabel update = new CommunityTopicLabel();
            update.setId(label.getId());
            update.setLabelName(dto.getLabelName());
            update.setOperater(operater);
            update.setUpdateTime(new Date());

            communityTopicLabelManager.update(update);

            redisOperation.del(CommunityTopicLabelUtil.SELECTABLE_LABELS_CACHE_KEY);
            log.info("发布标签更新事件，标签ID: {}", label.getId());
        }
    }

    @Override
    public void updateTopicLabelStatus(CommunityTopicLabelStatusDTO dto) {
        log.info("更新社区话题标签状态，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 查询标签
        CommunityTopicLabel label = communityTopicLabelManager.findById(dto.getId());
        if (label == null || DeleteFlagEnum.IS_DELETE.getCode().equals(label.getDelflag())) {
            throw new BusinessException("标签不存在");
        }

        // 如果当前状态与目标状态相同，则不需要更新
        if (dto.getStatus().equals(label.getStatus())) {
            return;
        }

        // 更新状态
        label.setStatus(dto.getStatus());
        label.setOperater(user.getUserName());
        label.setUpdateTime(new Date());
        if(Objects.equals(0, dto.getStatus())){
            label.setEndTime(new Date());
        }else if(Objects.equals(1, dto.getStatus())){
            label.setStartTime(new Date());
        }
        communityTopicLabelManager.update(label);

        redisOperation.del(CommunityTopicLabelUtil.SELECTABLE_LABELS_CACHE_KEY);
        log.info("发布标签状态变更事件，标签ID: {}, 状态: {}", label.getId(), label.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTopicLabelOrder(CommunityTopicLabelOrderDTO dto) {
        log.info("更新社区话题标签排序，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(com.hengtiansoft.security.util.UserUtil.getDetails(), "请登录后重试！");

        // 查询标签
        CommunityTopicLabel label = communityTopicLabelManager.findById(dto.getId());
        if (label == null || DeleteFlagEnum.IS_DELETE.getCode().equals(label.getDelflag())) {
            throw new BusinessException("标签不存在");
        }

        // 如果当前排序与目标排序相同，则不需要更新
        if (dto.getSortOrder().equals(label.getSortOrder())) {
            return;
        }

        // 检查是否存在排序值为1的记录
        List<CommunityTopicLabel> existingLabels = communityTopicLabelManager.findBySortOrder(dto.getSortOrder());
        if (!CollectionUtils.isEmpty(existingLabels)) {
            // 如果存在排序值为dto.getSortOrder()的记录，将所有记录的排序值往后移动1位
            log.info("存在排序值为{}的记录，将所有记录的排序值往后移动1位", dto.getSortOrder());
            communityTopicLabelManager.updateSortOrderGreaterThanOrEqual(dto.getSortOrder());
        }

        // 更新排序
        CommunityTopicLabel update = new CommunityTopicLabel();
        update.setId(dto.getId());
        update.setSortOrder(dto.getSortOrder());
        update.setOperater(user.getUserName());
        update.setUpdateTime(new Date());
        communityTopicLabelManager.update(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTopicLabel(Long id) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 查询推荐位
        CommunityTopicLabel topicLabel = communityTopicLabelManager.findById(id);
        if (topicLabel == null || DeleteFlagEnum.IS_DELETE.getCode().equals(topicLabel.getDelflag())) {
            throw new BusinessException("推荐位不存在");
        }

        int postCount = communityPostTopicLabelRelationDao.countPostsByTopicLabelId(id);
        if(postCount > 0){
            throw new BusinessException("已有关联帖子，无法删除");
        }
        // 逻辑删除
        CommunityTopicLabel update = new CommunityTopicLabel();
        update.setId(id);
        update.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        update.setOperater(user.getUserName());
        update.setUpdateTime(new Date());

        communityTopicLabelManager.update(update);

        CommunityPostTopicLabelRelation update4Po = new CommunityPostTopicLabelRelation();
        update4Po.setDelflag(0);
        update4Po.setUpdateTime(new Date());
        communityPostTopicLabelRelationDao.updateByCommunityTopicLabelId(update4Po, topicLabel.getId());

        redisOperation.del(CommunityTopicLabelUtil.SELECTABLE_LABELS_CACHE_KEY);
        log.info("发布标签删除事件，标签ID: {}", topicLabel.getId());
    }

    /**
     * 将实体转换为VO
     */
    private CommunityTopicLabelVO convertToVO(CommunityTopicLabel label) {
        CommunityTopicLabelVO vo = new CommunityTopicLabelVO();
        BeanUtils.copyProperties(label, vo);
        return vo;
    }

    /**
     * 将实体转换为Excel VO
     */
    private CommunityTopicLabelExcelVO convertToExcelVO(CommunityTopicLabel label) {
        CommunityTopicLabelExcelVO vo = new CommunityTopicLabelExcelVO();
        BeanUtils.copyProperties(label, vo);

        // 设置类型描述
        vo.setTypeDesc(label.getType() == 1 ? "运营创建" : "用户创建");

        // 设置状态描述
        vo.setStatusDesc(label.getStatus() == 0 ? "未上线" : "已上线");

        return vo;
    }

    /**
     * 导出社区话题标签列表任务
     */
    private void exportTopicLabelListTask(CommunityTopicLabelListDTO dto, DataExportTask exportTask) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + java.util.UUID.randomUUID().toString();
        java.io.File localTmpDirectory = new java.io.File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            java.io.File exportFile = new java.io.File(localTmpDirectory, exportTask.getExportName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), CommunityTopicLabelExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

            // 查询所有数据
            dto.setPageNum(1);
            dto.setPageSize(Integer.MAX_VALUE);
            List<CommunityTopicLabel> labelList = communityTopicLabelManager.findByCondition(dto);

            if (!CollectionUtils.isEmpty(labelList)) {
                // 转换为Excel VO
                List<CommunityTopicLabelExcelVO> excelVOList = labelList.stream()
                        .map(this::convertToExcelVO)
                        .collect(java.util.stream.Collectors.toList());

                // 写入Excel
                excelWriter.write(excelVOList, writeSheet);
            }

            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            java.io.FileInputStream input = new java.io.FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input, fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出社区话题标签列表失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (java.io.IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }
}
