package com.hengtiansoft.operation.community.service.impl;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.operation.community.dto.CommunityRuleInfoDTO;
import com.hengtiansoft.operation.community.dto.CommunityRuleSaveDTO;
import com.hengtiansoft.operation.community.service.CommunityRuleService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.dao.CommunitySettingDao;
import com.hengtiansoft.user.entity.po.CommunitySetting;
import com.hengtiansoft.user.manager.CommunityUserManager;
import com.hengtiansoft.user.util.CommunityRuleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 社区规则管理Service实现
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Slf4j
@Service
public class CommunityRuleServiceImpl implements CommunityRuleService {

    @Resource
    private CommunitySettingDao communitySettingDao;
    @Resource
    private CommunityUserManager communityUserManager;


    @Override
    public CommunityRuleInfoDTO getRuleInfo() {
        log.info("获取社区规则及积分配置详情");

        CommunityRuleInfoDTO dto = new CommunityRuleInfoDTO();
        CommunityRuleInfoDTO.PointsConfig pointsConfig = new CommunityRuleInfoDTO.PointsConfig();
        dto.setPointsConfig(pointsConfig);

        // 一次性查询所有配置项
        Map<String, CommunitySetting> settingsMap = communitySettingDao.findByKeys(CommunityRuleUtil.KEYS_COMMUNITY_RULES);

        // 设置社区公约内容
        CommunitySetting rulesSetting = settingsMap.get(CommunityRuleUtil.KEY_COMMUNITY_RULES);
        if (rulesSetting != null) {
            dto.setRulesContent(rulesSetting.getSettingValue());
        }

        // 设置发帖奖励积分
        CommunitySetting postPublishSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_POST_PUBLISH);
        pointsConfig.setPostPublish(postPublishSetting != null ?
                Integer.valueOf(postPublishSetting.getSettingValue()) : 0);

        // 设置发帖每日限制
        CommunitySetting postPublishLimitSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_POST_PUBLISH_LIMIT);
        pointsConfig.setPostPublishLimit(postPublishLimitSetting != null ?
                Integer.valueOf(postPublishLimitSetting.getSettingValue()) : 0);

        // 设置参与话题活动奖励积分
        CommunitySetting topicParticipationSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_TOPIC_PARTICIPATION);
        pointsConfig.setTopicParticipation(topicParticipationSetting != null ?
                Integer.valueOf(topicParticipationSetting.getSettingValue()) : 0);

        // 设置参与话题活动每日限制
        CommunitySetting topicParticipationLimitSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_TOPIC_PARTICIPATION_LIMIT);
        pointsConfig.setTopicParticipationLimit(topicParticipationLimitSetting != null ?
                Integer.valueOf(topicParticipationLimitSetting.getSettingValue()) : 0);

        // 设置置顶帖子奖励积分
        CommunitySetting postTopSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_POST_TOP);
        pointsConfig.setPostTop(postTopSetting != null ?
                Integer.valueOf(postTopSetting.getSettingValue()) : 0);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRule(CommunityRuleSaveDTO dto) {
        log.info("保存社区规则及积分配置，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        Date now = new Date();

        // 保存社区规则
        saveOrUpdateSetting(CommunityRuleUtil.KEY_COMMUNITY_RULES, dto.getRulesContent(), "社区规则内容", user.getUserName(), now);
        // 保存积分配置
        saveOrUpdateSetting(CommunityRuleUtil.KEY_POINTS_POST_PUBLISH, String.valueOf(dto.getPointsConfig().getPostPublish()),
                "发布帖子基础奖励积分", user.getUserName(), now);
        saveOrUpdateSetting(CommunityRuleUtil.KEY_POINTS_POST_PUBLISH_LIMIT, String.valueOf(dto.getPointsConfig().getPostPublishLimit()),
                "发布帖子每日奖励限制", user.getUserName(), now);
        saveOrUpdateSetting(CommunityRuleUtil.KEY_POINTS_TOPIC_PARTICIPATION, String.valueOf(dto.getPointsConfig().getTopicParticipation()),
                "参与话题活动基础奖励积分", user.getUserName(), now);
        saveOrUpdateSetting(CommunityRuleUtil.KEY_POINTS_TOPIC_PARTICIPATION_LIMIT, String.valueOf(dto.getPointsConfig().getTopicParticipationLimit()),
                "参与话题活动每日奖励限制", user.getUserName(), now);
        saveOrUpdateSetting(CommunityRuleUtil.KEY_POINTS_POST_TOP, String.valueOf(dto.getPointsConfig().getPostTop()),
                "置顶用户帖子到话题页奖励积分", user.getUserName(), now);
        //清除缓存
        communityUserManager.clearCommunityRulesCache();
    }



    /**
     * 保存或更新配置项
     */
    private void saveOrUpdateSetting(String key, String value, String description, String operater, Date now) {
        CommunitySetting setting = communitySettingDao.findByKey(key);

        if (setting == null) {
            // 新建配置项
            setting = new CommunitySetting();
            setting.setSettingKey(key);
            setting.setSettingValue(value);
            setting.setDescription(description);
            setting.setOperater(operater);
            setting.setCreateTime(now);
            setting.setUpdateTime(now);
            setting.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode()); // 未删除

            communitySettingDao.insert(setting);
        } else {
            // 更新配置项
            CommunitySetting update = new CommunitySetting();
            update.setId(setting.getId());
            update.setSettingValue(value);
            update.setOperater(operater);
            update.setUpdateTime(now);

            communitySettingDao.updateById(update);
        }
    }
}
