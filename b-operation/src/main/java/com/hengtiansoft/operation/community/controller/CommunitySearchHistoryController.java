package com.hengtiansoft.operation.community.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.user.entity.dto.CommunitySearchHistoryListDTO;
import com.hengtiansoft.operation.community.service.CommunitySearchHistoryService;
import com.hengtiansoft.operation.community.vo.CommunitySearchHistoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 社区搜索记录管理Controller
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Api(tags = "社区搜索记录管理")
@RestController
@RequestMapping("/community/searchHistory")
public class CommunitySearchHistoryController {

    @Resource
    private CommunitySearchHistoryService communitySearchHistoryService;

    @ApiOperation("获取搜索记录列表")
    @PostMapping
    public Response<PageVO<CommunitySearchHistoryVO>> getSearchHistoryList(@RequestBody CommunitySearchHistoryListDTO dto) {
        return ResponseFactory.success(communitySearchHistoryService.getSearchHistoryList(dto));
    }

    @ApiOperation("导出搜索记录列表")
    @PostMapping("/export")
    public Response<Void> exportSearchHistoryList(@RequestBody CommunitySearchHistoryListDTO dto) {
        communitySearchHistoryService.exportSearchHistoryList(dto);
        return ResponseFactory.success();
    }
}
