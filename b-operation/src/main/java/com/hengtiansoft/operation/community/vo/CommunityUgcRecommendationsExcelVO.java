package com.hengtiansoft.operation.community.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * UGC推荐位管理Excel导出VO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位管理Excel导出VO")
@ContentRowHeight(15) // 内容行高
@HeadRowHeight(20) // 表头行高
public class CommunityUgcRecommendationsExcelVO {

    @ExcelProperty("推荐位ID")
    @ColumnWidth(20)
    private Long id;

    @ExcelProperty("推荐位名称")
    @ColumnWidth(20)
    private String positionName;

    @ExcelProperty("排序权重")
    @ColumnWidth(20)
    private Integer sortOrder;

    @ExcelProperty("展示开始时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date startTime;

    @ExcelProperty("展示结束时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date endTime;

    @ExcelProperty("状态")
    @ColumnWidth(20)
    private String statusDesc;

    @ExcelProperty("关联类型")
    @ColumnWidth(20)
    private String typeDesc;

    @ExcelProperty("更新时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date updateTime;
}
