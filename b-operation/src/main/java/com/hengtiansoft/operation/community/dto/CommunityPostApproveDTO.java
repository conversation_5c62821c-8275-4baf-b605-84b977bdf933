package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 社区帖子批量通过DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区帖子批量通过DTO")
public class CommunityPostApproveDTO {

    @NotEmpty(message = "帖子ID列表不能为空")
    @ApiModelProperty(value = "要通过的帖子ID列表", required = true)
    private List<Long> postIds;
}
