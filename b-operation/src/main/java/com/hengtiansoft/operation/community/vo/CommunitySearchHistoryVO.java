package com.hengtiansoft.operation.community.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 社区搜索记录VO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区搜索记录VO")
public class CommunitySearchHistoryVO {

    @ApiModelProperty("记录ID")
    private Long id;

    @ApiModelProperty("搜索内容")
    private String content;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("搜索时间")
    private Date searchTime;
}
