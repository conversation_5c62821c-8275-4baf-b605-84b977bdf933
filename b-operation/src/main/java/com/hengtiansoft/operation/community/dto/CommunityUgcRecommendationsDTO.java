package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * UGC推荐位管理DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位管理DTO")
public class CommunityUgcRecommendationsDTO {

    @ApiModelProperty("推荐位ID (编辑时提供，新建时为null或不传)")
    private Long id;

    @NotBlank(message = "推荐位名称不能为空")
    @ApiModelProperty(value = "推荐位名称 (1-6字)", required = true)
    private String positionName;

    @NotNull(message = "排序权重不能为空")
    @ApiModelProperty(value = "排序权重 (整数)", required = true)
    private Integer sortOrder;

    @NotNull(message = "展示开始时间不能为空")
    @ApiModelProperty(value = "展示开始时间", required = true)
    private Date startTime;

    @NotNull(message = "展示结束时间不能为空")
    @ApiModelProperty(value = "展示结束时间", required = true)
    private Date endTime;

    @ApiModelProperty(value = "状态 1-未上线 2-已上线 3-已结束", required = true)
    private Integer status;

    @NotNull(message = "关联类型不能为空")
    @ApiModelProperty(value = "关联类型 (1-默认, 2-话题活动)", required = true)
    private Integer type;

    @ApiModelProperty("关联话题活动ID (当type=2时必需)")
    private Long communityTopicActivityId;

    @ApiModelProperty("人群限制 (1-不限, 2-新用户, 3-会员, 4-其他)")
    private Integer peopleLimit;

    @ApiModelProperty("会员等级 (当peopleLimit=3时)")
    private String grade;

    @ApiModelProperty("标签ID (当peopleLimit=4时)")
    private List<Long> labelIds;
}
