package com.hengtiansoft.operation.community.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.community.dto.CommunityPostApproveDTO;
import com.hengtiansoft.operation.community.dto.CommunityPostExportDTO;
import com.hengtiansoft.user.entity.dto.CommunityPostListDTO;
import com.hengtiansoft.operation.community.dto.CommunityPostRejectDTO;
import com.hengtiansoft.user.entity.vo.CommunityPostVO;

/**
 * 社区帖子管理Service
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface CommunityPostService {

    /**
     * 获取帖子列表
     *
     * @param dto 查询参数
     * @return 帖子列表分页结果
     */
    PageVO<CommunityPostVO> getPostList(CommunityPostListDTO dto);

    /**
     * 获取帖子详情
     *
     * @param postId 帖子ID
     * @return 帖子详情
     */
    CommunityPostVO getPostInfo(Long postId);

    /**
     * 导出帖子列表
     *
     * @param dto 导出参数
     */
    void exportPostList(CommunityPostExportDTO dto);

    /**
     * 批量通过帖子
     *
     * @param dto 批量通过参数
     */
    void approvePosts(CommunityPostApproveDTO dto);

    /**
     * 批量驳回帖子
     *
     * @param dto 批量驳回参数
     */
    void rejectPosts(CommunityPostRejectDTO dto);

    /**
     * 删除帖子
     *
     * @param postId 帖子ID
     */
    void deletePost(Long postId);
}
