package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * UGC推荐位关联置顶帖DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位关联置顶帖DTO")
public class CommunityUgcRecommendationsPinDTO {

    @NotNull(message = "推荐位ID不能为空")
    @ApiModelProperty(value = "推荐位ID", required = true)
    private Long id;

    @ApiModelProperty("要置顶的帖子ID (取消关联时不传)")
    private Long postId;
}
