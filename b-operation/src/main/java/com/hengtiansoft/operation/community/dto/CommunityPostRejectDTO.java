package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 社区帖子批量驳回DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区帖子批量驳回DTO")
public class CommunityPostRejectDTO {

    @ApiModelProperty(value = "要驳回的帖子列表")
    private List<RejectInfo> rejectList;


    @Data
    public static class RejectInfo {

        @ApiModelProperty(value = "要驳回的帖子ID")
        private Long postId;

        @ApiModelProperty("驳回理由")
        private String moderationRemark;
    }
}
