package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 社区用户导出DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区用户导出DTO")
public class CommunityUserExportDTO {

    @ApiModelProperty("用户ID精确匹配")
    private Long userId;

    @ApiModelProperty("手机号精确匹配")
    private String phone;

    @ApiModelProperty("昵称模糊匹配")
    private String nickName;

    @ApiModelProperty("用户状态 (1-正常, 2-禁言)")
    private Integer status;

    @ApiModelProperty("成为社区用户时间-开始")
    private Date minAgreedCommunityRuleTime;

    @ApiModelProperty("成为社区用户时间-结束")
    private Date maxAgreedCommunityRuleTime;

    @ApiModelProperty("会员等级")
    private Integer grade;

    @ApiModelProperty("社区消费频次")
    private Integer communityOrderCount;
}
