package com.hengtiansoft.operation.community.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.community.dto.*;
import com.hengtiansoft.operation.community.vo.CommunityUgcRecommendationsVO;

/**
 * UGC推荐位管理Service
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface CommunityUgcRecommendationsService {

    /**
     * 获取UGC推荐位列表
     *
     * @param dto 查询参数
     * @return 推荐位列表分页结果
     */
    PageVO<CommunityUgcRecommendationsVO> getRecommendationsList(CommunityUgcRecommendationsListDTO dto);

    /**
     * 导出UGC推荐位列表
     *
     * @param dto 导出参数
     */
    void exportRecommendationsList(CommunityUgcRecommendationsExportDTO dto);

    /**
     * 关联置顶帖到推荐位/取消关联
     *
     * @param dto 关联参数
     */
    void pinRecommendation(CommunityUgcRecommendationsPinDTO dto);

    /**
     * 保存或编辑UGC推荐位
     *
     * @param dto 保存参数
     */
    void saveRecommendation(CommunityUgcRecommendationsDTO dto);

    /**
     * UGC推荐位排序
     *
     * @param dto 排序参数
     */
    void orderRecommendation(CommunityUgcRecommendationsOrderDTO dto);

    /**
     * UGC推荐位下线状态变更
     *
     * @param dto 状态变更参数
     */
    void updateRecommendationStatus(CommunityUgcRecommendationsStatusDTO dto);

    /**
     * 删除UGC推荐位
     *
     * @param id 要删除的推荐位ID
     */
    void deleteRecommendation(Long id);

    /**
     * 获取UGC推荐位详情
     *
     * @param id 推荐位ID
     * @return 推荐位详情
     */
    CommunityUgcRecommendationsVO getRecommendationInfo(Long id);
}
