package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * UGC推荐位管理导出DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位管理导出DTO")
public class CommunityUgcRecommendationsExportDTO {

    @ApiModelProperty("推荐位ID精确匹配")
    private Long id;

    @ApiModelProperty("推荐位名称模糊匹配")
    private String positionName;

    @ApiModelProperty("状态 1-未上线 2-已上线 3-已结束")
    private Integer status;
}
