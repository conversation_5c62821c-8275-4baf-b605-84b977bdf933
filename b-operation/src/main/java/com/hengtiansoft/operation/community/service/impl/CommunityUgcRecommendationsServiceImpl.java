package com.hengtiansoft.operation.community.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.CommonOptUtil;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.operation.community.dto.*;
import com.hengtiansoft.operation.community.service.CommunityUgcRecommendationsService;
import com.hengtiansoft.operation.community.vo.CommunityUgcRecommendationsExcelVO;
import com.hengtiansoft.operation.community.vo.CommunityUgcRecommendationsVO;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.dao.CommunityPostDao;
import com.hengtiansoft.user.dao.CommunityUgcRecommendationsDao;
import com.hengtiansoft.user.entity.mapper.CommunityUgcRecommendationsMapper;
import com.hengtiansoft.user.entity.po.CommunityPost;
import com.hengtiansoft.user.entity.po.CommunityUgcRecommendations;
import com.hengtiansoft.user.enums.CommunityUgcRecommendationsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * UGC推荐位管理Service实现
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Slf4j
@Service
public class CommunityUgcRecommendationsServiceImpl implements CommunityUgcRecommendationsService {

    @Resource
    private CommunityUgcRecommendationsMapper communityUgcRecommendationsMapper;
    @Resource
    private CommunityUgcRecommendationsDao communityUgcRecommendationsDao;
    @Resource
    private CommunityPostDao communityPostDao;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private Executor labelTask;

    @Value("${local.tmp}")
    private String localDir;

    @Override
    public PageVO<CommunityUgcRecommendationsVO> getRecommendationsList(CommunityUgcRecommendationsListDTO dto) {
        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 构建查询条件
        Condition condition = new Condition(CommunityUgcRecommendations.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());

        // 添加查询条件
        if (dto.getId() != null) {
            criteria.andEqualTo("id", dto.getId());
        }
        if (StringUtils.isNotBlank(dto.getPositionName())) {
            criteria.andLike("positionName", "%" + dto.getPositionName() + "%");
        }
        if (dto.getStatus() != null) {
            criteria.andEqualTo("status", dto.getStatus());
        }

        // 排序
        condition.setOrderByClause("sort_order ASC, update_time DESC");

        // 执行查询
        List<CommunityUgcRecommendations> recommendations = communityUgcRecommendationsMapper.selectByExample(condition);

        if (CollectionUtils.isEmpty(recommendations)) {
            return PageUtils.emptyPage(dto);
        }

        // 转换为VO
        List<CommunityUgcRecommendationsVO> voList = recommendations.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建分页结果
        PageVO<CommunityUgcRecommendationsVO> pageVO = new PageVO<>();
        Page<CommunityUgcRecommendations> page = (Page<CommunityUgcRecommendations>) recommendations;
        pageVO.setList(voList);
        pageVO.setPageNum(page.getPageNum());
        pageVO.setPageSize(page.getPageSize());
        pageVO.setTotal(page.getTotal());
        pageVO.setPages(page.getPages());

        return pageVO;
    }

    @Override
    public void exportRecommendationsList(CommunityUgcRecommendationsExportDTO dto) {
        log.info("导出UGC推荐位列表，参数：{}", dto);
        UserDetailDTO detailDTO = UserUtil.getDetails();
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(FileExportCenterEnum.community_ugc_recommendations.getCode());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }

        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSON.toJSONString(dto));
        dataExportTask.setExportType(FileExportCenterEnum.community_ugc_recommendations.getCode());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());

        // 转换DTO为ListDTO用于查询
        CommunityUgcRecommendationsListDTO listDTO = new CommunityUgcRecommendationsListDTO();
        listDTO.setId(dto.getId());
        listDTO.setPositionName(dto.getPositionName());
        listDTO.setStatus(dto.getStatus());
        listDTO.setPageNum(1);
        listDTO.setPageSize(Integer.MAX_VALUE);

        dataExportTaskDao.saveOne(dataExportTask);

        // 异步执行导出任务
        labelTask.execute(() -> {
            try {
                exportRecommendationsListTask(listDTO, dataExportTask);
            } catch (Exception e) {
                log.error("导出UGC推荐位列表异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }

    /**
     * 异步导出UGC推荐位列表任务
     *
     * @param dto 查询参数
     * @param exportTask 导出任务
     */
    private void exportRecommendationsListTask(CommunityUgcRecommendationsListDTO dto, DataExportTask exportTask) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        File localTmpDirectory = new File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            File exportFile = new File(localTmpDirectory, exportTask.getExportName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), CommunityUgcRecommendationsExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();

            // 构建查询条件
            Condition condition = new Condition(CommunityUgcRecommendations.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());

            // 添加查询条件
            if (dto.getId() != null) {
                criteria.andEqualTo("id", dto.getId());
            }
            if (StringUtils.isNotBlank(dto.getPositionName())) {
                criteria.andLike("positionName", "%" + dto.getPositionName() + "%");
            }
            if (dto.getStatus() != null) {
                criteria.andEqualTo("status", dto.getStatus());
            }

            // 排序
            condition.setOrderByClause("sort_order ASC, update_time DESC");

            // 执行查询
            List<CommunityUgcRecommendations> recommendations = communityUgcRecommendationsMapper.selectByExample(condition);

            if (CollectionUtils.isEmpty(recommendations)) {
                dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
                dataExportTask.setUpdateTime(new Date());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
                return;
            }

            // 转换为ExcelVO
            List<CommunityUgcRecommendationsExcelVO> excelVOList = recommendations.stream()
                    .map(this::convertToExcelVO)
                    .collect(Collectors.toList());

            // 写入Excel
            excelWriter.write(excelVOList, EasyExcel.writerSheet("UGC推荐位列表")
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build());

            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input, fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出UGC推荐位列表失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTask.setUpdateTime(new Date());
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pinRecommendation(CommunityUgcRecommendationsPinDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 查询推荐位
        CommunityUgcRecommendations recommendation = communityUgcRecommendationsDao.findById(dto.getId());
        if (recommendation == null || DeleteFlagEnum.IS_DELETE.getCode().equals(recommendation.getDelflag())) {
            throw new BusinessException("推荐位不存在");
        }

        // 如果提供了帖子ID，则验证帖子是否存在
        if (dto.getPostId() != null) {
            CommunityPost post = communityPostDao.findById(dto.getPostId());
            if (post == null || DeleteFlagEnum.IS_DELETE.getCode().equals(post.getDelflag())) {
                throw new BusinessException("帖子不存在");
            }

            // 检查帖子状态是否已发布
            if (post.getStatus() != 1) {
                throw new BusinessException("只能置顶已发布的帖子");
            }
        }

        // 更新推荐位的置顶帖
        CommunityUgcRecommendations update = new CommunityUgcRecommendations();
        update.setPositionName(recommendation.getPositionName());
        update.setSortOrder(recommendation.getSortOrder());
        update.setStartTime(recommendation.getStartTime());
        update.setEndTime(recommendation.getEndTime());
        update.setStatus(recommendation.getStatus());
        update.setType(recommendation.getType());
        update.setCommunityTopicActivityId(recommendation.getCommunityTopicActivityId());
        update.setPeopleLimit(recommendation.getPeopleLimit());
        update.setGrade(recommendation.getGrade());
        update.setLabelId(recommendation.getLabelId());
        update.setCreateTime(recommendation.getCreateTime());
        update.setDelflag(recommendation.getDelflag());


        update.setId(recommendation.getId());
        // 显式设置topPostId，即使是null也要更新
        update.setTopPostId(dto.getPostId());
        update.setOperater(user.getUserName());
        update.setUpdateTime(new Date());
        communityUgcRecommendationsDao.updateAllById(update);
    }

    /**
     * 验证推荐位名称
     *
     * @param dto 推荐位数据
     */
    private void validatePositionName(CommunityUgcRecommendationsDTO dto) {
        if (StringUtils.isBlank(dto.getPositionName()) || dto.getPositionName().length() > 6) {
            throw new BusinessException("推荐位名称长度应为1-6个字符");
        }
    }

    /**
     * 验证展示时间
     *
     * @param dto 推荐位数据
     */
    private void validateShowTime(CommunityUgcRecommendationsDTO dto) {
        if (dto.getStartTime() == null || dto.getEndTime() == null) {
            throw new BusinessException("展示开始时间和结束时间不能为空");
        }

        if (dto.getStartTime().after(dto.getEndTime())) {
            throw new BusinessException("展示开始时间不能晚于结束时间");
        }
    }

    /**
     * 验证关联类型
     *
     * @param dto 推荐位数据
     */
    private void validateRelationType(CommunityUgcRecommendationsDTO dto) {
        // 验证关联话题活动
        if (dto.getType() == 2 && dto.getCommunityTopicActivityId() == null) {
            throw new BusinessException("关联话题活动时，必须提供话题活动ID");
        }

        // 验证默认类型的唯一性
        if (dto.getType() == 1) {
            // 查询是否已存在未删除的默认类型推荐位，排除当前记录
            List<CommunityUgcRecommendations> defaultTypeRecommendations = communityUgcRecommendationsDao.findDefaultTypeExcludeId(dto.getId(), Arrays.asList(1, 2));
            if (!CollectionUtils.isEmpty(defaultTypeRecommendations)) {
                throw new BusinessException("只允许存在一条默认类型的推荐位数据");
            }
        }
    }

    /**
     * 验证人群限制
     *
     * @param dto 推荐位数据
     */
    private void validatePeopleLimit(CommunityUgcRecommendationsDTO dto) {
        // 验证会员等级
        if (dto.getPeopleLimit() != null && dto.getPeopleLimit() == 3 && StringUtils.isBlank(dto.getGrade())) {
            throw new BusinessException("人群限制为会员时，必须提供会员等级");
        }

        // 验证标签ID
        if (dto.getPeopleLimit() != null && dto.getPeopleLimit() == 4 && CollectionUtils.isEmpty(dto.getLabelIds())) {
            throw new BusinessException("人群限制为其他时，必须提供标签ID");
        }
    }

    /**
     * 验证排序值并处理排序冲突
     *
     * @param dto 推荐位数据
     */
    private void validateAndHandleSortOrder(CommunityUgcRecommendationsDTO dto) {
        // 验证排序值
        if (dto.getSortOrder() == null) {
            throw new BusinessException("排序权重不能为空");
        }

        if (dto.getSortOrder() < 0) {
            throw new BusinessException("排序权重不能为负数");
        }

        // 检查是否存在相同排序值的记录
        List<CommunityUgcRecommendations> existingRecommendations = communityUgcRecommendationsDao.findBySortOrder(dto.getSortOrder());
        if (CollectionUtils.isNotEmpty(existingRecommendations)) {
            // 如果是编辑操作，且找到的记录就是当前记录，则不需要处理
            if (dto.getId() != null && dto.getId().equals(existingRecommendations.get(0).getId())) {
                // 排序值没有变化，不需要处理
            } else {
                // 存在相同排序值的其他记录，将所有记录的排序值往后移动1位
                log.info("存在排序值为{}的记录，将所有记录的排序值往后移动1位", dto.getSortOrder());
                communityUgcRecommendationsDao.updateSortOrderGreaterThanOrEqual(dto.getSortOrder());
            }
        }
    }

    /**
     * 处理标签ID，将List转为逗号分隔的字符串
     *
     * @param labelIds 标签ID列表
     * @return 逗号分隔的标签ID字符串
     */
    private String processLabelIds(List<Long> labelIds) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return null;
        }
        return labelIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRecommendation(CommunityUgcRecommendationsDTO dto) {
        UserDetailDTO userDetail = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 执行各项验证
        validatePositionName(dto);
        validateShowTime(dto);
        validateRelationType(dto);
        validatePeopleLimit(dto);
        validateAndHandleSortOrder(dto);

        Date now = new Date();

        // 处理标签ID
        String labelIdStr = processLabelIds(dto.getLabelIds());
        dto.setStatus(CommonOptUtil.getStatus(dto.getStartTime(), dto.getEndTime()).getCode());
        if (dto.getId() == null) {
            // 新增
            CommunityUgcRecommendations recommendation = new CommunityUgcRecommendations();
            recommendation.setPositionName(dto.getPositionName());
            recommendation.setSortOrder(dto.getSortOrder());
            recommendation.setStartTime(dto.getStartTime());
            recommendation.setEndTime(dto.getEndTime());
            recommendation.setStatus(dto.getStatus());
            recommendation.setType(dto.getType());
            recommendation.setCommunityTopicActivityId(dto.getCommunityTopicActivityId());
            recommendation.setPeopleLimit(dto.getPeopleLimit());
            recommendation.setGrade(dto.getGrade());
            recommendation.setLabelId(labelIdStr);
            recommendation.setOperater(userDetail.getUserName());
            recommendation.setCreateTime(now);
            recommendation.setUpdateTime(now);
            recommendation.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());

            communityUgcRecommendationsDao.insert(recommendation);
        } else {
            // 编辑
            CommunityUgcRecommendations existingRecommendation = communityUgcRecommendationsDao.findById(dto.getId());
            if (existingRecommendation == null || DeleteFlagEnum.IS_DELETE.getCode().equals(existingRecommendation.getDelflag())) {
                throw new BusinessException("推荐位不存在");
            }

            CommunityUgcRecommendations update = new CommunityUgcRecommendations();
            update.setId(dto.getId());
            update.setPositionName(dto.getPositionName());
            update.setSortOrder(dto.getSortOrder());
            update.setStartTime(dto.getStartTime());
            update.setEndTime(dto.getEndTime());
            update.setStatus(dto.getStatus());
            update.setType(dto.getType());
            update.setCommunityTopicActivityId(dto.getCommunityTopicActivityId());
            update.setPeopleLimit(dto.getPeopleLimit());
            update.setGrade(dto.getGrade());
            update.setLabelId(labelIdStr);
            update.setOperater(userDetail.getUserName());
            update.setUpdateTime(now);

            communityUgcRecommendationsDao.updateById(update);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderRecommendation(CommunityUgcRecommendationsOrderDTO dto) {
        log.info("更新UGC推荐位排序，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 查询推荐位
        CommunityUgcRecommendations recommendation = communityUgcRecommendationsDao.findById(dto.getId());
        if (recommendation == null || DeleteFlagEnum.IS_DELETE.getCode().equals(recommendation.getDelflag())) {
            throw new BusinessException("推荐位不存在");
        }

        // 如果当前排序与目标排序相同，则不需要更新
        if (dto.getSortOrder().equals(recommendation.getSortOrder())) {
            return;
        }

        // 检查是否存在排序值为dto.getSortOrder()的记录
        List<CommunityUgcRecommendations> existingRecommendations = communityUgcRecommendationsDao.findBySortOrder(dto.getSortOrder());
        if (!CollectionUtils.isEmpty(existingRecommendations)) {
            // 如果存在排序值为dto.getSortOrder()的记录，将所有记录的排序值往后移动1位
            log.info("存在排序值为{}的记录，将所有记录的排序值往后移动1位", dto.getSortOrder());
            communityUgcRecommendationsDao.updateSortOrderGreaterThanOrEqual(dto.getSortOrder());
        }

        // 更新排序权重
        CommunityUgcRecommendations update = new CommunityUgcRecommendations();
        update.setId(recommendation.getId());
        update.setSortOrder(dto.getSortOrder());
        update.setOperater(user.getUserName());
        update.setUpdateTime(new Date());

        communityUgcRecommendationsDao.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecommendationStatus(CommunityUgcRecommendationsStatusDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 查询推荐位
        CommunityUgcRecommendations recommendation = communityUgcRecommendationsDao.findById(dto.getId());
        if (recommendation == null || DeleteFlagEnum.IS_DELETE.getCode().equals(recommendation.getDelflag())) {
            throw new BusinessException("推荐位不存在");
        }

        // 验证状态值
        if (dto.getStatus() != 3) {
            throw new BusinessException("状态值无效，只能设置为下线");
        }

        // 更新状态
        Date now = new Date();
        CommunityUgcRecommendations update = new CommunityUgcRecommendations();
        update.setId(recommendation.getId());
        update.setStatus(dto.getStatus());
        update.setEndTime(now);
        update.setOperater(user.getUserName());
        update.setUpdateTime(now);
        communityUgcRecommendationsDao.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecommendation(Long id) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        // 查询推荐位
        CommunityUgcRecommendations recommendation = communityUgcRecommendationsDao.findById(id);
        if (recommendation == null || DeleteFlagEnum.IS_DELETE.getCode().equals(recommendation.getDelflag())) {
            throw new BusinessException("推荐位不存在");
        }

        // 逻辑删除
        CommunityUgcRecommendations update = new CommunityUgcRecommendations();
        update.setId(id);
        update.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        update.setOperater(user.getUserName());
        update.setUpdateTime(new Date());

        communityUgcRecommendationsDao.updateById(update);
    }

    @Override
    public CommunityUgcRecommendationsVO getRecommendationInfo(Long id) {
        // 查询推荐位
        CommunityUgcRecommendations recommendation = communityUgcRecommendationsDao.findById(id);
        if (recommendation == null || DeleteFlagEnum.IS_DELETE.getCode().equals(recommendation.getDelflag())) {
            throw new BusinessException("推荐位不存在");
        }

        return convertToVO(recommendation);
    }

    /**
     * 将PO转换为VO
     *
     * @param po 推荐位PO
     * @return 推荐位VO
     */
    private CommunityUgcRecommendationsVO convertToVO(CommunityUgcRecommendations po) {
        if (po == null) {
            return null;
        }

        CommunityUgcRecommendationsVO vo = new CommunityUgcRecommendationsVO();
        BeanUtils.copyProperties(po, vo);
        vo.setPostId(po.getTopPostId());
        // 处理标签ID，将逗号分隔的字符串转为List
        if (StringUtils.isNotBlank(po.getLabelId())) {
            List<Long> labelIds = Arrays.stream(po.getLabelId().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            vo.setLabelIds(labelIds);
        }

        return vo;
    }

    /**
     * 将PO转换为ExcelVO
     *
     * @param po 推荐位PO
     * @return 推荐位ExcelVO
     */
    private CommunityUgcRecommendationsExcelVO convertToExcelVO(CommunityUgcRecommendations po) {
        if (po == null) {
            return null;
        }

        CommunityUgcRecommendationsExcelVO vo = new CommunityUgcRecommendationsExcelVO();
        BeanUtils.copyProperties(po, vo);

        // 设置状态描述
        if (po.getStatus() != null) {
            try {
                vo.setStatusDesc(CommonActivityStatusEnum.getDescByCode(po.getStatus()));
            } catch (Exception e) {
                vo.setStatusDesc(String.valueOf(po.getStatus()));
            }
        }

        // 设置关联类型描述
        if (po.getType() != null) {
            try {
                vo.setTypeDesc(CommunityUgcRecommendationsTypeEnum.getDescByCode(po.getType()));
            } catch (Exception e) {
                vo.setTypeDesc(String.valueOf(po.getType()));
            }
        }

        return vo;
    }
}
