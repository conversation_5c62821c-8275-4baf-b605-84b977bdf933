package com.hengtiansoft.operation.community.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.user.entity.dto.CommunityTopicLabelListDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelOrderDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelSaveDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelStatusDTO;
import com.hengtiansoft.operation.community.vo.CommunityTopicLabelVO;

/**
 * 社区话题标签管理Service
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface CommunityTopicLabelService {

    /**
     * 获取话题标签列表
     *
     * @param dto 查询参数
     * @return 标签列表分页结果
     */
    PageVO<CommunityTopicLabelVO> getTopicLabelList(CommunityTopicLabelListDTO dto);

    /**
     * 导出话题标签列表
     *
     * @param dto 导出参数
     */
    void exportTopicLabelList(CommunityTopicLabelListDTO dto);

    /**
     * 保存话题标签（新建或编辑）
     *
     * @param dto 保存参数
     */
    void saveTopicLabel(CommunityTopicLabelSaveDTO dto);

    /**
     * 更新话题标签状态
     *
     * @param dto 状态更新参数
     */
    void updateTopicLabelStatus(CommunityTopicLabelStatusDTO dto);

    /**
     * 更新话题标签排序
     *
     * @param dto 排序更新参数
     */
    void updateTopicLabelOrder(CommunityTopicLabelOrderDTO dto);

    /**
     * 删除话题标签
     *
     * @param id
     */
    void deleteTopicLabel(Long id);
}
