package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 社区话题标签保存DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区话题标签保存DTO")
public class CommunityTopicLabelSaveDTO {

    @ApiModelProperty("标签ID (编辑时提供，新建时为null)")
    private Long id;

    @NotBlank(message = "标签名称不能为空")
    @ApiModelProperty(value = "标签名称", required = true)
    private String labelName;
}
