package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 社区规则及积分配置保存DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区规则及积分配置保存DTO")
public class CommunityRuleSaveDTO {

    @NotBlank(message = "社区规则内容不能为空")
    @ApiModelProperty(value = "社区规则内容", required = true)
    private String rulesContent;

    @Valid
    @NotNull(message = "积分配置不能为空")
    @ApiModelProperty(value = "积分配置对象", required = true)
    private PointsConfig pointsConfig;

    @Data
    public static class PointsConfig {
        @NotNull(message = "发布帖子基础奖励积分不能为空")
        @ApiModelProperty(value = "发布帖子基础奖励积分", required = true)
        private Integer postPublish;

        @NotNull(message = "发布帖子每日奖励限制不能为空")
        @ApiModelProperty(value = "发布帖子每日奖励限制", required = true)
        private Integer postPublishLimit;

        @NotNull(message = "参与话题活动基础奖励积分不能为空")
        @ApiModelProperty(value = "参与话题活动基础奖励积分", required = true)
        private Integer topicParticipation;

        @NotNull(message = "参与话题活动每日奖励限制不能为空")
        @ApiModelProperty(value = "参与话题活动每日奖励限制", required = true)
        private Integer topicParticipationLimit;

        @NotNull(message = "置顶用户帖子到话题页奖励积分不能为空")
        @ApiModelProperty(value = "置顶用户帖子到话题页奖励积分", required = true)
        private Integer postTop;


    }
}
