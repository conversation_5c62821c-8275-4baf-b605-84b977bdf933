package com.hengtiansoft.operation.community.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.operation.community.dto.CommunityUserExportDTO;
import com.hengtiansoft.operation.community.dto.CommunityUserSearchHistoryDTO;
import com.hengtiansoft.operation.community.service.CommunityUserService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.dao.CommunitySearchHistoryDao;
import com.hengtiansoft.user.dao.CommunityUserDao;
import com.hengtiansoft.user.dao.CustomerUserMoreDao;
import com.hengtiansoft.user.entity.dto.CommunityUserListDTO;
import com.hengtiansoft.user.entity.po.CommunitySearchHistory;
import com.hengtiansoft.user.entity.po.CommunityUser;
import com.hengtiansoft.user.entity.vo.CommunityUserExcelVO;
import com.hengtiansoft.user.entity.vo.CommunityUserSearchHistoryExcelVO;
import com.hengtiansoft.user.entity.vo.CommunityUserSearchHistoryVO;
import com.hengtiansoft.user.entity.vo.CommunityUserVO;
import com.hengtiansoft.user.enums.UserOrderCntEnum;
import com.hengtiansoft.user.util.CommunityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 社区用户管理Service实现
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Slf4j
@Service
public class CommunityUserServiceImpl implements CommunityUserService {

    @Resource
    private CommunityUserDao communityUserDao;
    @Resource
    private CommunitySearchHistoryDao communitySearchHistoryDao;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private Executor labelTask;
    @Resource
    private CustomerUserMoreDao customerUserMoreDao;
    @Value("${local.tmp}")
    private String localDir;

    @Override
    public PageVO<CommunityUserVO> getUserList(CommunityUserListDTO dto) {
        // 参数
        MutablePair<Integer, Integer> orderCntRange = UserOrderCntEnum.getRange(dto.getCommunityOrderCount());
        dto.setOrderCntMin(orderCntRange.getLeft());
        dto.setOrderCntMax(orderCntRange.getRight());

        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 使用关联查询获取社区用户列表
        List<Long> ids = communityUserDao.findCommunityUserIdsBySelectDb(dto);
        Pagination pagination = PageUtils.extract(ids);
        List<CommunityUserVO> userList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ids)){
            dto.setIds(ids);
            userList = communityUserDao.findCommunityUserList(dto);
        }

        if (CollectionUtils.isEmpty(userList)) {
            return PageUtils.emptyPage(dto);
        }

        // 获取用户ID列表
        List<Long> userIds = userList.stream()
                .map(CommunityUserVO::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 从 customer_user_more 表中获取总消费金额
        Map<Long, BigDecimal> totalAmountMap = customerUserMoreDao.findTotalAmountByUserIds(userIds);

        // 设置总消费金额
        userList.forEach(user -> {
            if (user.getUserId() != null && totalAmountMap.containsKey(user.getUserId())) {
                if (user.getStats() != null) {
                    user.getStats().setTotalAmount(totalAmountMap.get(user.getUserId()));
                }
            }
        });

        // 构建分页结果
        return PageUtils.toPageVO(pagination, userList);
    }

    @Override
    public void exportUserList(CommunityUserExportDTO dto) {
        log.info("导出社区用户列表，参数：{}", JSON.toJSONString(dto));
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(FileExportCenterEnum.community_user.getCode());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }

        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(FileExportCenterEnum.community_user.getCode());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(user.getUserName());

        // 转换DTO为ListDTO用于查询
        CommunityUserListDTO listDTO = new CommunityUserListDTO();
        listDTO.setUserId(dto.getUserId());
        listDTO.setPhone(dto.getPhone());
        listDTO.setNickName(dto.getNickName());
        listDTO.setStatus(dto.getStatus());
        listDTO.setMinAgreedCommunityRuleTime(dto.getMinAgreedCommunityRuleTime());
        listDTO.setMaxAgreedCommunityRuleTime(dto.getMaxAgreedCommunityRuleTime());
        listDTO.setGrade(dto.getGrade());
        listDTO.setCommunityOrderCount(dto.getCommunityOrderCount());

        dataExportTaskDao.saveOne(dataExportTask);

        // 异步执行导出任务
        labelTask.execute(() -> {
            try {
                exportUserListTask(listDTO, dataExportTask);
            } catch (Exception e) {
                log.error("导出社区用户列表异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }

    @Override
    public void banUser(Long id) {
        CommunityUser user = communityUserDao.findById(id);
        if (user == null || Objects.equals(user.getDelflag(), DeleteFlagEnum.IS_DELETE.getCode())) {
            throw new BusinessException("记录不存在");
        }

        if (user.getStatus() == 2) {
            throw new BusinessException("用户已被禁言");
        }

        // 更新用户状态为禁言
        CommunityUser update =new CommunityUser();
        update.setId(user.getId());
        update.setStatus(2);
        update.setBanTime(new Date());
        update.setUpdateTime(new Date());

        communityUserDao.updateById(update);
        log.info("用户{}已禁言", id);
    }

    @Override
    public void unbanUser(Long id) {
        CommunityUser user = communityUserDao.findById(id);
        if (user == null || Objects.equals(user.getDelflag(), DeleteFlagEnum.IS_DELETE.getCode())) {
            throw new BusinessException("记录不存在");
        }

        if (user.getStatus() != 2) {
            throw new BusinessException("用户未被禁言");
        }

        // 更新用户状态为正常
        CommunityUser update =new CommunityUser();
        update.setId(user.getId());
        update.setStatus(1);
        update.setBanTime(null);
        update.setUpdateTime(new Date());

        communityUserDao.updateById(update);
        log.info("用户{}已解除禁言", id);
    }

    @Override
    public CommunityUserVO getUserInfo(Long id) {
        CommunityUserVO userVO = communityUserDao.findCommunityUserDetail(id);
        if (userVO == null) {
            throw new BusinessException("用户不存在");
        }

        // 从 customer_user_more 表中获取总消费金额
        BigDecimal totalAmount = customerUserMoreDao.findTotalAmountByUserId(userVO.getUserId());
        if (totalAmount != null && userVO.getStats() != null) {
            userVO.getStats().setTotalAmount(totalAmount);
        }

        return userVO;
    }

    @Override
    public PageVO<CommunityUserSearchHistoryVO> getUserSearchHistory(CommunityUserSearchHistoryDTO dto) {
        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 查询用户搜索历史
        List<CommunitySearchHistory> historyList = communitySearchHistoryDao.findByUserId(dto.getUserId());

        if (CollectionUtils.isEmpty(historyList)) {
            return PageUtils.emptyPage(dto);
        }

        // 转换为VO
        List<CommunityUserSearchHistoryVO> voList = historyList.stream()
                .map(this::convertToSearchHistoryVO)
                .collect(Collectors.toList());

        // 构建分页结果
        PageVO<CommunityUserSearchHistoryVO> pageVO = new PageVO<>();
        Page<CommunitySearchHistory> page = (Page<CommunitySearchHistory>) historyList;
        pageVO.setList(voList);
        pageVO.setPageNum(page.getPageNum());
        pageVO.setPageSize(page.getPageSize());
        pageVO.setTotal(page.getTotal());
        pageVO.setPages(page.getPages());

        return pageVO;
    }

    @Override
    public void exportUserSearchHistory(CommunityUserSearchHistoryDTO dto) {
        log.info("导出用户搜索历史，参数：{}", dto);
        UserDetailDTO detailDTO = UserUtil.getDetails();
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(FileExportCenterEnum.community_user_search_history.getCode());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }

        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(FileExportCenterEnum.community_user_search_history.getCode());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());
        dataExportTaskDao.saveOne(dataExportTask);

        // 异步执行导出任务
        labelTask.execute(() -> {
            try {
                exportUserSearchHistoryTask(dto, dataExportTask);
            } catch (Exception e) {
                log.error("导出用户搜索历史异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }


    /**
     * 将CommunitySearchHistory转换为CommunityUserSearchHistoryVO
     */
    private CommunityUserSearchHistoryVO convertToSearchHistoryVO(CommunitySearchHistory history) {
        if (history == null) {
            return null;
        }

        CommunityUserSearchHistoryVO vo = new CommunityUserSearchHistoryVO();
        vo.setId(history.getId());
        vo.setContent(history.getContent());
        vo.setSearchTime(history.getSearchTime());

        return vo;
    }

    /**
     * 导出社区用户列表任务
     */
    private void exportUserListTask(CommunityUserListDTO dto, DataExportTask exportTask) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        File localTmpDirectory = new File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            File exportFile = new File(localTmpDirectory, exportTask.getExportName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), CommunityUserExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();

            Integer pageNum = 1;
            Integer pageSize = 5000;
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            while (true) {
                dto.setPageNum(pageNum);
                dto.setPageSize(pageSize);
                PageVO<CommunityUserVO> voPageVO = this.getUserList(dto);
                List<CommunityUserVO> vos = voPageVO.getList();
                Integer pages = voPageVO.getPages();

                List<CommunityUserExcelVO> excelVOS = CommunityUserUtil.convertToExcelList(vos);
                excelWriter.write(excelVOS, writeSheet);

                pageNum++;
                if (pageNum > pages) {
                    break;
                }
            }

            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input, fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出社区用户列表失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }

    /**
     * 导出用户搜索历史任务
     */
    private void exportUserSearchHistoryTask(CommunityUserSearchHistoryDTO dto, DataExportTask exportTask) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        File localTmpDirectory = new File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            File exportFile = new File(localTmpDirectory, exportTask.getExportName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), CommunityUserSearchHistoryExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();

            Integer pageNum = 1;
            Integer pageSize = 5000;
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            while (true) {
                // 设置分页
                PageHelper.startPage(pageNum, pageSize);

                // 查询用户搜索历史
                List<CommunitySearchHistory> historyList = communitySearchHistoryDao.findByUserId(dto.getUserId());

                if (CollectionUtils.isEmpty(historyList)) {
                    break;
                }

                // 获取分页信息
                Page<CommunitySearchHistory> page = (Page<CommunitySearchHistory>) historyList;

                // 转换为Excel VO并写入
                List<CommunityUserSearchHistoryExcelVO> excelVOS = CommunityUserUtil.convertToSearchHistoryExcelList(historyList);
                excelWriter.write(excelVOS, writeSheet);

                pageNum++;
                if (pageNum > page.getPages()) {
                    break;
                }
            }

            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input, fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出用户搜索历史失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }
}
