package com.hengtiansoft.operation.community.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 社区话题标签Excel导出VO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区话题标签Excel导出VO")
@ContentRowHeight(15) // 内容行高
@HeadRowHeight(20) // 表头行高
public class CommunityTopicLabelExcelVO {

    @ExcelProperty("标签ID")
    @ColumnWidth(20)
    private Long id;

    @ExcelProperty("标签名")
    @ColumnWidth(20)
    private String labelName;

    @ExcelProperty("关联的帖子数量")
    @ColumnWidth(20)
    private Integer postCount;

    @ExcelProperty("参与人数")
    @ColumnWidth(20)
    private Integer participantCount;

    @ExcelProperty("标签类型")
    @ColumnWidth(20)
    private String typeDesc;

    @ExcelProperty("排序权重")
    @ColumnWidth(20)
    private Integer sortOrder;

    @ExcelProperty("标签状态")
    @ColumnWidth(20)
    private String statusDesc;

    @ExcelProperty("更新时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date updateTime;
}
