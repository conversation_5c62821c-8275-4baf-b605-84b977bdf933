package com.hengtiansoft.operation.community.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * UGC推荐位管理VO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位管理VO")
public class CommunityUgcRecommendationsVO {

    @ApiModelProperty("推荐位ID")
    private Long id;

    @ApiModelProperty("推荐位名称")
    private String positionName;

    @ApiModelProperty("排序权重")
    private Integer sortOrder;

    @ApiModelProperty("展示开始时间")
    private Date startTime;

    @ApiModelProperty("展示结束时间")
    private Date endTime;

    @ApiModelProperty("状态 1-未上线 2-已上线 3-已结束")
    private Integer status;

    @ApiModelProperty("关联类型 (1-默认, 2-话题活动)")
    private Integer type;

    @ApiModelProperty("关联话题活动ID")
    private Long communityTopicActivityId;

    @ApiModelProperty("人群限制 (1-不限, 2-新用户, 3-会员, 4-其他)")
    private Integer peopleLimit;

    @ApiModelProperty("会员等级")
    private String grade;

    @ApiModelProperty("标签ID数组")
    private List<Long> labelIds;

    @ApiModelProperty("关联置顶帖ID")
    private Long postId;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
