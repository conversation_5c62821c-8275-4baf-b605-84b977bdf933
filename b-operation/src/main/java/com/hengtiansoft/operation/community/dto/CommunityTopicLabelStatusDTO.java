package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 社区话题标签状态更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区话题标签状态更新DTO")
public class CommunityTopicLabelStatusDTO {

    @NotNull(message = "标签ID不能为空")
    @ApiModelProperty(value = "要操作的标签ID", required = true)
    private Long id;

    @NotNull(message = "目标状态不能为空")
    @ApiModelProperty(value = "目标状态 (0=下线, 1=上线)", required = true)
    private Integer status;
}
