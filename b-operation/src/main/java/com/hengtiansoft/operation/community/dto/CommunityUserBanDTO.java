package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 社区用户禁言DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区用户禁言DTO")
public class CommunityUserBanDTO {

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "要禁言的用户ID", required = true)
    private Long userId;

    @ApiModelProperty("禁言原因")
    private String banReason;
}
