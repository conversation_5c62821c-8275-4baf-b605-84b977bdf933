package com.hengtiansoft.operation.community.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.community.dto.CommunityPostApproveDTO;
import com.hengtiansoft.operation.community.dto.CommunityPostExportDTO;
import com.hengtiansoft.user.entity.dto.CommunityPostListDTO;
import com.hengtiansoft.operation.community.dto.CommunityPostRejectDTO;
import com.hengtiansoft.operation.community.service.CommunityPostService;
import com.hengtiansoft.user.entity.vo.CommunityPostVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 社区帖子管理Controller
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Api(tags = "社区帖子管理")
@RestController
@RequestMapping("/community/posts")
public class CommunityPostController {

    @Resource
    private CommunityPostService communityPostService;

    @ApiOperation("获取帖子列表")
    @PostMapping
    public Response<PageVO<CommunityPostVO>> getPostList(@RequestBody CommunityPostListDTO dto) {
        return ResponseFactory.success(communityPostService.getPostList(dto));
    }

    @ApiOperation("获取帖子详情")
    @GetMapping("/info")
    public Response<CommunityPostVO> getPostInfo(@ApiParam("要查看的帖子ID") @RequestParam Long postId) {
        return ResponseFactory.success(communityPostService.getPostInfo(postId));
    }

    @ApiOperation("导出帖子列表")
    @PostMapping("/export")
    public Response<Void> exportPostList(@RequestBody CommunityPostExportDTO dto) {
        communityPostService.exportPostList(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("批量通过帖子")
    @PostMapping("/approve")
    public Response<Void> approvePosts(@Valid @RequestBody CommunityPostApproveDTO dto) {
        communityPostService.approvePosts(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("批量驳回帖子")
    @PostMapping("/reject")
    public Response<Void> rejectPosts(@Valid @RequestBody CommunityPostRejectDTO dto) {
        communityPostService.rejectPosts(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("删除帖子")
    @GetMapping("/delete")
    public Response<Void> deletePost(@ApiParam("要删除的帖子ID") @RequestParam Long postId) {
        communityPostService.deletePost(postId);
        return ResponseFactory.success();
    }
}
