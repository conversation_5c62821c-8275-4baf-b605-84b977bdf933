package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * UGC推荐位排序DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位排序DTO")
public class CommunityUgcRecommendationsOrderDTO {

    @NotNull(message = "推荐位ID不能为空")
    @ApiModelProperty(value = "推荐位ID", required = true)
    private Long id;

    @NotNull(message = "排序权重不能为空")
    @ApiModelProperty(value = "新的排序权重", required = true)
    private Integer sortOrder;
}
