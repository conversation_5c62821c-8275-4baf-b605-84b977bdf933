package com.hengtiansoft.operation.community.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.operation.community.dto.CommunityPostApproveDTO;
import com.hengtiansoft.operation.community.dto.CommunityPostExportDTO;
import com.hengtiansoft.operation.community.dto.CommunityPostRejectDTO;
import com.hengtiansoft.operation.community.service.CommunityPostService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.entity.vo.nascent.NascentUserVO;
import com.hengtiansoft.thirdpart.enumeration.NascentPointTypeEnum;
import com.hengtiansoft.thirdpart.interfaces.NascentCustomerManager;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.dao.*;
import com.hengtiansoft.user.entity.dto.CommunityPostListDTO;
import com.hengtiansoft.user.entity.po.*;
import com.hengtiansoft.user.entity.vo.CommunityPostExcelVO;
import com.hengtiansoft.user.entity.vo.CommunityPostStatsVO;
import com.hengtiansoft.user.entity.vo.CommunityPostVO;
import com.hengtiansoft.user.entity.vo.CommunityTopicLabelVO;
import com.hengtiansoft.user.service.CommunityPostDeleteAsyncService;
import com.hengtiansoft.user.util.CommunityPostUtil;
import com.hengtiansoft.user.util.CommunityRuleUtil;
import com.nascent.ecrp.opensdk.domain.point.CustomerPointInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 社区帖子管理Service实现
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Slf4j
@Service
public class CommunityPostServiceImpl implements CommunityPostService {

    @Resource
    private CommunityPostDao communityPostDao;
    @Resource
    private CommunityPostStatsDao communityPostStatsDao;
    @Resource
    private CommunityTopicLabelDao communityTopicLabelDao;
    @Resource
    private CommunityPostTopicLabelRelationDao communityPostTopicLabelRelationDao;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private Executor communityPostTask;
    @Resource
    private CommunitySettingDao communitySettingDao;
    @Resource
    private NascentPointChangeLogDao nascentPointChangeLogDao;
    @Resource
    private NascentCustomerManager nascentCustomerManager;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private CommunityPostDeleteAsyncService communityPostDeleteAsyncService;
    @Value("${local.tmp}")
    private String localDir;

    @Override
    public PageVO<CommunityPostVO> getPostList(CommunityPostListDTO dto) {
        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        //查询帖子ids by selectDb
        List<Long> ids = communityPostDao.findPostIdsBySelectDb(dto);
        Pagination pagination = PageUtils.extract(ids);
        List<CommunityPostVO> postList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ids)){
            // 使用关联查询获取帖子列表
            dto.setPostIds(ids);
            postList = communityPostDao.findPostList(dto);
        }

        // 处理查询结果
        if (!CollectionUtils.isEmpty(postList)) {
            // 获取所有帖子ID
            List<Long> postIds = postList.stream().map(CommunityPostVO::getId).collect(Collectors.toList());

            // 查询帖子统计信息
            Map<Long, CommunityPostStats> statsMap = getPostStatsMap(postIds);

            // 查询帖子关联的话题标签
            Map<Long, List<CommunityTopicLabelVO>> topicLabelsMap = getPostTopicLabelsMap(postIds);

            // 处理图片URL、设置统计数据和话题标签
            for (CommunityPostVO post : postList) {
                // 处理图片URL
                if (StringUtils.isNotBlank(post.getImageUrlsJson())) {
                    post.setImageUrls(JSON.parseArray(post.getImageUrlsJson().toString(), String.class));
                }

                // 设置统计信息
                CommunityPostStats stats = statsMap.get(post.getId());
                if (stats != null) {
                    CommunityPostStatsVO statsVO = new CommunityPostStatsVO();
                    BeanUtils.copyProperties(stats, statsVO);
                    post.setStats(statsVO);
                }

                // 设置话题标签
                post.setTopicLabels(topicLabelsMap.get(post.getId()));
            }
        }
        return PageUtils.toPageVO(pagination, postList);
    }

    @Override
    public CommunityPostVO getPostInfo(Long postId) {
        // 使用关联查询获取帖子详情
        CommunityPostVO vo = communityPostDao.findPostDetail(postId);
        if (vo == null) {
            throw new BusinessException("帖子不存在");
        }

        // 处理图片URL
        if (StringUtils.isNotBlank(vo.getImageUrlsJson())) {
            vo.setImageUrls(JSON.parseArray(vo.getImageUrlsJson().toString(), String.class));
        }

        // 查询帖子统计信息
        CommunityPostStats stats = communityPostStatsDao.findByPostId(postId);
        if (stats != null) {
            CommunityPostStatsVO statsVO = new CommunityPostStatsVO();
            BeanUtils.copyProperties(stats, statsVO);
            vo.setStats(statsVO);
        }

        // 查询帖子关联的话题标签
        List<CommunityTopicLabelVO> topicLabels = getPostTopicLabels(postId);
        vo.setTopicLabels(topicLabels);

        return vo;
    }

    @Override
    public void exportPostList(CommunityPostExportDTO dto) {
        log.info("导出社区帖子列表，参数：{}", dto);
        UserDetailDTO detailDTO = UserUtil.getDetails();
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(FileExportCenterEnum.community_post.getCode());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }

        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(FileExportCenterEnum.community_post.getCode());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());

        // 转换DTO为ListDTO用于查询
        CommunityPostListDTO listDTO = new CommunityPostListDTO();
        listDTO.setPostId(dto.getPostId());
        listDTO.setUserId(dto.getUserId());
        listDTO.setPhone(dto.getPhone());
        listDTO.setNickName(dto.getNickName());
        listDTO.setTitle(dto.getTitle());
        listDTO.setContent(dto.getContent());
        listDTO.setStatus(dto.getStatus());
        listDTO.setMinFirstPublishedTime(dto.getMinFirstPublishedTime());
        listDTO.setMaxFirstPublishedTime(dto.getMaxFirstPublishedTime());
        listDTO.setMinUpdateTime(dto.getMinUpdateTime());
        listDTO.setMaxUpdateTime(dto.getMaxUpdateTime());

        dataExportTaskDao.saveOne(dataExportTask);

        // 异步执行导出任务
        communityPostTask.execute(() -> {
            try {
                exportPostListTask(listDTO, dataExportTask);
            } catch (Exception e) {
                log.error("导出社区帖子列表异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvePosts(CommunityPostApproveDTO dto) {
        Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        if (CollectionUtils.isEmpty(dto.getPostIds())) {
            return;
        }

        // 获取积分奖励配置
        Map<String, CommunitySetting> settingsMap = communitySettingDao.findByKeys(Arrays.asList(
                CommunityRuleUtil.KEY_POINTS_POST_PUBLISH,
                CommunityRuleUtil.KEY_POINTS_POST_PUBLISH_LIMIT
        ));

        // 获取发帖奖励积分配置
        CommunitySetting postPublishSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_POST_PUBLISH);
        CommunitySetting postPublishLimitSetting = settingsMap.get(CommunityRuleUtil.KEY_POINTS_POST_PUBLISH_LIMIT);

        // 如果没有配置积分奖励，直接返回
        if (postPublishSetting == null || StringUtils.isBlank(postPublishSetting.getSettingValue())
                || "0".equals(postPublishSetting.getSettingValue())) {
            log.info("未配置发帖积分奖励或奖励积分为0，不发放积分");
        }

        // 解析积分奖励配置
        Integer pointReward = postPublishSetting != null ? Integer.valueOf(postPublishSetting.getSettingValue()) : 0;
        Integer dailyLimit = postPublishLimitSetting != null ? Integer.valueOf(postPublishLimitSetting.getSettingValue()) : 0;

        Date now = new Date();
        for (Long postId : dto.getPostIds()) {
            CommunityPost post = communityPostDao.findById(postId);
            if (post != null && post.getDelflag() == 0 && post.getStatus() == 0) {
                // 更新帖子状态为已发布
                CommunityPost update = new CommunityPost();
                update.setId(post.getId());
                update.setStatus(1);
                update.setModeratedTime(now);
                update.setUpdateTime(now);

                // 如果是首次发布，设置首次发布时间
/*                if (post.getFirstPublishedTime() == null) {
                    update.setFirstPublishedTime(now);
                }*/

                communityPostDao.updateById(update);

                // 如果配置了积分奖励，处理积分发放
                if (pointReward > 0 && dailyLimit > 0) {
                    try {
                        // 异步处理积分奖励，避免影响主流程
                        final Long userId = post.getUserId();
                        final Long finalPostId = post.getId();
                        communityPostTask.execute(() -> {
                            try {
                                processPointReward(userId, finalPostId, pointReward, dailyLimit, now);
                            } catch (Exception e) {
                                log.error("处理帖子审核通过积分奖励异常，用户ID: {}, 帖子ID: {}", userId, finalPostId, e);
                            }
                        });
                    } catch (Exception e) {
                        log.error("提交积分奖励任务异常，帖子ID: {}", post.getId(), e);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectPosts(CommunityPostRejectDTO dto) {
        Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");

        if (CollectionUtils.isEmpty(dto.getRejectList())) {
            return;
        }

        Date now = new Date();
        for (CommunityPostRejectDTO.RejectInfo rejectInfo : dto.getRejectList()) {
            CommunityPost post = communityPostDao.findById(rejectInfo.getPostId());
            if (post != null && post.getDelflag() == 0 && post.getStatus() == 0) {
                // 更新帖子状态为已驳回
                CommunityPost update = new CommunityPost();
                update.setId(post.getId());
                update.setStatus(2);
                update.setModerationRemark(rejectInfo.getModerationRemark());
                update.setModeratedTime(now);
                update.setUpdateTime(now);

                communityPostDao.updateById(update);
            }
        }
    }

    @Override
    public void deletePost(Long postId) {
        CommunityPost post = communityPostDao.findById(postId);
        if (post != null && post.getDelflag() == 0) {
            // 记录用户ID，用于后续异步处理
            final Long userId = post.getUserId();

            // 逻辑删除帖子
            CommunityPost po4Update = new CommunityPost();
            po4Update.setId(post.getId());
            po4Update.setDelflag(1);
            po4Update.setUpdateTime(new Date());
            communityPostDao.updateById(po4Update);

            // 异步处理帖子删除后的相关操作
            communityPostDeleteAsyncService.processPostDeleteAsync(postId, userId);
        }
    }

    /**
     * 导出社区帖子列表任务
     */
    private void exportPostListTask(CommunityPostListDTO dto, DataExportTask exportTask) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        File localTmpDirectory = new File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            File exportFile = new File(localTmpDirectory, exportTask.getExportName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), CommunityPostExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();

            Integer pageNum = 1;
            Integer pageSize = 5000;
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            while (true) {
                dto.setPageNum(pageNum);
                dto.setPageSize(pageSize);
                PageVO<CommunityPostVO> voPageVO = this.getPostList(dto);
                List<CommunityPostVO> vos = voPageVO.getList();
                Integer pages = voPageVO.getPages();

                List<CommunityPostExcelVO> excelVOS = CommunityPostUtil.convertToExcelList(vos);
                excelWriter.write(excelVOS, writeSheet);

                pageNum++;
                if (pageNum > pages) {
                    break;
                }
            }

            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input, fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出社区帖子列表失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }


    private Map<Long, CommunityPostStats> getPostStatsMap(List<Long> postIds) {
        Map<Long, CommunityPostStats> statsMap = new HashMap<>();
        if (CollectionUtils.isEmpty(postIds)) {
            return statsMap;
        }

        List<CommunityPostStats> statsList = communityPostStatsDao.findByPostIds(postIds);

        if (!CollectionUtils.isEmpty(statsList)) {
            for (CommunityPostStats stats : statsList) {
                statsMap.put(stats.getPostId(), stats);
            }
        }

        return statsMap;
    }

    /**
     * 获取帖子关联的话题标签Map
     *
     * @param postIds 帖子ID列表
     * @return 帖子ID -> 话题标签列表的映射
     */
    private Map<Long, List<CommunityTopicLabelVO>> getPostTopicLabelsMap(List<Long> postIds) {
        Map<Long, List<CommunityTopicLabelVO>> topicLabelsMap = new HashMap<>();
        if (CollectionUtils.isEmpty(postIds)) {
            return topicLabelsMap;
        }

        // 初始化每个帖子的标签列表
        for (Long postId : postIds) {
            topicLabelsMap.put(postId, new ArrayList<>());
        }

        // 查询帖子与话题标签的关联关系
        List<CommunityPostTopicLabelRelation> relations = communityPostTopicLabelRelationDao.findByPostIds(postIds);

        if (CollectionUtils.isEmpty(relations)) {
            return topicLabelsMap;
        }

        // 获取所有话题标签ID
        List<Long> labelIds = relations.stream()
                .map(CommunityPostTopicLabelRelation::getCommunityTopicLabelId)
                .collect(Collectors.toList());

        // 查询话题标签信息
        List<CommunityTopicLabel> labels = communityTopicLabelDao.findByIds(labelIds);

        if (CollectionUtils.isEmpty(labels)) {
            return topicLabelsMap;
        }

        // 构建标签ID -> 标签信息的映射
        Map<Long, CommunityTopicLabel> labelMap = new HashMap<>();
        for (CommunityTopicLabel label : labels) {
            labelMap.put(label.getId(), label);
        }

        // 组装数据
        for (CommunityPostTopicLabelRelation relation : relations) {
            Long postId = relation.getPostId();
            Long labelId = relation.getCommunityTopicLabelId();
            CommunityTopicLabel label = labelMap.get(labelId);

            if (label != null) {
                CommunityTopicLabelVO labelVO = new CommunityTopicLabelVO();
                labelVO.setCommunityTopicLabelId(label.getId());
                labelVO.setLabelName(label.getLabelName());

                topicLabelsMap.get(postId).add(labelVO);
            }
        }

        return topicLabelsMap;
    }

    /**
     * 获取帖子关联的话题标签列表
     *
     * @param postId 帖子ID
     * @return 话题标签列表
     */
    private List<CommunityTopicLabelVO> getPostTopicLabels(Long postId) {
        List<CommunityTopicLabelVO> topicLabels = new ArrayList<>();

        // 查询帖子与话题标签的关联关系
        List<CommunityPostTopicLabelRelation> relations = communityPostTopicLabelRelationDao.findByPostId(postId);

        if (CollectionUtils.isEmpty(relations)) {
            return topicLabels;
        }

        // 获取所有话题标签ID
        List<Long> labelIds = relations.stream()
                .map(CommunityPostTopicLabelRelation::getCommunityTopicLabelId)
                .collect(Collectors.toList());

        // 查询话题标签信息
        List<CommunityTopicLabel> labels = communityTopicLabelDao.findByIds(labelIds);

        if (CollectionUtils.isEmpty(labels)) {
            return topicLabels;
        }

        // 转换为VO对象
        for (CommunityTopicLabel label : labels) {
            CommunityTopicLabelVO labelVO = new CommunityTopicLabelVO();
            labelVO.setCommunityTopicLabelId(label.getId());
            labelVO.setLabelName(label.getLabelName());
            topicLabels.add(labelVO);
        }

        return topicLabels;
    }

    /**
     * 处理帖子审核通过后的积分奖励
     *
     * @param userId 用户ID
     * @param postId 帖子ID
     * @param pointReward 奖励积分数
     * @param dailyLimit 每日奖励次数限制
     * @param now 当前时间
     */
    private void processPointReward(Long userId, Long postId, Integer pointReward, Integer dailyLimit, Date now) {
        log.info("处理帖子审核通过积分奖励，用户ID: {}, 帖子ID: {}, 奖励积分: {}, 每日限制: {}", userId, postId, pointReward, dailyLimit);
        try {
            // 查询用户信息
            CustomerUser user = customerUserDao.findById(userId);
            if (user == null) {
                log.error("用户不存在，无法发放积分奖励，用户ID: {}", userId);
                return;
            }

            // 检查用户是否已从奶卡平台入会
            if (user.getNascentFlag() == null || user.getNascentFlag() != 1) {
                log.info("用户未从奶卡平台入会，不发放积分奖励，用户ID: {}", userId);
                return;
            }

            CommunityPost post = communityPostDao.findById(postId);
            if (post == null || Objects.equals(post.getDelflag(), DeleteFlagEnum.IS_DELETE.getCode())) {
                log.error("帖子不存在，无法发放积分奖励，帖子ID: {}", postId);
                return;
            }

            List<NascentPointChangeLog> changeLogs = nascentPointChangeLogDao.findByTargetIdAndType(postId, "community_post");
            if(!CollectionUtils.isEmpty(changeLogs)){
                log.warn("帖子已领取过积分奖励，帖子ID: {}", postId);
                return;
            }

            // 获取日期
            Date startOfDay = DateUtil.getDayOfStart(DateUtil.dateToLocalDate(post.getPublishedTime()));
            Date endOfDay =  DateUtil.getDayOfEnd(DateUtil.dateToLocalDate(post.getPublishedTime()));

            // 查询用户当天已获得的发帖积分奖励次数
            List<NascentPointChangeLog> logs = nascentPointChangeLogDao.findByPhoneAndType(user.getPhone(), "community_post", startOfDay, endOfDay);

            // 过滤出当天的记录
            long todayCount = logs.size();

            // 检查是否超过每日限制
            if (todayCount >= dailyLimit) {
                log.info("用户今日已达到发帖积分奖励次数上限，不再发放积分，用户ID: {}, 当前次数: {}, 限制次数: {}",
                        userId, todayCount, dailyLimit);
                return;
            }

            // 获取南讯用户信息
            NascentUserVO nascentUserVO = nascentCustomerManager.getNascentUserVO(userId, user.getPhone());
            if (nascentUserVO == null) {
                log.error("获取南讯用户信息失败，无法发放积分奖励，用户ID: {}", userId);
                return;
            }
            // 记录积分变更日志
            NascentPointChangeLog pointChangeLog = new NascentPointChangeLog();
            pointChangeLog.setUserId(userId);
            pointChangeLog.setPhone(user.getPhone());
            pointChangeLog.setTargetId(postId); // 关联帖子ID
            pointChangeLog.setPoint(new BigDecimal(pointReward));
            pointChangeLog.setBusinessType("community_post"); // 业务类型：社区发帖
            pointChangeLog.setStatus(0);
            pointChangeLog.setCreateTime(now);
            pointChangeLog.setUpdateTime(now);
            nascentPointChangeLogDao.insertOne(pointChangeLog);

            // 发放积分
            CustomerPointInfo customerPointInfo = nascentCustomerManager.addPoint(
                    nascentUserVO.getNasOuid(),
                    nascentUserVO.getPlatform(),
                    NascentPointTypeEnum.COMMUNITY.getCode(),
                    new BigDecimal(pointReward),
                    "社区发帖奖励");

            // 更新用户总积分
            if(null == customerPointInfo){
                throw new BusinessException("社区发帖奖励失败. userId:" + userId + " phone:" + user.getPhone());
            }
            CustomerUser update = new CustomerUser();
            update.setPoint(customerPointInfo.getAvailPoint().intValue());
            update.setId(userId);
            customerUserDao.updateOne(update);

            log.info("帖子审核通过积分奖励发放成功，用户ID: {}, 帖子ID: {}, 奖励积分: {}, 当前积分: {}",
                    userId, postId, pointReward, customerPointInfo.getAvailPoint());
        } catch (Exception e) {
            log.error("处理帖子审核通过积分奖励异常", e);
        }
    }
}
