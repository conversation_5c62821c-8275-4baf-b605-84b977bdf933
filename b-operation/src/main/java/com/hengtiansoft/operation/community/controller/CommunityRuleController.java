package com.hengtiansoft.operation.community.controller;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.community.dto.CommunityRuleInfoDTO;
import com.hengtiansoft.operation.community.dto.CommunityRuleSaveDTO;
import com.hengtiansoft.operation.community.service.CommunityRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 社区规则管理Controller
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Api(tags = "社区规则管理")
@RestController
@RequestMapping("/community/rule")
public class CommunityRuleController {

    @Resource
    private CommunityRuleService communityRuleService;

    @ApiOperation("获取社区规则及积分配置详情")
    @GetMapping("/info")
    public Response<CommunityRuleInfoDTO> getRuleInfo() {
        return ResponseFactory.success(communityRuleService.getRuleInfo());
    }

    @ApiOperation("保存社区规则及积分配置")
    @PostMapping("/save")
    public Response<Void> saveRule(@Valid @RequestBody CommunityRuleSaveDTO dto) {
        communityRuleService.saveRule(dto);
        return ResponseFactory.success();
    }
}
