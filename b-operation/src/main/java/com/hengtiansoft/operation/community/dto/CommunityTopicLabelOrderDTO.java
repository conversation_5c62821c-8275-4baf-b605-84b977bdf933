package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 社区话题标签排序DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区话题标签排序DTO")
public class CommunityTopicLabelOrderDTO {

    @NotNull(message = "标签ID不能为空")
    @ApiModelProperty(value = "标签ID", required = true)
    private Long id;

    @NotNull(message = "排序权重不能为空")
    @ApiModelProperty(value = "新的排序权重", required = true)
    private Integer sortOrder;
}
