package com.hengtiansoft.operation.community.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.user.entity.dto.CommunitySearchHistoryListDTO;
import com.hengtiansoft.operation.community.service.CommunitySearchHistoryService;
import com.hengtiansoft.operation.community.vo.CommunitySearchHistoryExcelVO;
import com.hengtiansoft.operation.community.vo.CommunitySearchHistoryVO;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.dao.CommunitySearchHistoryDao;
import com.hengtiansoft.user.entity.po.CommunitySearchHistory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 社区搜索记录管理Service实现
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Slf4j
@Service
public class CommunitySearchHistoryServiceImpl implements CommunitySearchHistoryService {

    @Resource
    private CommunitySearchHistoryDao communitySearchHistoryDao;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private Executor labelTask;
    @Value("${local.tmp}")
    private String localDir;

    @Override
    public PageVO<CommunitySearchHistoryVO> getSearchHistoryList(CommunitySearchHistoryListDTO dto) {
        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 查询数据
        List<CommunitySearchHistory> historyList = communitySearchHistoryDao.findByCondition(dto);

        if (CollectionUtils.isEmpty(historyList)) {
            return PageUtils.emptyPage(dto);
        }

        // 转换为VO
        List<CommunitySearchHistoryVO> voList = historyList.stream().map(this::convertToVO).collect(Collectors.toList());

        // 构建分页结果
        return PageUtils.toPageVO(voList);
    }

    @Override
    public void exportSearchHistoryList(CommunitySearchHistoryListDTO dto) {
        log.info("导出社区搜索记录列表，参数：{}", dto);
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(FileExportCenterEnum.community_user_search_history.getCode());
        if (null == enumByCode) {
            throw new BusinessException("导出类型不存在");
        }

        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSON.toJSONString(dto));
        dataExportTask.setExportType(FileExportCenterEnum.community_search_history.getCode());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(user.getUserName());
        dataExportTaskDao.saveOne(dataExportTask);

        // 异步执行导出任务
        labelTask.execute(() -> {
            try {
                exportSearchHistoryListTask(dto, dataExportTask);
            } catch (Exception e) {
                log.error("导出社区搜索记录列表异常", e);
                dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
                dataExportTaskDao.updateByPrimaryKey(dataExportTask);
            }
        });
    }

    /**
     * 将实体转换为VO
     */
    private CommunitySearchHistoryVO convertToVO(CommunitySearchHistory history) {
        CommunitySearchHistoryVO vo = new CommunitySearchHistoryVO();
        BeanUtils.copyProperties(history, vo);
        return vo;
    }

    /**
     * 将实体转换为Excel VO
     */
    private CommunitySearchHistoryExcelVO convertToExcelVO(CommunitySearchHistory history) {
        CommunitySearchHistoryExcelVO vo = new CommunitySearchHistoryExcelVO();
        BeanUtils.copyProperties(history, vo);
        return vo;
    }

    /**
     * 导出社区搜索记录列表任务
     *
     * @param dto 查询参数
     * @param exportTask 导出任务
     */
    private void exportSearchHistoryListTask(CommunitySearchHistoryListDTO dto, DataExportTask exportTask) {
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_FULL) + System.currentTimeMillis();
        String uniqueName = localDir + date + UUID.randomUUID().toString();
        File localTmpDirectory = new File(uniqueName);

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            if (localTmpDirectory.exists()) {
                FileUtils.cleanDirectory(localTmpDirectory);
            } else {
                FileUtils.forceMkdir(localTmpDirectory);
            }

            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            File exportFile = new File(localTmpDirectory, exportTask.getExportName() + enumByCode.getSubfix());
            ExcelWriter excelWriter = EasyExcel.write(exportFile.getPath(), CommunitySearchHistoryExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .build();

            Integer pageNum = 1;
            Integer pageSize = 5000;
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            while (true) {
                // 设置分页
                PageHelper.startPage(pageNum, pageSize);

                // 查询数据
                List<CommunitySearchHistory> historyList = communitySearchHistoryDao.findByCondition(dto);

                if (CollectionUtils.isEmpty(historyList)) {
                    break;
                }

                // 获取分页信息
                Page<CommunitySearchHistory> page = (Page<CommunitySearchHistory>) historyList;

                // 转换为Excel VO并写入
                List<CommunitySearchHistoryExcelVO> excelVOS = historyList.stream()
                        .map(this::convertToExcelVO)
                        .collect(Collectors.toList());
                excelWriter.write(excelVOS, writeSheet);

                pageNum++;
                if (pageNum > page.getPages()) {
                    break;
                }
            }

            excelWriter.finish();
            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            FileInputStream input = new FileInputStream(exportFile);
            String url = aliyunOssUtils.uploadFile(input, fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (Exception e) {
            log.error("导出社区搜索记录列表失败", e);
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        } finally {
            try {
                FileUtils.deleteDirectory(localTmpDirectory);
            } catch (IOException e) {
                log.error("删除临时文件夹{}失败", localTmpDirectory.getAbsolutePath(), e);
            }
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }
}
