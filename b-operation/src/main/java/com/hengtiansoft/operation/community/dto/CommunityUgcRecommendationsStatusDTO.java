package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * UGC推荐位状态更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("UGC推荐位状态更新DTO")
public class CommunityUgcRecommendationsStatusDTO {

    @NotNull(message = "推荐位ID不能为空")
    @ApiModelProperty(value = "要操作的推荐位ID", required = true)
    private Long id;

    @NotNull(message = "目标状态不能为空")
    @ApiModelProperty(value = "状态 1-未上线 2-已上线 3-已结束", required = true)
    private Integer status;
}
