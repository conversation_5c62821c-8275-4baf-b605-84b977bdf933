package com.hengtiansoft.operation.community.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.community.dto.*;
import com.hengtiansoft.operation.community.service.CommunityUgcRecommendationsService;
import com.hengtiansoft.operation.community.vo.CommunityUgcRecommendationsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * UGC推荐位管理Controller
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Api(tags = "UGC推荐位管理")
@RestController
@RequestMapping("/community/recommendations")
public class CommunityUgcRecommendationsController {

    @Resource
    private CommunityUgcRecommendationsService communityUgcRecommendationsService;

    @ApiOperation("获取UGC推荐位列表")
    @PostMapping
    public Response<PageVO<CommunityUgcRecommendationsVO>> getRecommendationsList(@RequestBody CommunityUgcRecommendationsListDTO dto) {
        return ResponseFactory.success(communityUgcRecommendationsService.getRecommendationsList(dto));
    }

    @ApiOperation("导出UGC推荐位列表")
    @PostMapping("/export")
    public Response<Void> exportRecommendationsList(@RequestBody CommunityUgcRecommendationsExportDTO dto) {
        communityUgcRecommendationsService.exportRecommendationsList(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("关联置顶帖到推荐位/取消关联")
    @PostMapping("/pin")
    public Response<Void> pinRecommendation(@Valid @RequestBody CommunityUgcRecommendationsPinDTO dto) {
        communityUgcRecommendationsService.pinRecommendation(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("保存或编辑UGC推荐位")
    @PostMapping("/save")
    public Response<Void> saveRecommendation(@Valid @RequestBody CommunityUgcRecommendationsDTO dto) {
        communityUgcRecommendationsService.saveRecommendation(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("UGC推荐位排序")
    @PostMapping("/order")
    public Response<Void> orderRecommendation(@Valid @RequestBody CommunityUgcRecommendationsOrderDTO dto) {
        communityUgcRecommendationsService.orderRecommendation(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("UGC推荐位下线状态变更")
    @PostMapping("/statusUpdate")
    public Response<Void> updateRecommendationStatus(@Valid @RequestBody CommunityUgcRecommendationsStatusDTO dto) {
        communityUgcRecommendationsService.updateRecommendationStatus(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("删除UGC推荐位")
    @GetMapping("/delete")
    public Response<Void> deleteRecommendation(@ApiParam("要删除的推荐位ID") @RequestParam Long id) {
        communityUgcRecommendationsService.deleteRecommendation(id);
        return ResponseFactory.success();
    }

    @ApiOperation("UGC推荐位详情")
    @GetMapping("/info")
    public Response<CommunityUgcRecommendationsVO> getRecommendationInfo(@ApiParam("推荐位ID") @RequestParam Long id) {
        return ResponseFactory.success(communityUgcRecommendationsService.getRecommendationInfo(id));
    }
}
