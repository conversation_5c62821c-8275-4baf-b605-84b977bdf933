package com.hengtiansoft.operation.community.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.operation.community.dto.CommunityUserExportDTO;
import com.hengtiansoft.operation.community.dto.CommunityUserSearchHistoryDTO;
import com.hengtiansoft.user.entity.dto.CommunityUserListDTO;
import com.hengtiansoft.user.entity.vo.CommunityUserSearchHistoryVO;
import com.hengtiansoft.user.entity.vo.CommunityUserVO;

/**
 * 社区用户管理Service
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface CommunityUserService {

    /**
     * 获取社区用户列表
     *
     * @param dto 查询参数
     * @return 用户列表分页结果
     */
    PageVO<CommunityUserVO> getUserList(CommunityUserListDTO dto);

    /**
     * 导出用户列表
     *
     * @param dto 导出参数
     */
    void exportUserList(CommunityUserExportDTO dto);

    /**
     * 禁言用户
     *
     * @param id ID
     */
    void banUser(Long id);

    /**
     * 解除用户禁言
     *
     * @param id ID
     */
    void unbanUser(Long id);

    /**
     * 获取用户详情
     *
     * @param id ID
     * @return 用户详情
     */
    CommunityUserVO getUserInfo(Long id);

    /**
     * 获取用户搜索历史列表
     *
     * @param dto 查询参数
     * @return 搜索历史分页结果
     */
    PageVO<CommunityUserSearchHistoryVO> getUserSearchHistory(CommunityUserSearchHistoryDTO dto);

    /**
     * 导出用户搜索历史
     *
     * @param dto 查询参数
     */
    void exportUserSearchHistory(CommunityUserSearchHistoryDTO dto);
}
