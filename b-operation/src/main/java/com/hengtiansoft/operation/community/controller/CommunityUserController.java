package com.hengtiansoft.operation.community.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.community.dto.CommunityUserExportDTO;
import com.hengtiansoft.operation.community.dto.CommunityUserSearchHistoryDTO;
import com.hengtiansoft.operation.community.service.CommunityUserService;
import com.hengtiansoft.user.entity.dto.CommunityUserListDTO;
import com.hengtiansoft.user.entity.vo.CommunityUserSearchHistoryVO;
import com.hengtiansoft.user.entity.vo.CommunityUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 社区用户管理Controller
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Api(tags = "社区用户管理")
@RestController
@RequestMapping("/community/users")
public class CommunityUserController {

    @Resource
    private CommunityUserService communityUserService;

    @ApiOperation("获取社区用户列表")
    @PostMapping
    public Response<PageVO<CommunityUserVO>> getUserList(@RequestBody CommunityUserListDTO dto) {
        return ResponseFactory.success(communityUserService.getUserList(dto));
    }

    @ApiOperation("导出用户列表")
    @PostMapping("/export")
    public Response<Void> exportUserList(@RequestBody CommunityUserExportDTO dto) {
        communityUserService.exportUserList(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("禁言用户")
    @GetMapping("/ban")
    public Response<Void> banUser(@ApiParam("要禁言的ID") @RequestParam Long id) {
        communityUserService.banUser(id);
        return ResponseFactory.success();
    }

    @ApiOperation("解除禁言用户")
    @GetMapping("/unban")
    public Response<Void> unbanUser(@ApiParam("要解除禁言的ID") @RequestParam Long id) {
        communityUserService.unbanUser(id);
        return ResponseFactory.success();
    }

    @ApiOperation("获取用户详情")
    @GetMapping("/info")
    public Response<CommunityUserVO> getUserInfo(@ApiParam("要查看详情的用户ID") @RequestParam Long id) {
        return ResponseFactory.success(communityUserService.getUserInfo(id));
    }

    @ApiOperation("获取用户搜索历史列表")
    @PostMapping("/searchHistory")
    public Response<PageVO<CommunityUserSearchHistoryVO>> getUserSearchHistory(@Valid @RequestBody CommunityUserSearchHistoryDTO dto) {
        return ResponseFactory.success(communityUserService.getUserSearchHistory(dto));
    }

    @ApiOperation("导出用户搜索历史")
    @PostMapping("/searchHistory/export")
    public Response<Void> exportUserSearchHistory(@Valid @RequestBody CommunityUserSearchHistoryDTO dto) {
        communityUserService.exportUserSearchHistory(dto);
        return ResponseFactory.success();
    }
}
