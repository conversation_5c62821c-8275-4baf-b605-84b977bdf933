package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 社区帖子导出DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区帖子导出DTO")
public class CommunityPostExportDTO {

    @ApiModelProperty("帖子ID精确匹配")
    private Long postId;

    @ApiModelProperty("作者用户ID")
    private Long userId;

    @ApiModelProperty("作者手机号")
    private String phone;

    @ApiModelProperty("作者昵称模糊匹配")
    private String nickName;

    @ApiModelProperty("帖子标题包含")
    private String title;

    @ApiModelProperty("帖子内容包含")
    private String content;

    @ApiModelProperty("帖子状态 (0-待审核, 1-已发布, 2-已驳回)")
    private Integer status;

    @ApiModelProperty("首次发布时间开始")
    private Date minFirstPublishedTime;

    @ApiModelProperty("首次发布时间结束")
    private Date maxFirstPublishedTime;

    @ApiModelProperty("编辑时间开始")
    private Date minUpdateTime;

    @ApiModelProperty("编辑时间结束")
    private Date maxUpdateTime;

    @ApiModelProperty("会员等级")
    private String grade;
}
