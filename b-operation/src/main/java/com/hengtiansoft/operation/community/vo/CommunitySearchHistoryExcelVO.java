package com.hengtiansoft.operation.community.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 社区搜索记录Excel导出VO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区搜索记录Excel导出VO")
@ContentRowHeight(15) // 内容行高
@HeadRowHeight(20) // 表头行高
public class CommunitySearchHistoryExcelVO {

    @ExcelProperty("记录ID")
    @ColumnWidth(20)
    private Long id;

    @ExcelProperty("搜索内容")
    @ColumnWidth(25)
    private String content;

    @ExcelProperty("用户ID")
    @ColumnWidth(20)
    private Long userId;

    @ExcelProperty("手机号")
    @ColumnWidth(20)
    private String phone;

    @ExcelProperty("用户昵称")
    @ColumnWidth(20)
    private String nickName;

    @ExcelProperty("搜索时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(30)
    private Date searchTime;
}
