package com.hengtiansoft.operation.community.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 社区话题标签VO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区话题标签VO")
public class CommunityTopicLabelVO {

    @ApiModelProperty("标签ID")
    private Long id;

    @ApiModelProperty("标签名称")
    private String labelName;

    @ApiModelProperty("关联的帖子数量")
    private Integer postCount;

    @ApiModelProperty("参与人数")
    private Integer participantCount;

    @ApiModelProperty("标签类型 (1-运营创建, 2-用户创建)")
    private Integer type;

    @ApiModelProperty("排序权重")
    private Integer sortOrder;

    @ApiModelProperty("标签状态 (0-未上线, 1-已上线)")
    private Integer status;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
