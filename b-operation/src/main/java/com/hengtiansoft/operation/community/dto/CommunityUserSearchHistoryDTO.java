package com.hengtiansoft.operation.community.dto;

import com.hengtiansoft.common.entity.dto.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 社区用户搜索历史查询DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("社区用户搜索历史查询DTO")
public class CommunityUserSearchHistoryDTO extends PageParams {

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "目标用户ID", required = true)
    private Long userId;
}
