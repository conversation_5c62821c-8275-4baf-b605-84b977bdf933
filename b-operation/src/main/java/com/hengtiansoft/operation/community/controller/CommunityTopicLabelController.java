package com.hengtiansoft.operation.community.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.user.entity.dto.CommunityTopicLabelListDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelOrderDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelSaveDTO;
import com.hengtiansoft.operation.community.dto.CommunityTopicLabelStatusDTO;
import com.hengtiansoft.operation.community.service.CommunityTopicLabelService;
import com.hengtiansoft.operation.community.vo.CommunityTopicLabelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 社区话题标签管理Controller
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Api(tags = "社区话题标签管理")
@RestController
@RequestMapping("/community/topic/labels")
public class CommunityTopicLabelController {

    @Resource
    private CommunityTopicLabelService communityTopicLabelService;

    @ApiOperation("获取话题标签列表")
    @PostMapping
    public Response<PageVO<CommunityTopicLabelVO>> getTopicLabelList(@RequestBody CommunityTopicLabelListDTO dto) {
        return ResponseFactory.success(communityTopicLabelService.getTopicLabelList(dto));
    }

    @ApiOperation("导出话题标签列表")
    @PostMapping("/export")
    public Response<Void> exportTopicLabelList(@RequestBody CommunityTopicLabelListDTO dto) {
        communityTopicLabelService.exportTopicLabelList(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("保存话题标签")
    @PostMapping("/save")
    public Response<Void> saveTopicLabel(@Valid @RequestBody CommunityTopicLabelSaveDTO dto) {
        communityTopicLabelService.saveTopicLabel(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("更新话题标签状态")
    @PostMapping("/statusUpdate")
    public Response<Void> updateTopicLabelStatus(@Valid @RequestBody CommunityTopicLabelStatusDTO dto) {
        communityTopicLabelService.updateTopicLabelStatus(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("更新话题标签排序")
    @PostMapping("/order")
    public Response<Void> updateTopicLabelOrder(@Valid @RequestBody CommunityTopicLabelOrderDTO dto) {
        communityTopicLabelService.updateTopicLabelOrder(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("删除话题标签")
    @GetMapping("/delete")
    public Response<Void> deleteTopicLabelOrder(@ApiParam("要删除的话题标签") @RequestParam Long id) {
        communityTopicLabelService.deleteTopicLabel(id);
        return ResponseFactory.success();
    }
}
