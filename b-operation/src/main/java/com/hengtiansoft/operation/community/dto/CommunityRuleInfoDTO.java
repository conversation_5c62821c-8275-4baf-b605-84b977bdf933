package com.hengtiansoft.operation.community.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 社区规则及积分配置详情DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@ApiModel("社区规则及积分配置详情DTO")
public class CommunityRuleInfoDTO {

    @ApiModelProperty("社区规则内容")
    private String rulesContent;

    @ApiModelProperty("积分配置对象")
    private PointsConfig pointsConfig;

    @Data
    public static class PointsConfig {
        @ApiModelProperty("发布帖子基础奖励积分")
        private Integer postPublish;

        @ApiModelProperty("发布帖子基础奖励积分每日次数")
        private Integer postPublishLimit;

        @ApiModelProperty("置顶用户帖子到话题页奖励积分")
        private Integer postTop;

        @ApiModelProperty("参与话题活动基础奖励积分")
        private Integer topicParticipation;

        @ApiModelProperty("参与话题活动基础奖励积分每次次数")
        private Integer topicParticipationLimit;
    }
}
