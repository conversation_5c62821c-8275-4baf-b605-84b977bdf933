package com.hengtiansoft.operation.community.dto;

import com.hengtiansoft.common.entity.dto.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UGC推荐位管理列表查询DTO
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("UGC推荐位管理列表查询DTO")
public class CommunityUgcRecommendationsListDTO extends PageParams {

    @ApiModelProperty("推荐位ID精确匹配")
    private Long id;

    @ApiModelProperty("推荐位名称模糊匹配")
    private String positionName;

    @ApiModelProperty("状态 1-未上线 2-已上线 3-已结束")
    private Integer status;
}
