package com.hengtiansoft.operation.report.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.content.entity.dto.InspectionReportDTO;
import com.hengtiansoft.content.entity.dto.InspectionReportSearchDTO;
import com.hengtiansoft.content.entity.vo.InspectionReportVO;

public interface InspectionReportService {


    PageVO<InspectionReportVO> search(InspectionReportSearchDTO dto);

    void update(InspectionReportDTO dto);

    InspectionReportVO detail(Long id);

    void delete(Long id);

    void save(InspectionReportDTO dto);
}
