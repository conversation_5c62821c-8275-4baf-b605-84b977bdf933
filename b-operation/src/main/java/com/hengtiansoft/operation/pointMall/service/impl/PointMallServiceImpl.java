package com.hengtiansoft.operation.pointMall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.*;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.dto.SkuProductBaseDTO;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.entity.po.SkuStock;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.pointMall.service.PointMallService;
import com.hengtiansoft.operation.pointMall.service.SwapOrderService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.dto.SwapOrderPointDTO;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.entity.po.CouponStats;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.entity.vo.SwapOrderPointVO;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.enums.SwapChannelEnum;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.manager.CouponStatsManager;
import com.hengtiansoft.privilege.entity.dto.MonitorPageDTO;
import com.hengtiansoft.privilege.entity.dto.PointMallDTO;
import com.hengtiansoft.privilege.entity.dto.PointMallItemDTO;
import com.hengtiansoft.privilege.entity.dto.PointMallListDTO;
import com.hengtiansoft.privilege.entity.po.MonitorPage;
import com.hengtiansoft.privilege.entity.po.PointMall;
import com.hengtiansoft.privilege.entity.po.PointMallItem;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.MoTypeEnum;
import com.hengtiansoft.privilege.manager.MonitorPageManager;
import com.hengtiansoft.privilege.manager.PointMallItemManager;
import com.hengtiansoft.privilege.manager.PointMallManager;
import com.hengtiansoft.privilege.util.PointMallUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.enumeration.MessageSubscribeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PointMallServiceImpl implements PointMallService {
    @Resource
    private PointMallManager pointMallManager;
    @Resource
    private PointMallItemManager pointMallItemManager;
    @Resource
    private MonitorPageManager monitorPageManager;
    @Resource
    private MonitorPageService monitorPageService;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private SwapOrderService swapOrderService;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private StockManager stockManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private CouponStatsManager couponStatsManager;
    @Resource
    private MilkProducerService milkProducerService;


    @Override
    public PageVO<PointMallVO> getList(PointMallListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<PointMall> pointMallList = pointMallManager.findByCondition(dto);
        List<Long> ids = StreamUtils.toList(pointMallList, PointMall::getId);
        List<PointMallItem> itemPos = pointMallItemManager.findByPointMallIds(ids);
        Map<Long, List<PointMallItem>> itemMap = StreamUtils.group(itemPos, PointMallItem::getPointMallId);
        if (CollectionUtils.isEmpty(pointMallList)) {
            return PageUtils.emptyPage(dto);
        }

        //优惠券主表
        List<Long> ruleIds = StreamUtils.filterConvert(itemPos, item -> SwapTypeEnum.COUPON.getCode().equals(item.getSwapType()),
                PointMallItem::getTargetId);
        Map<Long, CouponRule> couponMap = StreamUtils.toMap(couponRuleManager.findByIds(ruleIds),
                CouponRule::getId);
        //优惠券相关统计
        Map<Long, CouponStats> couponStatsMap = StreamUtils.toMap(couponStatsManager.getByCouponRuleIdList(ruleIds),
                CouponStats::getCouponRuleId);

        return PageUtils.convert(pointMallList, data -> {
            PointMallVO vo = PointMallUtil.convert2VO(data);
            List<PointMallItem> items = itemMap.get(data.getId());
            if(CollectionUtils.isNotEmpty(items)){
                vo.setPoint(items.get(0).getPoint());
                vo.setTargetId(items.get(0).getTargetId());
            }
            if(Objects.equals(vo.getSwapType(), SwapTypeEnum.COUPON.getCode())){
                //优惠券 支付金额、客单价
                CouponStats couponStats = couponStatsMap.get(vo.getTargetId());
                if(couponStats != null){
                    vo.setTotalAmount(couponStats.getTotalAmount());
                    vo.setAverageUser(couponStats.getAverageUser());
                }
                //优惠券 已使用数量
                CouponRule coupon = couponMap.get(vo.getTargetId());
                if(coupon != null){
                    vo.setUseCount(coupon.getUseCount());
                }
            }
            return vo;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PointMallDTO dto) {
        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        dto.setOperator(user.getUserName());
        dto.setName(dto.getPointItems().get(0).getName());
        // 数据校验
        // 校验活动时间
        if(Objects.nonNull(dto.getEndTime()) && Objects.nonNull(dto.getStartTime())){
            if(dto.getEndTime().before(dto.getStartTime())){
                throw new BusinessException("活动结束时间不能早于开始时间");
            }
        }
        PointMall po = PointMallUtil.buildPO(dto);
        po.setStatus(CommonOptUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
        validParam(dto);
        if(Objects.isNull(dto.getId())){
            // 无id，新增
            pointMallManager.insert(po);
            //推广
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(dto.getName()+"_积分商城");
            monitorPage.setTargetId(po.getId());
            monitorPage.setType(MoTypeEnum.POINT_MALL.getCode());
            monitorPage.setTargetName(dto.getName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
        }else{
            PointMall old = pointMallManager.findById(dto.getId());
            Assert.notNull(old, "活动不存在");
            // 兑换总数量不能小于已兑换数量
            if(dto.getTotalCnt().intValue() < old.getSwapCnt()){
                throw new BusinessException("总兑换数量"+dto.getTotalCnt()+"不能小于已兑换数量"+old.getSwapCnt());
            }
            // 有id，更新
            // 更新换成了updateByPrimaryKey，就不能把null值赋给product了，不然会把数据库的值覆盖掉
            BeanUtil.copyProperties(po, old, CopyOptions.create().setIgnoreNullValue(true));
            old.setSwapLimit(po.getSwapLimit());
            old.setPicUrl(po.getPicUrl());
            old.setGrade(Optional.ofNullable(po.getGrade()).orElse(Strings.EMPTY));
            old.setMemberDay(po.getMemberDay());
            pointMallManager.updateAll(old);
        }
        // 数据处理,point、flashPoint字段放到下面item中
        List<PointMallItem> itemPos =  PointMallUtil.buildItemPO(dto, po);
        // 删除旧明细
        pointMallItemManager.deleteByPointMallId(po.getId());
        // 明细新增
        if(CollectionUtils.isNotEmpty(itemPos)){
            pointMallItemManager.insertList(itemPos);
        }
        if(CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(po.getStatus())){
            // 活动开始提醒
            Long msgId = pointMallManager.createMsg(po.getId(), MessageSubscribeEnum.POINT_MALL_START_NOTICE);
            // 如果是立即发送，则以当前时间发送
            milkProducerService.activityMsgPush(msgId, new Date());
        }

    }

    private void validParam(PointMallDTO dto) {
        // 校验总兑换数量，不能大于优惠券发放数量
        // 校验活动时间重复
        List<Long> targetIds = StreamUtils.toList(dto.getPointItems(), PointMallItemDTO::getTargetId);
        Assert.notEmpty(targetIds, "兑换物不能为空");
        if(null != StreamUtils.findFirst(dto.getPointItems(),
                item -> SwapTypeEnum.PRODUCT.getCode().equals(dto.getSwapType()) &&
                        null == item.getPrice())){
            throw new BusinessException("实物兑换划线价不能为空");
        }
        //时间段重复校验
        List<PointMall> pointMallList = pointMallManager.findByCouponTargetIds(targetIds, dto.getSwapType(), Arrays.asList(CommonActivityStatusEnum.NOT_STARTED.getCode(), CommonActivityStatusEnum.IN_PROGRESS.getCode()));
        for (PointMall pointMall : pointMallList) {
            if(Objects.equals(pointMall.getId(), dto.getId())){
                continue;
            }
            if (DateUtil.isOverlap(dto.getStartTime(), dto.getEndTime(), pointMall.getStartTime(), pointMall.getEndTime())) {
                throw new BusinessException("该活动【"+dto.getName()+"】的时间段与名称为【" + pointMall.getName() + "】的有重复，请修改");
            }
        }

        PointMall old = null;
        if(null != dto.getId()){
            old = pointMallManager.findById(dto.getId());
            Assert.notNull(old, "活动不存在");
        }
        //库存校验
        Integer cnt = dto.getTotalCnt();
        if(null != old){
            cnt = dto.getTotalCnt() - old.getSwapCnt();
        }
        if(Objects.equals(dto.getSwapType(), SwapTypeEnum.COUPON.getCode())){
            List<CouponRule> couponRules = couponRuleManager.findByIds(targetIds);
            Assert.notEmpty(couponRules, "优惠券不存在");
            for (CouponRule couponRule : couponRules) {
                if(cnt > couponRule.getIssuedQuantity() - couponRule.getReceiveCount()){
                    throw new BusinessException("总兑换数量不能大于优惠券剩余数量");
                }
            }
        }else if(Objects.equals(dto.getSwapType(), SwapTypeEnum.PRODUCT.getCode())){
            List<Sku> skuList = skuManager.selectByIds(targetIds);
            Assert.notEmpty(skuList, "商品不存在");
            Map<Long, SkuStock> stockMap = StreamUtils.toMap(stockManager.find(targetIds), SkuStock::getSkuId);
            for (Sku sku : skuList) {
                SkuStock skuStock = stockMap.get(sku.getId());
                Assert.notNull(skuStock, "库存数据缺失!");
                if(cnt > skuStock.getStockNum()){
                    throw new BusinessException("总兑换数量不能大于商品剩余数量");
                }
            }
        }
        //memberDay会员日，activityType = 1时必传
        if(Objects.equals(dto.getActivityType(), ActivityTypeEnum.MEMBER_DAY.getCode())){
            if(null == dto.getMemberDay()){
                throw new BusinessException("会员日不能为空");
            }
            if(dto.getMemberDay() < 1 || dto.getMemberDay() > 31){
                throw new BusinessException("会员日设置错误");
            }
        }
        //flashPoint秒杀价，activityType = 2时必传
        if(Objects.equals(dto.getActivityType(), ActivityTypeEnum.SECKILL.getCode())){
            if(null == dto.getFlashPoint()){
                throw new BusinessException("秒杀价不能为空");
            }
            if(dto.getFlashPoint() > dto.getPoint()){
                throw new BusinessException("秒杀价不能大于兑换积分");
            }
        }
    }

    @Override
    public void delete(Long id) {
        PointMall old = pointMallManager.findById(id);
        Assert.notNull(old, "活动不存在");
        pointMallManager.deleteById(id);
        pointMallItemManager.deleteByPointMallId(id);
    }

    @Override
    public PromoVO promotion(Long id) {
        PointMall pointMall = pointMallManager.findById(id);
        Assert.notNull(pointMall, "活动不存在");
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.POINT_MALL.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        PromoVO promoVO = new PromoVO();
        promoVO.setId(id);

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    @Override
    public PointMallVO get(Long id) {
        PointMall po = pointMallManager.findByIdDetail(id);
        Assert.notNull(po, "活动不存在");
        PointMallVO mallVO = PointMallUtil.convert2VO(po);
        List<Long> targetIds = StreamUtils.toList(mallVO.getPointItems(), PointMallItemVO::getTargetId);
        Assert.notEmpty(targetIds, "数据缺失!");
        if(SwapTypeEnum.COUPON.getCode().equals(mallVO.getSwapType())){
            Map<Long, CouponRule> couponRuleMap = StreamUtils.toMap(couponRuleManager.findByIds(targetIds), CouponRule::getId);
            mallVO.getPointItems().forEach(item -> {
                mallVO.setPoint(item.getPoint());
                item.setItemDelflag(DeleteFlagEnum.IS_DELETE.getCode());
                CouponRule couponRule = couponRuleMap.get(item.getTargetId());
                if(null != couponRule){
                    item.setCouponName(couponRule.getCouponName());
                    item.setCouponType(couponRule.getCouponType());
                    item.setAmountLimit(couponRule.getAmountLimit());
                    item.setAmountFull(couponRule.getAmountFull());
                    item.setAmountReduce(couponRule.getAmountReduce());
                    item.setDiscount(couponRule.getDiscount());
                    item.setStatus(couponRule.getStatus());
                    item.setRemainingNum(couponRule.getIssuedQuantity().longValue() - couponRule.getReceiveCount().longValue());
                    item.setItemDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
                }
            });
        }else if(SwapTypeEnum.PRODUCT.getCode().equals(mallVO.getSwapType())){
            Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuManager.skuProductWithStockList(targetIds), SkuProductBaseDTO::getId);
            mallVO.getPointItems().forEach(item -> {
                mallVO.setPoint(item.getPoint());
                mallVO.setFlashPoint(item.getFlashPoint());
                item.setItemDelflag(DeleteFlagEnum.IS_DELETE.getCode());
                SkuProductBaseDTO sku = skuMap.get(item.getTargetId());
                if(null != sku){
                    item.setPicUrl(sku.getPicUrl());
                    item.setProductName(sku.getProductName());
                    item.setSpecValueList(sku.getSpecValueList());
                    item.setSaleStatus(sku.getSaleStatus());
                    item.setStockNum(sku.getStock());
                    item.setListPrice(sku.getListPrice());
                    item.setItemDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
                }
            });
        }
        return mallVO;
    }

    @Override
    public void export(SwapOrderPointDTO dto) {
        UserDetailDTO detailDTO = UserUtil.getDetails();
        validTimeRange(dto);
        FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(dto.getExportType());
        if (null == enumByCode){
            throw new BusinessException("导出类型不存在");
        }
        DataExportTask dataExportTask = new DataExportTask();
        String date = DateUtil.dateToString(new Date(), DateUtil.DATE_PATTERN_YEAR);
        dataExportTask.setExportName(enumByCode.getFileName() + date);
        dataExportTask.setExportParam(JSONObject.toJSONString(dto));
        dataExportTask.setExportType(dto.getExportType());
        dataExportTask.setStatus(DataExportStatusEnum.PROCESSING.getKey());
        dataExportTask.setOperation(detailDTO.getUserName());
        dto.setFileName(enumByCode.getFileName() + date);
        dataExportTaskDao.saveOne(dataExportTask);
        // 异步执行
        swapOrderService.export(dto, dataExportTask);
    }

    private static void validTimeRange(SwapOrderPointDTO dto) {
        // 支付时间判断
        boolean payTimeFlag = ObjectUtils.allNotNull(dto.getPayTimeStart(), dto.getPayTimeEnd());
        if(!payTimeFlag){
            throw new BusinessException("兑换时间必须填写");
        }

        if(payTimeFlag){
            // 支付开始时间和支付结束时间间隔不能超过3个月
            long interval = DateUtil.interval(dto.getPayTimeStart(), dto.getPayTimeEnd(), ChronoUnit.DAYS);
            if(interval > 93){
                throw new BusinessException("时间间隔不能超过3个月，有问题请联系管理员");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startPointMall(Long id) {
        PointMall po = pointMallManager.findById(id);
        Assert.notNull(po, "活动不存在");
        List<PointMallItem> pointMallItems = pointMallItemManager.findByPointMallId(id);
        po.setPointItems(pointMallItems);
        PointMallDTO dto = BeanUtils.deepCopy(po, PointMallDTO.class);
        dto.setStartTime(new Date());
        PointMallItem item = StreamUtils.getFirst(pointMallItems);
        dto.setFlashPoint(item.getFlashPoint());
        dto.setPoint(item.getFlashPoint());
        validParam(dto);
        if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.IN_PROGRESS.getCode())){
            return;
        }
        PointMall po4Update = new PointMall();
        po4Update.setId(id);
        po4Update.setStartTime(new Date());
        po4Update.setStatus(CommonActivityStatusEnum.IN_PROGRESS.getCode());
        pointMallManager.update(po4Update);

        // 活动开始提醒
        Long msgId = pointMallManager.createMsg(id, MessageSubscribeEnum.POINT_MALL_START_NOTICE);
        // 如果是立即发送，则以当前时间发送
        milkProducerService.activityMsgPush(msgId, new Date());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endPointMall(Long id) {
        PointMall po = pointMallManager.findById(id);
        Assert.notNull(po, "活动不存在");
        if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.END.getCode())){
            return;
        }
        PointMall po4Update = new PointMall();
        po4Update.setId(id);
        po4Update.setEndTime(new Date());
        po4Update.setStatus(CommonActivityStatusEnum.END.getCode());
        pointMallManager.update(po4Update);
        //删除redis用户订阅信息
        pointMallManager.delSubscribeFromRedis(id);
    }

    @Override
    public PageVO<SwapOrderPointVO> swapOrderGetList(SwapOrderPointDTO dto) {
        dto.setChannel(SwapChannelEnum.MALL.getCode());
        return swapOrderService.getList(dto);
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = null;
        if(Objects.equals(dto.getSwapType(), SwapTypeEnum.COUPON.getCode())){
            writeSheet = EasyExcel.writerSheet().head(PointMallCouponExcelVO.class).build();
        }else if(Objects.equals(dto.getSwapType(), SwapTypeEnum.PRODUCT.getCode())){
            writeSheet = EasyExcel.writerSheet().head(PointMallProductExcelVO.class).build();
        }

        PointMallListDTO listDTO = new PointMallListDTO();
        listDTO.setStatus(dto.getStatus());
        listDTO.setName(dto.getName());
        listDTO.setActivityType(dto.getActivityType());
        listDTO.setSwapType(dto.getSwapType());
        int pageNum = 1;
        int pageSize = 1000;
        int pages = 1;
        do {
            listDTO.setPageNum(pageNum);
            listDTO.setPageSize(pageSize);

            PageVO<PointMallVO> pointMallPageVO = this.getList(listDTO);
            Pagination pagination = pointMallPageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;

            if(Objects.equals(dto.getSwapType(), SwapTypeEnum.COUPON.getCode())){
                excelWriter.write(PointMallUtil.convert2CouponExcelVO(pointMallPageVO.getList()), writeSheet);
            }else if(Objects.equals(dto.getSwapType(), SwapTypeEnum.PRODUCT.getCode())){
                excelWriter.write(PointMallUtil.convert2ProductExcelVO(pointMallPageVO.getList()), writeSheet);
            }
        }while (pageNum <= pages);
    }
}
