package com.hengtiansoft.operation.pointMall.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.pointMall.service.PointMallService;
import com.hengtiansoft.order.entity.dto.SwapOrderPointDTO;
import com.hengtiansoft.order.entity.vo.SwapOrderPointVO;
import com.hengtiansoft.order.enums.SwapChannelEnum;
import com.hengtiansoft.privilege.entity.dto.PointMallDTO;
import com.hengtiansoft.privilege.entity.dto.PointMallListDTO;
import com.hengtiansoft.privilege.entity.vo.PointMallVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "积分商城B端")
@RequestMapping("/pointMall")
public class PointMallController {

    @Autowired
    private PointMallService pointMallService;

    @ApiOperation("分页列表")
    @PostMapping("/getList")
    public Response<PageVO<PointMallVO>> getList(@RequestBody PointMallListDTO dto) {
        return ResponseFactory.success(pointMallService.getList(dto));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Object> save(@RequestBody @Validated PointMallDTO dto) {
        pointMallService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Object> delete(@RequestParam Long id) {
        pointMallService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public Response<PointMallVO> get(@RequestParam Long id) {
        return ResponseFactory.success(pointMallService.get(id));
    }

    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<PromoVO> promotion(@RequestParam Long id) {
        return ResponseFactory.success(pointMallService.promotion(id));
    }

    @ApiOperation("开始活动")
    @GetMapping("/start")
    public Response<Object> start(@RequestParam Long id) {
        pointMallService.startPointMall(id);
        return ResponseFactory.success();
    }

    @ApiOperation("结束活动")
    @GetMapping("/end")
    public Response<Object> end(@RequestParam Long id) {
        pointMallService.endPointMall(id);
        return ResponseFactory.success();
    }

    @ApiOperation("分页列表")
    @PostMapping("/swapOrder/getList")
    public Response<PageVO<SwapOrderPointVO>> swapOrderGetList(@RequestBody SwapOrderPointDTO dto) {
        return ResponseFactory.success(pointMallService.swapOrderGetList(dto));
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "/swapOrder/export")
    public Response<Object> export(@RequestBody SwapOrderPointDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        dto.setChannel(SwapChannelEnum.MALL.getCode());
        // dto.setExportFlag(true);
        pointMallService.export(dto);
        return ResponseFactory.success();
    }

}
