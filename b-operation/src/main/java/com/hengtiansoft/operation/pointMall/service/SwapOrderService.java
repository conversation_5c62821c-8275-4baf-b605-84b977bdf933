package com.hengtiansoft.operation.pointMall.service;


import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.order.entity.dto.SwapOrderPointDTO;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.entity.vo.SwapOrderPointVO;

/**
 * <AUTHOR>
 */
public interface SwapOrderService {

    void export(SwapOrderPointDTO dto, DataExportTask dataExportTask);

    void swapOrderGetList(SwapOrderPointDTO dto);

    PageVO<SwapOrderPointVO> getList(SwapOrderPointDTO dto);
}
