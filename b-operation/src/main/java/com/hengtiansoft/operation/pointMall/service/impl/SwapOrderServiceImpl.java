package com.hengtiansoft.operation.pointMall.service.impl;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.operation.pointMall.service.SwapOrderService;
import com.hengtiansoft.order.dao.DataExportTaskDao;
import com.hengtiansoft.order.entity.dto.SwapOrderPointDTO;
import com.hengtiansoft.order.entity.po.DataExportTask;
import com.hengtiansoft.order.entity.po.SwapItem;
import com.hengtiansoft.order.entity.po.SwapOrder;
import com.hengtiansoft.order.entity.vo.*;
import com.hengtiansoft.order.enums.FileExportCenterEnum;
import com.hengtiansoft.order.enums.SwapOrderTypeEnum;
import com.hengtiansoft.order.manager.SwapItemManager;
import com.hengtiansoft.order.manager.SwapOrderManager;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SwapOrderServiceImpl implements SwapOrderService {
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private SwapOrderManager swapOrderManager;
    @Resource
    private SwapItemManager swapItemManager;

    @Async
    @Override
    public void export(SwapOrderPointDTO dto, DataExportTask exportTask) {
        PageVO<SwapOrderPointVO> voPageVOS = this.getList(dto);
        List<SwapOrderPointVO> vos = voPageVOS.getList();

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(exportTask.getId());
        try {
            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(exportTask.getExportType());
            List excelVOS = null;
            Class head = null;
            Integer mergeRowIndex = null;
            List<Integer> mergeColumnRegion = Lists.newArrayList();
            if(enumByCode == FileExportCenterEnum.point_mall){
                excelVOS = this.convert2ExcelList(vos);
                head = SwapOrderExcelVO.class;
                mergeRowIndex = null;
                mergeColumnRegion = Lists.newArrayList();
            }else{
                throw new BusinessException("导出类型有误");
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, head)
                    .autoCloseStream(Boolean.TRUE)
                    .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnRegion))
                    .sheet().doWrite(excelVOS);

            String fileName = exportTask.getExportName() + enumByCode.getSubfix();
            String url = aliyunOssUtils.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()),
                    fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (RuntimeException e) {
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }

    @Override
    public void swapOrderGetList(SwapOrderPointDTO dto) {

    }

    private List<SwapOrderExcelVO> convert2ExcelList(List<SwapOrderPointVO> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return Lists.newArrayList();
        }
        List<SwapOrderExcelVO> excelVOS = Lists.newArrayList();
        for (SwapOrderPointVO vo : vos) {
            SwapOrderExcelVO excelVO = new SwapOrderExcelVO();
            excelVO.setPhone(vo.getPhone());
            excelVO.setPayTime(vo.getPayTime());
            excelVO.setTargetName(vo.getTargetName());
            excelVO.setSwapOrderTypeStr(SwapOrderTypeEnum.getEnum(vo.getSwapOrderType()).getName());
            excelVO.setPoint(vo.getPoint());
            excelVO.setCount(vo.getCount());
            excelVOS.add(excelVO);
        }
        return excelVOS;
    }
    @Override
    public PageVO<SwapOrderPointVO> getList(SwapOrderPointDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<SwapOrder> pos = swapOrderManager.findByCondetion(dto);
        List<String> swapNos = StreamUtils.toList(pos, SwapOrder::getSwapNo);
        Map<String, List<SwapItem>> itemPosMap = StreamUtils.group(swapItemManager.findBySwapNos(swapNos), SwapItem::getSwapNo);
        return PageUtils.convert(pos, data -> {
            SwapOrderPointVO vo = this.convert2PointVO(data);
            List<SwapItem> swapItems = itemPosMap.get(data.getSwapNo());
            SwapItem swapItem = swapItems.get(0);
            vo.setTargetName(swapItem.getTargetName());
            vo.setPicUrl(swapItem.getPicUrl());
            vo.setCount(swapItem.getCount());
            return vo;
        });
    }

    private SwapOrderPointVO convert2PointVO(SwapOrder po) {
        SwapOrderPointVO swapOrderPointVO = new SwapOrderPointVO();
        swapOrderPointVO.setId(po.getId());
        swapOrderPointVO.setUserId(po.getUserId());
        swapOrderPointVO.setPhone(po.getPhone());
        swapOrderPointVO.setSwapPhone(po.getSwapPhone());
        swapOrderPointVO.setSwapNo(po.getSwapNo());
        swapOrderPointVO.setSwapStatus(po.getSwapStatus());
        swapOrderPointVO.setChannel(po.getChannel());
        swapOrderPointVO.setTotalAmount(po.getTotalAmount());
        swapOrderPointVO.setRealAmount(po.getRealAmount());
        swapOrderPointVO.setOrderTime(po.getOrderTime());
        swapOrderPointVO.setPayTime(po.getPayTime());
        swapOrderPointVO.setPushFlag(po.getPushFlag());
        swapOrderPointVO.setOperator(po.getOperator());
        swapOrderPointVO.setCreateTime(po.getCreateTime());
        swapOrderPointVO.setUpdateTime(po.getUpdateTime());
        swapOrderPointVO.setDelflag(po.getDelflag());
        swapOrderPointVO.setSwapOrderType(po.getSwapOrderType());
        swapOrderPointVO.setPoint(po.getPoint());
        return swapOrderPointVO;

    }
}
