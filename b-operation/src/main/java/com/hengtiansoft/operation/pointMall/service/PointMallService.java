package com.hengtiansoft.operation.pointMall.service;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.order.entity.dto.SwapOrderPointDTO;
import com.hengtiansoft.order.entity.vo.SwapOrderPointVO;
import com.hengtiansoft.privilege.entity.dto.PointMallDTO;
import com.hengtiansoft.privilege.entity.dto.PointMallListDTO;
import com.hengtiansoft.privilege.entity.vo.PointMallVO;
import com.hengtiansoft.privilege.entity.vo.PromoVO;


public interface PointMallService {

    PageVO<PointMallVO> getList(PointMallListDTO dto);

    void save(PointMallDTO dto);

    void delete(Long id);

    PromoVO promotion(Long id);

    PointMallVO get(Long id);

    void export(SwapOrderPointDTO dto);

    void startPointMall(Long id);

    void endPointMall(Long id);

    PageVO<SwapOrderPointVO> swapOrderGetList(SwapOrderPointDTO dto);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);
}
