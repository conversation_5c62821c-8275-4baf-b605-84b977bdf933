package com.hengtiansoft.operation.luckyDraw.service;


import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.privilege.entity.dto.LuckyRecordSearchDTO;
import com.hengtiansoft.privilege.entity.vo.*;

import java.util.List;

public interface LuckyRecordService {


    PageVO<LuckyRecordListVO> page(LuckyRecordSearchDTO dto);

    LuckyRecordCountVO count(Long luckyId);

    void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter);

    List<LuckyPrizeVO> prizeList(Long luckyId, String prizeName);


}
