package com.hengtiansoft.operation.luckyDraw.controller;


import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.luckyDraw.service.LuckyRecordService;
import com.hengtiansoft.privilege.entity.dto.LuckyRecordSearchDTO;
import com.hengtiansoft.privilege.entity.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "抽奖记录")
@RequestMapping("/luckyRecord")
public class LuckyRecordController {

    @Autowired
    private LuckyRecordService luckyRecordService;

    @ApiOperation("奖品列表")
    @GetMapping("/prizeList")
    public Response<List<LuckyPrizeVO>> prizeList(@RequestParam Long luckyId,
                                                  @RequestParam(required = false) String prizeName) {
        return ResponseFactory.success(luckyRecordService.prizeList(luckyId, prizeName));
    }


    @ApiOperation("抽奖记录分页列表")
    @PostMapping("/page")
    public Response<PageVO<LuckyRecordListVO>> page(@RequestBody LuckyRecordSearchDTO dto) {
        return ResponseFactory.success(luckyRecordService.page(dto));
    }


    @ApiOperation("抽奖记录统计")
    @GetMapping("/count")
    public Response<LuckyRecordCountVO> count(@RequestParam Long luckyId) {
        return ResponseFactory.success(luckyRecordService.count(luckyId));
    }
}
