package com.hengtiansoft.operation.luckyDraw.service;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.privilege.entity.dto.LuckyDrawSaveDTO;
import com.hengtiansoft.privilege.entity.dto.LuckyDrawSearchDTO;
import com.hengtiansoft.privilege.entity.dto.LuckyPrizeSupDTO;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.LuckyDrawStatusEnum;

import java.util.List;

public interface LuckyDrawService {

    /**
     * 活动详情
     * @param id 幸运大抽奖活动id
     * @return
     */
    LuckyDrawDetailVO detail(Long id);

    /**
     * 删除活动
     * @param id 幸运大抽奖活动id
     */
    void delete(Long id);

    /**
     * 活动保存/编辑，通过id判断
     * @param dto
     */
    void save(LuckyDrawSaveDTO dto);

    /**
     * 分页列表
     * @param dto
     * @return
     */
    PageVO<LuckyDrawListVO> page(LuckyDrawSearchDTO dto);

    /**
     * 奖品去补充
     * @param dto
     * @return
     */
    void supplement(List<LuckyPrizeSupDTO> dto);

    /**
     * 活动上下线
     * @param id 活动id
     * @return
     */
    void setLuckyDrawStatus(Long id, LuckyDrawStatusEnum end);

    /**
     * 校验库存
     * @param targetId 奖品原id
     * @param type 奖品类型
     * @param count 数量
     * @return
     */
    void checkStock(Long prizeId, Long targetId, Integer type, Long count);

    /**
     * 抽奖活动列表
     * @param dto
     * @return
     */
    List<LuckyDrawListVO> list(LuckyDrawSearchDTO dto);

    /**
     * 抽奖活动推广信息
     * @param id
     * @return
     */
    LuckyDrawPromoVO promotion(Long id);
}
