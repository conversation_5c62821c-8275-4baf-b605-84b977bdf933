package com.hengtiansoft.operation.luckyDraw.service.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.StockCalculateDTO;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.operation.luckyDraw.service.LuckyDrawService;
import com.hengtiansoft.operation.mq.service.MilkProducerService;
import com.hengtiansoft.operation.role.auth.entity.dto.UserDetailDTO;
import com.hengtiansoft.operation.sms.service.MonitorPageService;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.LuckyDrawDetailVO;
import com.hengtiansoft.privilege.entity.vo.LuckyDrawListVO;
import com.hengtiansoft.privilege.entity.vo.LuckyDrawPromoVO;
import com.hengtiansoft.privilege.entity.vo.LuckyPrizeVO;
import com.hengtiansoft.privilege.enums.*;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.privilege.util.LuckyDrawUtil;
import com.hengtiansoft.privilege.util.LuckyRecordUtil;
import com.hengtiansoft.privilege.util.LuckyWheelUtil;
import com.hengtiansoft.security.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;

import static com.github.pagehelper.page.PageMethod.startPage;

@Service
public class LuckyDrawServiceImpl implements LuckyDrawService {

    @Autowired
    private LuckyDrawManager luckyDrawManager;

    @Autowired
    private LuckyPrizeManager luckyPrizeManager;

    @Autowired
    private LuckyWheelManager luckyWheelManager;

    @Autowired
    private LuckyRecordManager luckyRecordManager;

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private MonitorPageService monitorPageService;

    @Autowired
    private MonitorPageManager monitorPageManager;

    @Autowired
    private CouponRuleManager couponRuleManager;

    @Autowired
    private StockManager stockManager;

    @Autowired
    private MilkProducerService  milkProducerService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private LuckyDetailManager luckyDetailManager;

    @Override
    public LuckyDrawDetailVO detail(Long id) {

        //活动
        LuckyDraw luckyDraw = luckyDrawManager.get(id);
        if(null == luckyDraw){
            throw new BusinessException("抽奖活动不存在！");
        }

        //任务
        List<Task> taskList = taskManager.findByActivityId(id);

        //奖品
        List<LuckyPrize> prizeList = luckyPrizeManager.findByLuckyId(id);

        //转盘
        List<LuckyWheel> wheelList = luckyWheelManager.findByLuckyId(id);

        LuckyDrawDetailVO luckyDrawDetailVO = LuckyDrawUtil.convert2DetailVO(luckyDraw, taskList, prizeList, wheelList);


        return luckyDrawDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        //活动
        LuckyDraw luckyDraw = luckyDrawManager.get(id);
        if(null == luckyDraw){
            throw new BusinessException("抽奖活动不存在！");
        }

        if(LuckyDrawStatusEnum.IN_PROGRESS.getCode().equals(luckyDraw.getStatus())){
            throw new BusinessException("进行中的活动不能删除！");
        }

        //活动主表
        luckyDrawManager.deleteById(id);

        //活动任务
        taskManager.deleteByLuckyId(id);

        //活动奖品
        luckyPrizeManager.deleteByLuckyId(id);

        //活动转盘
        luckyWheelManager.deleteByLuckyId(id);
    }

    @Override
    public void save(LuckyDrawSaveDTO dto) {

        UserDetailDTO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        dto.setOperator(user.getUserName());

        //参数校验
        checkParams(dto);

        //编辑标记
        boolean updateFlag = false;
        if(null != dto.getId()){
            updateFlag = true;
        }
        boolean finalUpdateFlag = updateFlag;
        boolean executeStatus = transactionTemplate.execute(status -> {
            //活动主表
            luckyDrawManager.save(dto);

            //活动任务
            taskManager.luckyDrawTaskSave(finalUpdateFlag, dto.getId(), dto.getOperator(), dto.getTaskList());

            //活动奖品
            List<LuckyPrize> prizeList = luckyPrizeManager.save(finalUpdateFlag, dto.getId(), dto.getOperator(), dto.getPrizeList(), dto.getThankYou());

            //奖品与转盘的关联
            LuckyWheelUtil.buildPrizeWithWheel(prizeList, dto.getWheelList());

            //活动转盘
            luckyWheelManager.save(finalUpdateFlag, dto.getId(), dto.getOperator(), dto.getWheelList());

            return true;
        });

        if(!finalUpdateFlag && executeStatus){
            //推广
            MonitorPageDTO monitorPage = new MonitorPageDTO();
            monitorPage.setName(dto.getLuckyName() + "—幸运大抽奖统计");
            monitorPage.setTargetId(dto.getId());
            monitorPage.setType(MoTypeEnum.LUCKY_DRAW.getCode());
            monitorPage.setTargetName(dto.getLuckyName());
            monitorPage.setPromotion(BasicFlagEnum.YES.getKey());
            monitorPageService.insertOrUpdate(monitorPage);
        }

        if(updateFlag && executeStatus){
            milkProducerService.luckyDrawUpdate(dto.getId());
        }
    }

    private void checkParams(LuckyDrawSaveDTO dto) {
        //活动主表
        checkDraw(dto);
        //任务
        checkTask(dto);
        //奖品
        checkPrize(dto);
        //转盘
        checkWheel(dto);
    }

    private void checkWheel(LuckyDrawSaveDTO dto) {
        if(CollectionUtils.isEmpty(dto.getWheelList()) || dto.getWheelList().size() != 9 ){
            throw new BusinessException("请设置9个转盘格子！");
        }
        for (LuckyWheelDTO wheelDTO: dto.getWheelList()) {
            if(null == wheelDTO.getRadio()){
                throw new BusinessException("转盘概率缺失！");
            }
        }

        BigDecimal radio = StreamUtils.mapReduce(dto.getWheelList(), LuckyWheelDTO::getRadio, BigDecimal::add);
        if(radio.compareTo(BigDecimal.valueOf(100)) != 0 ){
            throw new BusinessException("总概率必须为100%！");
        }
    }

    private void checkPrize(LuckyDrawSaveDTO dto) {
        if(null == dto.getThankYou()){
            throw new BusinessException("缺少未中奖设置！");
        }
        if(CollectionUtils.isEmpty(dto.getPrizeList())){
            return;
        }
        for (LuckyPrizeDTO prizeDTO: dto.getPrizeList()) {
            if(StringUtils.isBlank(prizeDTO.getPrizeName())){
                throw new BusinessException("奖品名称不能为空！");
            }
            if(null == prizeDTO.getTotalCnt() || prizeDTO.getTotalCnt() < 1){
                throw new BusinessException("发放总量必须大于等于1份！");
            }
            LuckyPrizeTypeEnum luckyPrizeTypeEnum = LuckyPrizeTypeEnum.getEnumByCode(prizeDTO.getPrizeType());
            if(null == luckyPrizeTypeEnum){
                throw new BusinessException("请选择奖品内容！");
            }
            if(LuckyPrizeTypeEnum.POINT.equals(luckyPrizeTypeEnum)){
                if(null == prizeDTO.getPoint() || prizeDTO.getPoint() < 1){
                    throw new BusinessException("奖品积分必须大于等于1积分！");
                }
            }
        }
    }

    private void checkDraw(LuckyDrawSaveDTO dto) {
        if(StringUtils.isBlank(dto.getLuckyName())){
            throw new BusinessException("活动名称不能为空！");
        }

        if(dto.getLuckyName().length() > 15){
            throw new BusinessException("活动名称不能超过15个字！");
        }

        LuckyDrawSearchDTO searchDTO = new LuckyDrawSearchDTO();
        searchDTO.setLuckyName(dto.getLuckyName());
        searchDTO.setNotId(dto.getId());
        if(CollectionUtils.isNotEmpty(luckyDrawManager.searchByCondition(searchDTO))){
            throw new BusinessException("活动名称已存在！");
        }

        if(null == dto.getStartTime() || null == dto.getEndTime()){
            throw new BusinessException("活动时间不能为空！");
        }

        if(dto.getStartTime().after(dto.getEndTime())){
            throw new BusinessException("活动开始时间不能大于结束时间！");
        }

        if(null == LuckyDrawPeopleLimitEnum.getEnumByCode(dto.getPeopleLimit())){
            throw new BusinessException("参与人限制不能为空！");
        }

        if(null != dto.getPointDeduction() && dto.getPointDeduction() <= 0){
            throw new BusinessException("参与条件积分必须大于0！");
        }

        if(null == dto.getEverydayCnt() && null == dto.getEveryoneCnt()){
            throw new BusinessException("抽奖次数不能为空！");
        }

        if(null !=dto.getEverydayCnt() && null != dto.getEveryoneCnt()){
            if(dto.getEverydayCnt() > dto.getEveryoneCnt()){
                throw new BusinessException("每人每天抽奖次数不能大于每人一共抽奖次数！");
            }
        }

        if(null == dto.getWinningCnt()){
            throw new BusinessException("中奖次数不能为空！");
        }

        if(StringUtils.isBlank(dto.getBgImg())){
            throw new BusinessException("背景图片缺失！");
        }

        if(null == LuckyDrawWinningShowEnum.getEnumByCode(dto.getWinningShow())){
            throw new BusinessException("中奖名单设置不能为空！");
        }

        if(StringUtils.isBlank(dto.getRule())){
            throw new BusinessException("玩法规则不能为空！");
        }

        if(LuckyDrawPeopleLimitEnum.GRADE.getCode().equals(dto.getPeopleLimit())) {
            if (CollectionUtils.isEmpty(dto.getGradeList())) {
                throw new BusinessException("会员等级不能为空!");
            } else {
                dto.setGrade(StringUtils.join(dto.getGradeList(), ","));
            }
        }

        Date now = new Date();
        if(dto.getStartTime().before(now)){
            dto.setStatus(LuckyDrawStatusEnum.IN_PROGRESS.getCode());
        }

        if(dto.getEndTime().before(now)){
            dto.setStatus(LuckyDrawStatusEnum.END.getCode());
        }
    }

    private void checkTask(LuckyDrawSaveDTO dto) {
        if(CollectionUtils.isEmpty(dto.getTaskList())){
            return;
        }
        for (LuckyDrawTaskDTO taskDTO: dto.getTaskList()) {
            if(null == taskDTO.getTaskType()){
                throw new BusinessException("缺失额外任务的任务类型参数!");
            }
            TaskTypeEnum taskTypeEnum = TaskTypeEnum.getEnum(taskDTO.getTaskType());
            switch (taskTypeEnum){
                case ORDER:
/*                    if(StringUtils.isBlank(taskDTO.getTaskUrl())){
                        throw new BusinessException("引导页面缺失!");
                    }*/
                    if(null == taskDTO.getParams().getOrderMinLimit()){
                        throw new BusinessException("订单最低门槛缺失!");
                    }
                    if(null == taskDTO.getParams().getMaxCnt()){
                        throw new BusinessException("最多获得次数缺失!");
                    }
                    if(taskDTO.getParams().getMaxCnt() < 1 || taskDTO.getParams().getMaxCnt() > 99){
                        throw new BusinessException("每天/每人最多可获得1-99次!");
                    }
                    break;
                case SHARE:
                    if(null == taskDTO.getParams().getMaxCnt()){
                        throw new BusinessException("最多获得次数缺失!");
                    }
                    if(taskDTO.getParams().getMaxCnt() < 1 || taskDTO.getParams().getMaxCnt() > 99){
                        throw new BusinessException("每天/每人最多可获得1-99次!");
                    }
                    break;
                case BROWSE_PAGE:
                    if(StringUtils.isBlank(taskDTO.getTaskUrl())){
                        throw new BusinessException("引导页面缺失!");
                    }
                    if(null == taskDTO.getParams().getMaxCnt()){
                        throw new BusinessException("最多获得次数缺失!");
                    }
                    if(taskDTO.getParams().getMaxCnt() < 1 || taskDTO.getParams().getMaxCnt() > 99){
                        throw new BusinessException("每天/每人最多可获得1-99次!");
                    }
                    if(null != taskDTO.getParams().getStopTime() &&
                            (taskDTO.getParams().getStopTime() < 1 || taskDTO.getParams().getStopTime() > 99)){
                        throw new BusinessException("需停留值范围1-99!");
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public PageVO<LuckyDrawListVO> page(LuckyDrawSearchDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<LuckyDraw> luckyDrawList = luckyDrawManager.searchByCondition(dto);

        if (CollectionUtils.isEmpty(luckyDrawList)) {
            return PageUtils.emptyPage(dto);
        }

        List<Long> ids = StreamUtils.toList(luckyDrawList, LuckyDraw::getId);
        //奖品
        List<LuckyPrize> luckyPrizeList = luckyPrizeManager.selectByLuckyIds(ids);
        Map<Long, List<LuckyPrize>> prizeMap = StreamUtils.group(luckyPrizeList, LuckyPrize::getLuckyId);


        //抽奖记录
        LuckyRecordSearchDTO searchDTO = new LuckyRecordSearchDTO();
        searchDTO.setLuckyIds(ids);
        List<LuckyRecord> luckyRecordList = luckyRecordManager.getList(searchDTO);
        Map<Long, List<LuckyRecord>> luckyRecordMap = StreamUtils.group(luckyRecordList, LuckyRecord::getLuckyId);

        //转盘
        LuckyWheelSearchDTO wheelSearchDTO = new LuckyWheelSearchDTO();
        wheelSearchDTO.setLuckyIds(ids);
        List<LuckyWheel> luckyWheelList = luckyWheelManager.getList(wheelSearchDTO);
        Map<Long, List<LuckyWheel>> luckyWheelMap = StreamUtils.group(luckyWheelList, LuckyWheel::getLuckyId);


        return PageUtils.convert(luckyDrawList, data ->{
            LuckyDrawListVO luckyDrawVO = LuckyDrawUtil.convert2VO(data);
            List<LuckyPrize> prizeList = prizeMap.get(data.getId());
            List<LuckyWheel> wheelList = luckyWheelMap.get(data.getId());
            List<Long> prizeIds = StreamUtils.toList(wheelList, LuckyWheel::getPrizeId);

            List<LuckyRecord> recordList = luckyRecordMap.get(data.getId());
            if(CollectionUtils.isNotEmpty(recordList)){
                LuckyDetail luckyDetail = LuckyRecordUtil.getPrizeData(recordList);

                luckyDrawVO.setJoinCnt(recordList.size());
                luckyDrawVO.setPeopleCnt(luckyDetail.getPeopleCnt());
                luckyDrawVO.setPrizeCnt(luckyDetail.getPrizeCnt());
            }

            //根据转盘配置抽取奖品
            prizeList = StreamUtils.filter(prizeList, x-> prizeIds.contains(x.getId()));
            //排除谢谢参与
            prizeList = StreamUtils.filter(prizeList, x -> !LuckyPrizeTypeEnum.THANK_YOU.getCode().equals(x.getPrizeType()));
            String prizeNames = StreamUtils.joinStringFilter(prizeList, "、", LuckyPrize::getPrizeName, StringUtils::isNotBlank);
            luckyDrawVO.setPrizeStr(prizeNames);

            //余量小于20,预警
            if(LuckyDrawStatusEnum.NOT_STARTED.getCode().equals(luckyDrawVO.getStatus())
                    || LuckyDrawStatusEnum.IN_PROGRESS.getCode().equals(luckyDrawVO.getStatus())){
                List<LuckyPrizeVO> luckyPrizeVOList = BeanUtils.copyList(prizeList, LuckyPrizeVO::new);
                luckyPrizeVOList = StreamUtils.convertFilter(luckyPrizeVOList, prize -> {
                    prize.setRemainingCnt(prize.getTotalCnt() - prize.getLuckyCnt());
                    return prize;
                }, x -> x.getRemainingCnt() < 20);
                luckyDrawVO.setWarningPrizeList(luckyPrizeVOList);
            }

            return luckyDrawVO;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplement(List<LuckyPrizeSupDTO> supList) {
        if(CollectionUtils.isEmpty(supList)){
            throw new BusinessException("参数缺失！");
        }
        for (LuckyPrizeSupDTO prizeSup: supList) {
            LuckyPrize luckyPrize = luckyPrizeManager.get(prizeSup.getPrizeId());
            if(null == luckyPrize){
                throw new BusinessException(prizeSup.getPrizeName() + "奖品不存在！");
            }
            if(null != prizeSup.getAddCnt() && prizeSup.getAddCnt() > 0){
                //校验奖品实际库存
                checkStock(prizeSup.getPrizeId(), luckyPrize.getTargetId(), luckyPrize.getPrizeType(), Long.valueOf(luckyPrize.getTotalCnt() + prizeSup.getAddCnt()));
                //奖品发放总量补充
                luckyPrizeManager.addTotalCnt(luckyPrize.getId(), luckyPrize.getTotalCnt(),luckyPrize.getTotalCnt() + prizeSup.getAddCnt());
            }
        }
    }


    @Override
    public void setLuckyDrawStatus(Long id, LuckyDrawStatusEnum statusEnum) {
        LuckyDraw luckyDraw = luckyDrawManager.get(id);
        if(null == luckyDraw){
            throw new BusinessException("活动不存在！");
        }
        LuckyDraw lucky = new LuckyDraw();
        lucky.setId(id);
        if(statusEnum.equals(LuckyDrawStatusEnum.IN_PROGRESS)){
            if(!LuckyDrawStatusEnum.NOT_STARTED.getCode().equals(luckyDraw.getStatus())){
                throw new BusinessException("该活动不处于未上线状态不允许开始，请勿操作！");
            }
            lucky.setStatus(LuckyDrawStatusEnum.IN_PROGRESS.getCode());
            lucky.setStartTime(new Date());
        }else if(statusEnum.equals(LuckyDrawStatusEnum.END)){
            if(!LuckyDrawStatusEnum.IN_PROGRESS.getCode().equals(luckyDraw.getStatus())){
                throw new BusinessException("该活动不处于进行中状态不允许结束，请勿操作！");
            }
            lucky.setStatus(LuckyDrawStatusEnum.END.getCode());
            lucky.setEndTime(new Date());
        }
        luckyDrawManager.updateSelective(lucky);

        //通知
        milkProducerService.luckyDrawUpdate(id);
    }


    @Override
    public void checkStock(Long prizeId, Long targetId, Integer type, Long count) {
        LuckyPrizeTypeEnum prizeTypeEnum = LuckyPrizeTypeEnum.getEnum(type);
        if(null != prizeId){
            LuckyPrize prize = luckyPrizeManager.get(prizeId);
            if(null == prize){
                throw new BusinessException("奖品不存在！");
            }else{
                count = null == prize.getLuckyCnt() ? count : count - prize.getLuckyCnt();
            }
        }
        if(prizeTypeEnum.equals(LuckyPrizeTypeEnum.COUPON)){
            //优惠券库存校验
            checkCouponStock(targetId, count);

        }else if(prizeTypeEnum.equals(LuckyPrizeTypeEnum.GIFT)){
            //赠品库存校验
            checkGiftStock(targetId, count);
        }
    }

    @Override
    public List<LuckyDrawListVO> list(LuckyDrawSearchDTO dto) {
        LuckyDraw luckyDraw = luckyDrawManager.get(dto.getId());
        if(null == luckyDraw){
            throw new BusinessException("抽奖活动不存在！");
        }
        List<LuckyDetail> luckyDetailList = luckyDetailManager.findByLuckyId(dto.getId());
        if (CollectionUtils.isEmpty(luckyDetailList)) {
            return new ArrayList<>(0);
        }
        return StreamUtils.convert(luckyDetailList, data -> {
            LuckyDrawListVO luckyDrawListVO = new LuckyDrawListVO();
            luckyDrawListVO.setId(luckyDraw.getId());
            luckyDrawListVO.setLuckyName(luckyDraw.getLuckyName());
            luckyDrawListVO.setVisitPeopleCnt(data.getVisitPeopleCnt());
            luckyDrawListVO.setVisitCnt(data.getVisitCnt());
            luckyDrawListVO.setPeopleCnt(data.getPeopleCnt());
            luckyDrawListVO.setJoinCnt(data.getJoinCnt());
            luckyDrawListVO.setPrizeCnt(data.getPrizeCnt());
            luckyDrawListVO.setJoinRate(data.getJoinRate().multiply(BigDecimal.valueOf(100)) + "%");
            luckyDrawListVO.setPeopleJoinAve(data.getPeopleJoinAve());
            luckyDrawListVO.setOrderTaskPeopleCnt(data.getOrderTaskPeopleCnt());
            luckyDrawListVO.setOrderTaskAmount(data.getOrderTaskAmount());
            luckyDrawListVO.setShareCnt(data.getShareCnt());
            luckyDrawListVO.setSharePeopleCnt(data.getSharePeopleCnt());
            luckyDrawListVO.setShareInCnt(data.getShareInCnt());
            luckyDrawListVO.setVisitPageCnt(data.getVisitPageCnt());
            luckyDrawListVO.setVisitAmount(data.getVisitAmount());
            luckyDrawListVO.setConverDateStr(DateUtil.dateToString(data.getConverDate(), DateUtil.SIMPLE_YMD));
            luckyDrawListVO.setDrawAmount(data.getDrawAmount());
            return luckyDrawListVO;
        });
    }

    @Override
    public LuckyDrawPromoVO promotion(Long id) {
        LuckyDraw luckyDraw = luckyDrawManager.get(id);
        if(null == luckyDraw){
            throw new BusinessException("活动不存在！");
        }
        //推广
        MonitorPageDTO pageDTO = new MonitorPageDTO();
        pageDTO.setTargetId(id);
        pageDTO.setType(MoTypeEnum.LUCKY_DRAW.getCode());
        pageDTO.setPromotion(BasicFlagEnum.YES.getKey());
        List<MonitorPage> monitorPageList = monitorPageManager.getList(pageDTO);
        Map<Long, MonitorPage> monitorPageMap = StreamUtils.toMap(monitorPageList, MonitorPage::getTargetId);

        LuckyDrawPromoVO promoVO = new LuckyDrawPromoVO();
        promoVO.setId(id);
        promoVO.setLuckyName(luckyDraw.getLuckyName());

        MonitorPage monitorPage = monitorPageMap.get(id);
        if(null != monitorPage){
            promoVO.setMiniQrCode(monitorPage.getMiniQrcode());
            promoVO.setShortLink(monitorPage.getShortLink());
            promoVO.setPageUrl(monitorPage.getPageUrl());
            promoVO.setWxUrl(monitorPage.getWxUrl());
        }
        return promoVO;
    }

    private void checkGiftStock(Long skuId, Long count) {
        StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
        stockCalculateDTO.setSkuId(skuId);
        stockCalculateDTO.setStockNum(count);
        List<Long> skuIds = stockManager.checkStock(Lists.newArrayList(stockCalculateDTO));
        if(CollectionUtils.isNotEmpty(skuIds)){
            throw new BusinessException("赠品库存不足!");
        }
    }

    private void checkCouponStock(Long ruleId, Long count) {
        CouponRule rule = couponRuleManager.findById(ruleId);
        if(null == rule){
            throw new BusinessException("优惠券不存在!");
        }

        //校验剩余数量，发行量-已领取-发放总量
        if(rule.getIssuedQuantity() - rule.getReceiveCount() - count < 0 ){
            throw new BusinessException("优惠券库存不足!");
        }
    }

}
