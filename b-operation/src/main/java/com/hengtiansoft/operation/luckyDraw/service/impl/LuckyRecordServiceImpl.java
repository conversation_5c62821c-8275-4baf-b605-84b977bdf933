package com.hengtiansoft.operation.luckyDraw.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.operation.luckyDraw.service.LuckyRecordService;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.privilege.entity.dto.LuckyRecordSearchDTO;
import com.hengtiansoft.privilege.entity.po.LuckyDraw;
import com.hengtiansoft.privilege.entity.po.LuckyPrize;
import com.hengtiansoft.privilege.entity.po.LuckyRecord;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.LuckyPrizeTypeEnum;
import com.hengtiansoft.privilege.enums.LuckyRecordIsPrizeEnum;
import com.hengtiansoft.privilege.manager.LuckyDrawManager;
import com.hengtiansoft.privilege.manager.LuckyPrizeManager;
import com.hengtiansoft.privilege.manager.LuckyRecordManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.github.pagehelper.page.PageMethod.startPage;

@Service
public class LuckyRecordServiceImpl implements LuckyRecordService {

    @Autowired
    private LuckyRecordManager luckyRecordManager;

    @Autowired
    private LuckyPrizeManager luckyPrizeManager;

    @Autowired
    private CouponRuleManager couponRuleManager;

    @Autowired
    private SkuManager skuManager;

    @Autowired
    private LuckyDrawManager luckyDrawManager;


    @Override
    public PageVO<LuckyRecordListVO> page(LuckyRecordSearchDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<LuckyRecord> luckyRecordList = luckyRecordManager.getList(dto);
        if(CollectionUtils.isEmpty(luckyRecordList)){
            return PageUtils.emptyPage(dto);
        }
        List<Long> prizeIds = StreamUtils.filterConvert(luckyRecordList,
                record -> LuckyRecordIsPrizeEnum.WINNING.getCode().equals(record.getIsPrize()),
                LuckyRecord::getPrizeId);

        List<LuckyPrize> luckyPrizeList = luckyPrizeManager.findByIds(prizeIds);
        Map<Long, LuckyPrize> prizeMap = StreamUtils.toMap(luckyPrizeList, LuckyPrize::getId);
        //优惠券
        Map<Long, CouponRule> couponRuleMap = getCouponRuleMap(luckyPrizeList);
        //赠品sku
        Map<Long, Sku> skuMap = getSkuMap(luckyPrizeList);
        return PageUtils.convert(luckyRecordList, data ->{

            LuckyPrize luckyPrize = prizeMap.get(data.getPrizeId());

            LuckyRecordListVO luckyRecordListVO = new LuckyRecordListVO();
            luckyRecordListVO.setPhone(data.getPhone());
            luckyRecordListVO.setPrizeDate(data.getPrizeDate());
            if(LuckyRecordIsPrizeEnum.NOT_WINNING.getCode().equals(data.getIsPrize())){
                luckyRecordListVO.setPrizeInfo(LuckyRecordIsPrizeEnum.NOT_WINNING.getDesc());
            }else{
                if(LuckyPrizeTypeEnum.POINT.getCode().equals(luckyPrize.getPrizeType())){

                    luckyRecordListVO.setPrizeInfo(luckyPrize.getPoint() + "积分<br>" + LuckyPrizeTypeEnum.POINT.getDesc());
                }else if(LuckyPrizeTypeEnum.COUPON.getCode().equals(luckyPrize.getPrizeType())){

                    CouponRule couponRule = couponRuleMap.get(luckyPrize.getTargetId());
                    luckyRecordListVO.setPrizeInfo(luckyPrize.getPrizeName() + "<br>" + LuckyPrizeTypeEnum.COUPON.getDesc() + ":" + couponRule.getCouponName());
                }else if(LuckyPrizeTypeEnum.GIFT.getCode().equals(luckyPrize.getPrizeType())){

                    Sku sku = skuMap.get(luckyPrize.getTargetId());
                    luckyRecordListVO.setPrizeInfo(luckyPrize.getPrizeName() + "<br>" + LuckyPrizeTypeEnum.GIFT.getDesc() + ":" + sku.getSkuName());
                }
            }
            return luckyRecordListVO;
        });
    }

    @Override
    public LuckyRecordCountVO count(Long luckyId) {

        LuckyDraw luckyDraw = luckyDrawManager.get(luckyId);
        if(null == luckyDraw){
            throw new BusinessException("活动不存在！");
        }
        LuckyRecordCountVO luckyRecordCountVO = new LuckyRecordCountVO();
        luckyRecordCountVO.setLuckyName(luckyDraw.getLuckyName());
        luckyRecordCountVO.setStartTime(luckyDraw.getStartTime());
        luckyRecordCountVO.setEndTime(luckyDraw.getEndTime());

        LuckyRecordSearchDTO searchDTO = new LuckyRecordSearchDTO();
        searchDTO.setLuckyId(luckyId);
        List<LuckyRecord> luckyRecordList = luckyRecordManager.getList(searchDTO);
        //参与手机号去重
        List<LuckyRecord> distByPhoneList = StreamUtils.distinct(luckyRecordList, Comparator.comparing(LuckyRecord::getPhone));

        //中奖记录
        List<LuckyRecord> isPrizeList = StreamUtils.filter(luckyRecordList,
                x -> LuckyRecordIsPrizeEnum.WINNING.getCode().equals(x.getIsPrize()));

        //中奖手机号去重
        List<LuckyRecord> isPrizeDistByPhoneRecordList = StreamUtils.distinct(
                isPrizeList,
                Comparator.comparing(LuckyRecord::getPhone));

        luckyRecordCountVO.setLuckyId(luckyId);
        luckyRecordCountVO.setDrawCnt(distByPhoneList.size());
        luckyRecordCountVO.setPrizeCnt(isPrizeDistByPhoneRecordList.size());

        List<LuckyPrizeCountVO> prizeList = new ArrayList<>();
        Map<Long, List<LuckyRecord>> isPrizeCountMap = StreamUtils.group(isPrizeList, LuckyRecord::getPrizeId);

        for (Map.Entry<Long, List<LuckyRecord>> entry : isPrizeCountMap.entrySet()) {
            LuckyPrizeCountVO prizeCountVO = new LuckyPrizeCountVO();
            LuckyPrize luckyPrize = luckyPrizeManager.get(entry.getKey());
            prizeCountVO.setPrizeName(luckyPrize.getPrizeName());
            prizeCountVO.setPrizeCnt(entry.getValue().size());
            prizeCountVO.setId(luckyPrize.getId());
            prizeList.add(prizeCountVO);
        }
        luckyRecordCountVO.setPrizeList(prizeList);
        return luckyRecordCountVO;
    }

    @Override
    public void export(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        LuckyDraw luckyDraw = luckyDrawManager.get(dto.getLuckyId());
        Assert.notNull(luckyDraw, "抽奖活动不存在！");
        WriteSheet writeSheet = EasyExcel.writerSheet(luckyDraw.getLuckyName()).head(LuckyRecordDataExportVO.class).build();

        LuckyRecordSearchDTO searchDTO = new LuckyRecordSearchDTO();
        searchDTO.setPhone(dto.getPhone());
        searchDTO.setJoinStartTime(dto.getSearchTimeStart());
        searchDTO.setJoinEndTime(dto.getSearchTimeEnd());
        searchDTO.setPrizeId(dto.getPrizeId());
        searchDTO.setLuckyId(dto.getLuckyId());
        searchDTO.setIsPrize(dto.getIsPrize());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            PageHelper.startPage(pageNum, pageSize);
            List<LuckyRecord> luckyRecordList = luckyRecordManager.getList(searchDTO);
            Pagination pagination = PageUtils.extract(luckyRecordList);
            pages = pagination.getPages();
            pageNum ++;
            //中奖者
            List<Long> prizeIds = StreamUtils.filterConvert(luckyRecordList,
                    record -> LuckyRecordIsPrizeEnum.WINNING.getCode().equals(record.getIsPrize()),
                    LuckyRecord::getPrizeId);
            List<LuckyPrize> luckyPrizeList = luckyPrizeManager.findByIds(prizeIds);
            Map<Long, LuckyPrize> prizeMap = StreamUtils.toMap(luckyPrizeList, LuckyPrize::getId);
            //优惠券
            Map<Long, CouponRule> couponRuleMap = getCouponRuleMap(luckyPrizeList);
            //赠品sku
            Map<Long, Sku> skuMap = getSkuMap(luckyPrizeList);
            //封装导出数据
            List<LuckyRecordDataExportVO> luckyRecordDataExportVOList = buildLuckRecordExcelVO(luckyRecordList, prizeMap, couponRuleMap, skuMap);
            excelWriter.write(luckyRecordDataExportVOList, writeSheet);
        }while (pageNum <= pages);
    }

    private List<LuckyRecordDataExportVO> buildLuckRecordExcelVO(List<LuckyRecord> luckyRecordList, Map<Long, LuckyPrize> prizeMap, Map<Long, CouponRule> couponRuleMap, Map<Long, Sku> skuMap) {
            return StreamUtils.convert(luckyRecordList, data ->{

            LuckyPrize luckyPrize = prizeMap.get(data.getPrizeId());

            LuckyRecordDataExportVO luckyRecordExportVO = new LuckyRecordDataExportVO();
            luckyRecordExportVO.setPhone(data.getPhone());
            luckyRecordExportVO.setPrizeDate(data.getPrizeDate());
            luckyRecordExportVO.setPrizeDateStr(DateUtil.dateToString(data.getPrizeDate(), DateUtil.SIMPLE_FMT));
            if(LuckyRecordIsPrizeEnum.NOT_WINNING.getCode().equals(data.getIsPrize())){
                luckyRecordExportVO.setPrizeInfo(LuckyRecordIsPrizeEnum.NOT_WINNING.getDesc());
            }else{
                if(LuckyPrizeTypeEnum.POINT.getCode().equals(luckyPrize.getPrizeType())){

                    luckyRecordExportVO.setPrizeInfo(luckyPrize.getPoint() + "积分");
                    luckyRecordExportVO.setPrizeInfoExtra(LuckyPrizeTypeEnum.POINT.getDesc());
                }else if(LuckyPrizeTypeEnum.COUPON.getCode().equals(luckyPrize.getPrizeType())){

                    CouponRule couponRule = couponRuleMap.get(luckyPrize.getTargetId());
                    luckyRecordExportVO.setPrizeInfo(luckyPrize.getPrizeName());
                    luckyRecordExportVO.setPrizeInfoExtra(LuckyPrizeTypeEnum.COUPON.getDesc() + ":" + couponRule.getCouponName());
                }else if(LuckyPrizeTypeEnum.GIFT.getCode().equals(luckyPrize.getPrizeType())){

                    Sku sku = skuMap.get(luckyPrize.getTargetId());
                    luckyRecordExportVO.setPrizeInfo(luckyPrize.getPrizeName());
                    luckyRecordExportVO.setPrizeInfoExtra(LuckyPrizeTypeEnum.GIFT.getDesc() + ":" + sku.getSkuName());
                }
            }
            return luckyRecordExportVO;
        });
    }

    private Map<Long, Sku> getSkuMap(List<LuckyPrize> luckyPrizeList) {
        List<Long> skuIds = StreamUtils.filterConvert(luckyPrizeList,
                prize -> LuckyPrizeTypeEnum.GIFT.getCode().equals(prize.getPrizeType()),
                LuckyPrize::getTargetId);
        List<Sku> skuList = skuManager.selectByIds(skuIds);
        Map<Long, Sku> skuMap = StreamUtils.toMap(skuList, Sku::getId);
        return skuMap;
    }

    private Map<Long, CouponRule> getCouponRuleMap(List<LuckyPrize> luckyPrizeList) {
        List<Long> ruleIds = StreamUtils.filterConvert(luckyPrizeList,
                prize -> LuckyPrizeTypeEnum.COUPON.getCode().equals(prize.getPrizeType()),
                LuckyPrize::getTargetId);
        List<CouponRule> ruleList = couponRuleManager.findByIdList(ruleIds);
        Map<Long, CouponRule> couponRuleMap = StreamUtils.toMap(ruleList, CouponRule::getId);
        return couponRuleMap;
    }

    @Override
    public List<LuckyPrizeVO> prizeList(Long luckyId, String prizeName) {
        List<LuckyPrize> luckyPrizeList = luckyPrizeManager.getAll(luckyId, prizeName);
        List<LuckyPrizeVO> prizeVOList = StreamUtils.filterConvert(luckyPrizeList, prize ->
                        !LuckyPrizeTypeEnum.THANK_YOU.getCode().equals(prize.getPrizeType()),
                x ->{
                    if(LuckyPrizeTypeEnum.POINT.getCode().equals(x.getPrizeType())){
                        x.setPrizeName(LuckyPrizeTypeEnum.POINT.getDesc() + ":" + x.getPoint() + LuckyPrizeTypeEnum.POINT.getDesc() );
                    }else{
                        x.setPrizeName(LuckyPrizeTypeEnum.getDescByCode(x.getPrizeType()) + ":" + x.getPrizeName());
                    }
                    return BeanUtils.copy(x, LuckyPrizeVO::new);
                });

        return prizeVOList;
    }
}
