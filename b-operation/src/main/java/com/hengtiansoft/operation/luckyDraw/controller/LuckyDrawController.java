package com.hengtiansoft.operation.luckyDraw.controller;


import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.operation.luckyDraw.service.LuckyDrawService;
import com.hengtiansoft.privilege.entity.dto.LuckyDrawSaveDTO;
import com.hengtiansoft.privilege.entity.dto.LuckyDrawSearchDTO;
import com.hengtiansoft.privilege.entity.dto.LuckyPrizeSupDTO;
import com.hengtiansoft.privilege.entity.vo.*;
import com.hengtiansoft.privilege.enums.LuckyDrawStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "幸运大抽奖")
@RequestMapping("/luckyDraw")
public class LuckyDrawController {

    @Autowired
    private LuckyDrawService luckyDrawService;

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response<Object> save(@RequestBody LuckyDrawSaveDTO dto) {
        luckyDrawService.save(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("抽奖活动分页列表")
    @PostMapping("/page")
    public Response<PageVO<LuckyDrawListVO>> page(@RequestBody LuckyDrawSearchDTO dto) {
        return ResponseFactory.success(luckyDrawService.page(dto));
    }

    @ApiOperation("活动记录")
    @PostMapping("/list")
    public Response<List<LuckyDrawListVO>> list(@RequestBody LuckyDrawSearchDTO dto) {
        return ResponseFactory.success(luckyDrawService.list(dto));
    }

    @ApiOperation("详情")
    @GetMapping("/detail")
    public Response<LuckyDrawDetailVO> detail(@RequestParam Long id) {
        return ResponseFactory.success(luckyDrawService.detail(id));
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public Response<Object> delete(@RequestParam Long id) {
        luckyDrawService.delete(id);
        return ResponseFactory.success();
    }

    @ApiOperation("补充奖品")
    @PostMapping("/supplement")
    public Response<Object> supplement(@RequestBody List<LuckyPrizeSupDTO> dto) {
        luckyDrawService.supplement(dto);
        return ResponseFactory.success();
    }

    @ApiOperation("开始活动")
    @GetMapping("/start")
    public Response<Object> start(@RequestParam Long id) {
        luckyDrawService.setLuckyDrawStatus(id, LuckyDrawStatusEnum.IN_PROGRESS);
        return ResponseFactory.success();
    }

    @ApiOperation("结束活动")
    @GetMapping("/end")
    public Response<Object> end(@RequestParam Long id) {
        luckyDrawService.setLuckyDrawStatus(id, LuckyDrawStatusEnum.END);
        return ResponseFactory.success();
    }


    @ApiOperation("奖品库存校验")
    @GetMapping("/checkPrizeStock")
    public Response<Object> checkStock(@RequestParam(required = false) Long prizeId,
                                       @RequestParam(required = false) Long targetId,
                                       @RequestParam Integer type,
                                       @RequestParam Long count) {
        luckyDrawService.checkStock(prizeId, targetId, type, count);
        return ResponseFactory.success();
    }


    @ApiOperation("推广")
    @GetMapping("/promotion")
    public Response<LuckyDrawPromoVO> promotion(@RequestParam Long id) {
        return ResponseFactory.success(luckyDrawService.promotion(id));
    }
}
