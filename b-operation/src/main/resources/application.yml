##############固定不变的配置放这里###################
server:
  port: 8000
  servlet:
    context-path: /operation/xhr/
spring:
  profiles:
    active: '@profileActive@'
  redis:
    session:
      store-type: redis

    lettuce:
      shutdown-timeout: 1000ms
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 4
        max-wait: 3000ms
        #只有一下配置了，上面的连接池才有用
        time-between-eviction-runs: 60s


    database: 0
    timeout: 1000ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 下面为连接池的补充设置，应用到上面所有数据源中
      #初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
      initial-size: 5
      #最小连接池数量
      min-idle: 5
      #最大连接池数量
      max-active: 200
      # 获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置useUnfairLock属性为true使用非公平锁
      max-wait: 6000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 2000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 600000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      #用来检测连接是否有效的sql，要求是一个查询语句，常用select 'x'。如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会起作用。
      validation-query: SELECT 'x'
      #是否在连接空闲一段时间后检测其可用性
      test-while-idle: true
      #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      test-on-return: false
      #是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭。
      pool-prepared-statements: false
      #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
      max-pool-prepared-statement-per-connection-size: 20
      #   配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat
      use-global-data-source-stat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connect-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 配置监控服务器
      stat-view-servlet:
        #是否启用StatViewServlet（监控页面）默认值为false（考虑到安全问题默认并未启动，如需启用建议设置密码或白名单以保障安全）
        enabled: true
        reset-enable: false
        url-pattern: /druid/*
        # 添加IP白名单
        #allow:
        # 添加IP黑名单，当白名单和黑名单重复时，黑名单优先级更高
        #deny:
      web-stat-filter:
        #是否启用StatFilter默认值false
        enabled: true
        # 添加过滤规则
        url-pattern: /*
        # 忽略过滤格式
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        session-stat-max-count: 1000
        session-stat-enable: true
        profile-enable: true

#mybatis 配置，扫描所有*.xml,启用驼峰，禁用二级缓存
mybatis:
  mapper-locations:
    - classpath*:mapper/*.xml
  configuration:
    #默认全局执行sql超时时间25秒，更细粒度请在具体方法上配置，单位秒
    default-statement-timeout: 600
    #驼峰
    map-underscore-to-camel-case: true
    #禁用二级缓存
    cache-enabled: false
    #相当于禁用一级缓存
    local-cache-scope: STATEMENT

#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: false
  supportMethodsArguments: true
  params: count=countSql;pageNum=pageNum;pageSize=pageSize;

# security 配置
security:
  config:
    permit-all: false
    permit-path: /,/csrf,/captcha/**,/health,/monitorPage/getWxShortLink,/weixin/getUrlLink,/product/listWithMarket,/weixin/getUrlLinkV2

# 自定义配置   
#外部员工是否发放福利
selfConfig:
  giftAll: false


secretKey:
  public-activation-code: milk_card

# RSA 秘钥
rsa:
  private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCgTB/63DO2f/qOn917+I+I593rRqqXQ4i6aBjjiPLmPooOOqcX2JfUtRqd/9slDR44FeP57hXFhjXnUSaYaUx1xJayzuGUhSH6pIlzIyKmVg99fA01ccSayHa+dGU2Fx6bOHLBNVcwzh61u1YtrrbxcMD1qoQRaGPlLen44vpSVIVZSpysHgnShGjLZYoxLSXMCDXbojUpP9UiVyLokBlzhbX7N1teF/n86c2eeYpoKhGEgCefvbEVIFfE3hnMYkOGE7/n7S/5hiwU3NGKAy5w30rUVdiHlNd4yvUNgj045dbpnMKgUOTgy+zKv9gNftmXHUh7MV9L5DqSqx/Bl8VlAgMBAAECggEARWPuzBeawzvvaOJHbK0S5X8KEBlNK6HuhFMfww+7pnQYK0S0pJv8TfYACbmCUi21kS3YvI6WGiWZHAODI6MhmDF70ve/qx1esjOb1J23GC5VEksrJKGVLDPvQ077qUik1SjAdGb00YnIvX7VNX1WcHoQ7cfc1/RKmiEgEb+gARcp3Egl9MhCZaTHfoA6/tzXpWMUp/Na1fudl5gdbUGya0PCf3jJDCpcJjpT3s7CJgeXcvIm883SV1ebwyCgnjpdwDOcw8/wogHCayhEEdFe9IarQGDrQL77syd0OzTo5f6Nu51eRysdQjWLnvluba5BQmsq5ExBoMPYOeGwChgEWQKBgQDWr8T7Txv/BM7lu3Gz0k6V1rQosLoxnwdG4s2A6Jzqsa6edKCJ7jbp8N/5uNNmkNOTvqxY9YX4EaocG4OY25MhioksXtXhuJIjY9OSbd1EKPwgSMcVifv9s8EIvA77SZ+9etXiPnGITRy2nj5zwsTSWKdEvYMbpitQ3I1spTjjHwKBgQC/JPJquddLs8hXz0Fx7YjC3hOx0j16eiehlXsvkWaGgx/cSJp1fu+Wfl0BrDLvllokPE+hWUTS3V1WAPzExcHwvp6eOYOxfvXDPM5Gb7Y7XjJ3vePNtrSV0hOVfKm9v6JSuy3NA0Y+u52d+hJsulxWLS+ZvH9OVxPFlVMxPrIq+wKBgQCL1MRaY3Z6rekMnQA7/ZkN42BIuIMt2sTkmMj9U4URIFX6A0ClVjESQr+/aXDX9zC3pJOhBZoSr366+/jE0Q+N4yvhaIuqj4sv3c9qKoDZ6k/9vylckBk/b5slU0f2yEdxJNa2m7Fa887l60AGKjAESZX5Ie6JTbwc2OqMfzkERQKBgCGsB4xnipbKZ/uYBiZkutfhOUT1DWF7DWo4pAfALHh7tuweWh9iqiAvyICX7i0opl6FxV1eFXK0DlItQFoFoGGhot2qshldjTvEhW4BFfAlauyPJgdCLVPLOyn1WQ2VKao7URkxf3ljoaOHvyEqP0PrF9yUSTTqwA/pe0tMiSRrAoGAPi2HizAevqmCLWAntTYPZoPFsX1KnTH8a4fKLC5OF6drf6/V6MEMPTxP1VjcXNskQee003ryNfboHaP8mW6jvNtKzbridTQKDBgCmQkttTADSFz6OUcqEIGlnNSNWsjp3yrufYA1TCXrw0KXVBlwXXLUaaFjrqyKSP3udHO/bcM=
  dispatch-private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDdsrjPN8/qIRAHki+0DzivSFciIA6tzaTEcs11y6O5Y0whNqV+CN94CPWztJCJde+bmkGdH8FweRkZSieQ39Pwy4+5rV3vmC0bHUx/ySeWp5/k5lq7o7pS0ATEhLXTFMsJcM8RNmRFd7aUypOh4tYkw5iujOF6LH6NxLFOfghGxDoNJik8av+sgSOMqL0ovWzdvQVYtjqLsHaRZbBLcOOt+8etsp8WMwXgb+MJbW5/ORjOndmJXRnIwEQ+LzR+lDXalDbqX9lc3V/0vLZFMPGkTdD72TjteFcwVct5gl8mWVWDCGkbDjSObeHR0SPyXnVUv7LcGb8tqpmmeEpIv8XbAgMBAAECggEAbPoIXFvsCQO9lGsopdMBoy/lHPIzJP48KxDfrDKwxVVpO75X9S85YhDNbkuo7NkbMhrOfAT7GmV8j/DiqBVOYkQ93yC4Vykvqu9pBjuECUtWjrcpDKl70m9MmKk/k4XaFEbukmBxQ7CIAHXfwij16NZ4+/xmbZ7nipqQKaxMNzQdswtJhiwG7yXhpBEgosZJQVcQMKFtYrAVXkgpF8/N2OePKiWCcjaC+LzP3RDmxgs5Y52SMBO8IQQ4fldOVAdtwBqr44Fdqy/4rYKVFxwyDglOsIAbTakbH+BJnBb3TbPkfBYELvRB8VQDGVkpwcL9z0jgDT7TtXbwJDljdlCcMQKBgQDyFwINVx9b+Qx8Hoc6p29oN1n1NtevT7ngdHvLXgLH/EXXVScQFd1yirPRSobscGLqdb7vG3ULosN/u4x4cO+S/PvSSlp0bK7QS3qMV4AxiA0MmtvQ51xrPED6ohIM3+S1FyTBuGxZOlvWKz4iOa9+FnfGu1cCT6QYftgqM78wKQKBgQDqb8OVvs4/w1eolTbTMBM8owPbwjYJMexFb6I2fd5VONTj7XurKpIimjJHPkVBuP9VRVqtg283relNZOSjcKLNYjiIfqKl+g/3wgalmJaCaTqaS486aYSUine1HOM12EpJHsNxsLZN38Ww69/hojRaYAJEhmwxDWXf+PFhw1u2YwKBgGSsuMe/cWUYHt8G+wmjU6stSZW2Jt/a8dur1UdvYwyzln/EA6imzx6xG7Jvoch1vEzwhmR9MKGUIGTgI7x6isa/uiFrlk5QXCp3GRF/2em/QF37kGpBrKdTuVoBSCxed4fvhfOhtD0vDb304bPt9VMkGM9mXPqzD/S320FBn8JxAoGAalL0FocqDXsv13KNIezs3JRmXJXRYgw1RyyhGiut2WLkEteTPgcZrf3XDb6tNGiNzQOeY5F3sH38kLBQjdu4yoNgdm9Qnwwxj8F8uD/jCxRkkrPwKqLOkXvXXNnUJoiI8G62X2qw09LIWzwvFqs1KUPf42o/yWN1L8Y824T5pFcCgYAhFCHhvjdoNZPPhFrbmJp/UU+Yei5dLs58LBu4C5LgzDWBSeYNnnIp3UJ6HN+V40gnBN1MSkDOdOWNujFeP3Y82yhI5sAa0tn8YRhJOIBtmynhFCpfeH6k8Ry+fPPvbQWRXaYruxLzSu/n5iWjSBZk0lc0olhap25KXZRQCQZh8A==

divisionDate: 2022-02-20

tlog:
  pattern: '[$traceId]'

liteflow:
  rule-source: config/flow.el.xml
  when-thread-pool-isolate: true