
##############请根据不同的环境进行配置###################
spring:
  servlet:
    multipart:
      #是否支持文件上传
      enabled: true
      #单个文件大小
      max-file-size: 100MB
      #总上传大小
      max-request-size: 100MB
      #达到多少阈值以后写入磁盘
      file-size-threshold: 0B
      #是否支持 multipart 上传文件时懒加载
      resolve-lazily: false
      
# redis的ip和端口
  redis:
    host: r-bp1md6tjo9ikyexfjw.redis.rds.aliyuncs.com
    port: 6379
    database: 11
    password: Ryytn@2022!!
 # 数据库url和账号，密码
  datasource:
    dynamic:
      druid:
        # 下面为连接池的补充设置，应用到上面所有数据源中
        #初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
        initial-size: 5
        #最小连接池数量
        min-idle: 5
        #最大连接池数量
        max-active: 200
        # 获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置useUnfairLock属性为true使用非公平锁
        max-wait: 6000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        time-between-eviction-runs-millis: 2000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        min-evictable-idle-time-millis: 600000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        max-evictable-idle-time-millis: 900000
        #用来检测连接是否有效的sql，要求是一个查询语句，常用select 'x'。如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会起作用。
        validation-query: SELECT 'x'
        #是否在连接空闲一段时间后检测其可用性
        test-while-idle: true
      primary: db1 # 配置默认数据库
      datasource:
        db1: # 数据源1配置
          url: ***********************************************************************************************************************************************************************************************************************************************************************************************
          username: milkcard
          password: lqB0CXhTLG3Kcap
        db2: # 数据源2配置
          url: ***********************************************************************************************************************************************************************************************************************************************************************************************
          username: milkcard
          password: lqB0CXhTLG3Kcap
        db3: # 数据源3配置
          url: ************************************************************************************************************************************************************************************************************************************************************************************************
          username: ryytn_milkcard_query
          password: ryytn_milkcard_query_1qaz2wsx
        selectDB: # selectDB数据源配置
          url: ********************************************************************************************************************************************************************************************************************************************************************************************************
          username: admin
          password: Buzhongyao123

#通用mapper配置
mapper:
  # 主键自增回写方法
  identity: MYSQL

#自定义swagger配置展示项目信息
swagger:
  config:
    #是否实例化swagger实例
    enable: false
    #项目描述信息
    info:
      title: SpringBoot项目集成Swagger实例文档dev
      desc: 项目描述dev
      version: beta
#自定义缓存过期时间
cache:
  timeout:
    #默认全局设置缓存过期时间
    def: 100s
    #具体缓存空间的缓存过期时间
    #第一种写法
    #    namespaces: {user: 100s,role: 500s}
    #第二种写法
    namespaces:
      user: 100s

#是否开启快速参数验证默认未开启
validator:
  failFast:
    enabled: false
#是否启用连接池，默认不启用
lettuce:
  pool:
    enabled: false

# spring session 配置
server:
  servlet:
    session:
      #是否在重新启动之间保留会话数据
      persistent: true
      #session 无操作失效时间 两小时
      timeout: 7200

activation-code:
  publicUrl: /pages/coupon/getCouponFeedback?code=



#日志拦截器
request:
  logger:
    aspect:
      enable: true

local:
  tmp: /tmp/milk/

milk:
  local:
    domain: https://mini.ryytngroup.com
    h5Domain: https://h5.ryytngroup.com