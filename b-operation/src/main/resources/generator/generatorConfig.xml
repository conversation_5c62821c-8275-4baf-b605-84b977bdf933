<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- Mybatis generator配置，默认针对MySQL数据库，具体参考 http://www.mybatis.org/generator/configreference/xmlconfig.html -->
<generatorConfiguration>

    <!-- 配置文件的路径 -->
    <properties resource="generator/mybatisGenerator.properties"/>
    <!-- 每一张表只生成一个实体类 -->
    <context id="default" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 生成的Java文件的编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 自动识别数据库关键字，默认false，如果设置为true，根据SqlReservedWords中定义的关键字列表；
             一般保留默认值，遇到数据库关键字（Java关键字），使用columnOverride覆盖 -->
        <property name="autoDelimitKeywords" value="false"/>
        <!-- beginningDelimiter和endingDelimiter：指明数据库的用于标记数据库对象名的符号，
        比如ORACLE就是双引号 \&quot;，MYSQL默认是`反引号 -->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <!-- tk -->
        <!--这里最关键的参数就是 mappers，配置后生成的 Mapper 接口都会自动继承上改接口-->
        <!--
        其他参数的含义：
        •	caseSensitive 是否区分大小写，默认值 false。如果数据库区分大小写，这里就需要配置为 true，这样当表名为 USER 时，会生成 @Table(name = "USER") 注解，否则使用小写 user 时会找不到表。
        •	forceAnnotation 是否强制生成注解，默认 false，如果设置为 true，不管数据库名和字段名是否一致，都会生成注解（包含 @Table 和 @Column）。
        •	beginningDelimiter 和 endingDelimiter 开始和结束分隔符，对于有关键字的情况下适用。
        •	useMapperCommentGenerator 是否使用通用 Mapper 提供的注释工具，默认 true 使用，这样在生成代码时会包含字段的注释（目前只有 mysql 和 oracle 支持），设置 false 后会用默认的，或者你可以配置自己的注释插件。
        •	generateColumnConsts 在生成的 model中，增加字段名的常量，便于使用 Example 拼接查询条件的时候使用。
        •	lombok 增加 model 代码生成时，可以直接生成 lombok 的 @Date 注解， 使用者在插件配置项中增加 <property name="lombok" value="Getter,Setter,ToString,Accessors"/> 即可生成对应包含注解的 model 类。
        -->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.hengtiansoft.common.baseMapper.CrudMapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value="`"/>
            <property name="endingDelimiter" value="`"/>
            <property name="lombok" value="Data"/>
        </plugin>


        <!-- 数据库连接的信息：驱动类、连接地址、用户名、密码 -->
        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.username}"
                        password="${jdbc.password}">
            <!-- 用于获取表的注释信息等 -->
            <property name="useInformationSchema" value="true"/>
            <!--防止生成其他库同名表-->
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>


        <!-- Java类型解析配置 -->
        <javaTypeResolver>
            <!-- 将JDBC DECIMAL和NUMERIC类型解析为java.math.BigDecimal -->
            <property name="forceBigDecimals" value="false"/>
            <!-- 将JDBC DATE,TIME和TIMESTAMP类型解析为java.util.Date -->
            <property name="useJSR310Types" value="false"/>
        </javaTypeResolver>


        <!-- targetPackage生成Domain类的包路径，targetProject生成Domain类的物理路径 -->
        <javaModelGenerator targetPackage="${model.target.package}" targetProject="${model.target.project}">
            <!-- 不使用schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
            <!-- 不为String类型的setter方法自动加上trim -->
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>


        <!-- targetPackage生成XML文件的包路径，targetProject生成XML文件的物理路径-->
        <sqlMapGenerator targetPackage="${mapper.target.package}" targetProject="${mapper.target.project}">
            <!-- 不使用schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>


        <!-- targetPackage生成DAO接口的包路径，targetProject生成DAO接口的物理路径 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="${dao.target.package}" targetProject="${dao.target.project}">
            <!-- 不使用schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>


        <!-- 指定数据库表，schema数据库名，tableName数据表名，%表示匹配所有的表 -->
        <!--        <table tableName="%"-->
        <!--               enableCountByExample="false" enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false">-->
        <!--            &lt;!&ndash; generatedKey用于生成生成主键的方法，column表示主键名称，-->
        <!--                 sqlStatement=JDBC相当于在生成的insert元素上添加useGeneratedKeys="true"和keyProperty属性 &ndash;&gt;-->
        <!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="select SEQ_{1}.nextval from dual"/>&ndash;&gt;-->
        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->

        <!--            &lt;!&ndash;该元素会在根据表名计算类名之前先重命名表名，非常适合用于表名中都有公用的前缀字符串的时候，&ndash;&gt;-->
        <!--            &lt;!&ndash;比如表名为：T_USER,T_ORDER等；&ndash;&gt;-->
        <!--            &lt;!&ndash;那么就可以设置searchString为"^T"（无论表名是否大写，此处首字母一定为大写），并使用空白替换，&ndash;&gt;-->
        <!--            &lt;!&ndash;那么会生成User, Order对象，&ndash;&gt;-->
        <!--            &lt;!&ndash;注意，MBG是使用java.util.regex.Matcher.replaceAll来替换searchString和replaceString的&ndash;&gt;-->
        <!--            &lt;!&ndash;            <domainObjectRenamingRule searchString="^TbEros" replaceString=""/>&ndash;&gt;-->

        <!--            &lt;!&ndash;-->
        <!--                                该元素会在根据表中列名计算对象属性名之前先重命名列名，非常适合用于表中的列都有公用的前缀字符串的时候，-->
        <!--                                比如列名为：CUST_ID,CUST_NAME,CUST_EMAIL,CUST_ADDRESS等；-->
        <!--                                那么就可以设置searchString为"^CUST_"，并使用空白替换，那么生成的Customer对象中的属性名称就不是-->
        <!--                                custId,custName等，而是先被替换为ID,NAME,EMAIL,然后变成属性：id，name，email；-->
        <!--                                注意，MBG是使用java.util.regex.Matcher.replaceAll来替换searchString和replaceString的，-->
        <!--                                如果使用了columnOverride元素，该属性无效；-->
        <!--                                   <columnRenamingRule searchString="" replaceString=""/>-->
        <!--                                &ndash;&gt;-->
        <!--            &lt;!&ndash; 用来修改表中某个列的属性，MBG会使用修改后的列来生成domain的属性；-->
        <!--                column:要重新设置的列名；一个table元素中可以有多个columnOverride元素-->
        <!--                详细可参考http://www.mybatis.org/generator/configreference/columnOverride.html-->
        <!--                <columnOverride column="username" property="userName" />-->
        <!--            &ndash;&gt;-->
        <!--            &lt;!&ndash; ignoreColumn设置一个MGB忽略的列，如果设置了改列，那么在生成的domain中，生成的SQL中，都不会有该列出现-->
        <!--                column:指定要忽略的列的名字；delimitedColumnName：参考table元素的delimitAllColumns配置，默认为false；-->
        <!--                注意，一个table元素中可以有多个ignoreColumn元素-->
        <!--                <ignoreColumn column="deptId" delimitedColumnName=""/>-->
        <!--            &ndash;&gt;-->
        <!--        </table>-->

    </context>
</generatorConfiguration>