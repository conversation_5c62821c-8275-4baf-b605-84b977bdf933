<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 文件输出格式 -->
<!--    <property name="PATTERN" value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) |-%-5level [%thread] %c [%L] -| %msg%n" />-->
    <property name="PRO-PATTERN" value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) |-%-5level [%thread] %c [%L] -| %msg%n" />

    <!-- qa文件路径 -->
    <property name="QA_FILE_PATH" value="/opt/logs/operation" />
    <property name="QA_FILE_NAME" value="qa-b-operation" />

    <!-- uat文件路径 -->
    <property name="UAT_FILE_PATH" value="/opt/logs/operation" />
    <property name="UAT_FILE_NAME" value="uat-b-operation" />

    <!-- pt文件路径 -->
    <property name="PT_FILE_PATH" value="/opt/logs/operation" />
    <property name="PT_FILE_NAME" value="pt-b-operation" />



    <!-- prod文件路径 -->
    <property name="PRO_FILE_PATH" value="/opt/logs/operation/" />
    <property name="PRO_FILE_NAME" value="prod-b-operation" />

    <!-- 开发环境 -->
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <logger name="com.hengtiansoft" level="debug"/>
        <root level="info">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

<!--     测试环境 -->
    <springProfile name="qa">


        <!-- 每天产生一个文件 -->
        <appender name="QA-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 文件路径 -->
            <file>${QA_FILE_PATH}/${QA_FILE_NAME}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- 文件名称 -->
                <fileNamePattern>${QA_FILE_PATH}/${QA_FILE_NAME}.%d.%i.log</fileNamePattern>
                <!-- 文件最大保存历史数量 -->
                <MaxHistory>100</MaxHistory>
                <timeBasedFileNamingAndTriggeringPolicy  class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <!-- maxFileSize:这是活动文件的大小，默认值是10MB，测试时可改成1KB看效果 -->
                    <maxFileSize>300MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
            </rollingPolicy>
            <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>>
        </appender>


        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>>
        </appender>

        <logger name="com.hengtiansoft" level="debug"/>
        <root level="info">

            <appender-ref ref="QA-FILE" />
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>


    <springProfile name="pt">
        <!-- 每天产生一个文件 -->
        <appender name="PT-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 文件路径 -->
            <file>${PT_FILE_PATH}/${PT_FILE_NAME}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- 文件名称 -->
                <fileNamePattern>${PT_FILE_PATH}/${PT_FILE_NAME}.%d.%i.log</fileNamePattern>
                <!-- 文件最大保存历史数量 -->
                <MaxHistory>100</MaxHistory>
                <timeBasedFileNamingAndTriggeringPolicy  class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <!-- maxFileSize:这是活动文件的大小，默认值是10MB，测试时可改成1KB看效果 -->
                    <maxFileSize>300MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
            </rollingPolicy>
            <encoder>
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>

        <logger name="com.hengtiansoft" level="debug"/>
        <root level="info">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="PT-FILE" />
        </root>
    </springProfile>

    <!-- 用户测试环境 -->
    <springProfile name="uat">

        <!-- 每天产生一个文件 -->
        <appender name="UAT-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 文件路径 -->
            <file>${UAT_FILE_PATH}/${UAT_FILE_NAME}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- 文件名称 -->
                <fileNamePattern>${UAT_FILE_PATH}/${UAT_FILE_NAME}.%d.%i.log</fileNamePattern>
                <!-- 文件最大保存历史数量 -->
                <MaxHistory>100</MaxHistory>
                <timeBasedFileNamingAndTriggeringPolicy  class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <!-- maxFileSize:这是活动文件的大小，默认值是10MB，测试时可改成1KB看效果 -->
                    <maxFileSize>300MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
            </rollingPolicy>
            <encoder>
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>


        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>

        <logger name="io.lettuce.core.protocol" level="warn"/>
        <logger name="com.hengtiansoft" level="debug"/>
        <root level="info">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="UAT-FILE" />
        </root>
    </springProfile>

    <!-- 生产环境 -->
    <springProfile name="prod">
        <appender name="PROD_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${PRO_FILE_PATH}/${PRO_FILE_NAME}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${PRO_FILE_PATH}/${PRO_FILE_NAME}.%d.%i.log</fileNamePattern>
                <MaxHistory>7</MaxHistory>
                <timeBasedFileNamingAndTriggeringPolicy  class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <!-- maxFileSize:这是活动文件的大小，默认值是10MB，测试时可改成1KB看效果 -->
                    <maxFileSize>300MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
            </rollingPolicy>
            <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
                <pattern>${PRO-PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <logger name="io.lettuce.core.protocol" level="warn"/>

        <logger name="com.hengtiansoft" level="info"/>

        <root level="info">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="PROD_FILE" />
        </root>
    </springProfile>
</configuration>