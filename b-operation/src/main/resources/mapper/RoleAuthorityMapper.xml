<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.operation.role.auth.mapper.RoleAuthorityMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.operation.role.auth.entity.po.RoleAuthority">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="authority_id" jdbcType="BIGINT" property="authorityId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, role_id, authority_id
    </sql>

    <delete id="deleteByAuthorityId">

        delete from role_authority where authority_id = #{authorityId}

    </delete>

    <delete id="deleteByRoleId">

        delete from role_authority where role_id = #{roleId}

    </delete>

    <insert id="insertRoleAuthList">

        insert into role_authority(role_id, authority_id) VALUES
        <foreach collection="authorityIds" item="authorityId" separator=",">
            (#{roleId},#{authorityId})
        </foreach>

    </insert>
</mapper>