<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.activity.entity.mapper.RequestLogMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.operation.requestLogger.entity.po.RequestLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="module_id" jdbcType="INTEGER" property="moduleId" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_user_name" jdbcType="VARCHAR" property="operatorUserName" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
    <result column="uri" jdbcType="VARCHAR" property="uri" />
    <result column="controller_note" jdbcType="VARCHAR" property="controllerNote" />
    <result column="method_note" jdbcType="VARCHAR" property="methodNote" />
    <result column="execution_time" jdbcType="BIGINT" property="executionTime" />
    <result column="request_status" jdbcType="VARCHAR" property="requestStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="params" jdbcType="LONGVARCHAR" property="params" />
  </resultMap>
</mapper>