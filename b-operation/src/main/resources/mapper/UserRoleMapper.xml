<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.operation.role.auth.mapper.UserRoleMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.operation.role.auth.entity.po.UserRole">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="platform_type" jdbcType="TINYINT" property="platformType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, role_id, user_id, organization_id, platform_type
    </sql>

    <select id="findFirstIdByRoleId" resultType="java.lang.Long">

        select id from user_role where role_id = #{roleId} limit 1

    </select>

    <insert id="insertUserRole">

        insert into user_role(user_id, role_id) values
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{userId},#{roleId})
        </foreach>

    </insert>

    <select id="findRoleIdByUserId" resultType="java.lang.Long">

        select
        role_id
        from user_role
        where platform_type = #{platformType}
        and user_id = #{userId}

    </select>

    <delete id="deleteByUserId">

        delete
        from user_role
        where user_id = #{userId}
        and organization_id = #{organizationId}
        and platform_type = #{platformType}

    </delete>
</mapper>