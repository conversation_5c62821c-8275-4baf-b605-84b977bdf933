<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.operation.role.auth.mapper.RoleMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.operation.role.auth.entity.po.Role">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="platform_type" jdbcType="VARCHAR" property="platformType"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_description" jdbcType="VARCHAR" property="roleDescription"/>
        <result column="editable" jdbcType="TINYINT" property="editable"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, organization_id, platform_type, role_name, role_description, editable, create_id,
        update_id, create_time, update_time
    </sql>


    <select id="listRoleBrief" resultType="com.hengtiansoft.operation.role.auth.entity.vo.RoleBriefVO">

        select id, role_name
        from role
        where platform_type = #{platformType}
        and organization_id = #{organizationId}
        order by create_time desc

    </select>

    <select id="findByUserId" resultType="com.hengtiansoft.operation.role.auth.entity.vo.RoleBriefVO">

        select
        r.id, r.role_name
        from role r, user_role ur
        where ur.role_id = r.id
        and ur.user_id = #{userId}
        and r.organization_id = #{organizationId}
        and r.platform_type = #{platformType}

    </select>

    <select id="findByPage" resultType="com.hengtiansoft.operation.role.auth.entity.vo.RoleListVO">

        select
        r.id, r.role_name, r.editable, r.create_time
        from role r
        where r.organization_id = #{organizationId}
        and r.platform_type = #{platformType}
        <if test="null != roleName and '' != roleName">
            <bind name="roleName" value="'%' + roleName + '%'"/>
            and r.role_name like #{roleName}
        </if>
        order by r.create_time desc

    </select>

    <select id="findByRoleName" resultMap="BaseResultMap">

        select *
        from role
        where organization_id = #{organizationId}
        and platform_type = #{platformType}
        and role_name = #{roleName}
        <if test="null != exceptRoleId">
            and id != #{exceptRoleId}
        </if>

    </select>

    <select id="findById" resultMap="BaseResultMap">

        select *
        from role
        where id = #{roleId}
        and organization_id = #{organizationId}
        and platform_type = #{platformType}

    </select>

    <delete id="deleteById">

        delete
        from role
        where id = #{roleId}
        and platform_type = #{platformType}
        and organization_id = #{organizationId}

    </delete>

    <select id="validIdsCount" resultType="java.lang.Integer">

        select
        count(id)
        from role
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and platform_type = #{platformType}
        and organization_id = #{organizationId}

    </select>
</mapper>