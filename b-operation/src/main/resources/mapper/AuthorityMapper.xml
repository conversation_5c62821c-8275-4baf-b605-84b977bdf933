<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.operation.role.auth.mapper.AuthorityMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.operation.role.auth.entity.po.Authority">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="platform_type" jdbcType="TINYINT" property="platformType"/>
        <result column="authority_type" jdbcType="TINYINT" property="authorityType"/>
        <result column="authority_parent_id" jdbcType="BIGINT" property="authorityParentId"/>
        <result column="authority_name" jdbcType="VARCHAR" property="authorityName"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="frontend_unique_key" jdbcType="VARCHAR" property="frontendUniqueKey"/>
        <result column="backend_unique_key" jdbcType="VARCHAR" property="backendUniqueKey"/>
        <result column="show_status" jdbcType="TINYINT" property="showStatus"/>
        <result column="seq" jdbcType="TINYINT" property="seq"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, platform_type, authority_type, authority_parent_id, authority_name, alias, remark,
        frontend_unique_key, backend_unique_key, show_status, seq, create_time, update_time
    </sql>


    <select id="findByPlatformType" resultMap="BaseResultMap">

        select
        *
        from authority
        where
        platform_type = #{platformType}
        <if test="null != showStatusCode">
            and show_status = #{showStatusCode}
        </if>
        order by authority_parent_id ,seq asc

    </select>

    <select id="findByUserId" resultType="com.hengtiansoft.operation.role.auth.entity.vo.AuthVO">

        select
        distinct a.*
        from authority a, role_authority ra, user_role ur
        where
        a.platform_type = #{platformType}
        and ur.user_id = #{userId}
        and ur.role_id = ra.role_id
        and ra.authority_id = a.id

    </select>

    <select id="findByRoleId" resultType="com.hengtiansoft.operation.role.auth.entity.vo.AuthVO">

        select
        a.*
        from authority a , role_authority ra
        where
        a.show_status = #{showStatusCode}
        and a.id = ra.authority_id
        and ra.role_id = #{roleId}

    </select>

    <delete id="deleteById">

        delete
        from authority
        where
        id = #{id}
        and platform_type = #{platformType}

    </delete>

    <select id="findById" resultMap="BaseResultMap">

        select
        *
        from authority
        where
        id = #{id}
        and platform_type = #{platformType}

    </select>

    <select id="validIdsCount" resultType="java.lang.Integer">

        select
        count(id)
        from authority
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and platform_type = #{platformType}

    </select>

    <update id="update">
        update authority
        <trim prefix="set" suffixOverrides=",">
            <if test="null != authorityDTO.authorityName">authority_name = #{authorityDTO.authorityName},</if>
            <if test="null != authorityDTO.alias">alias= #{authorityDTO.alias},</if>
            <if test="null != authorityDTO.remark">remark= #{authorityDTO.remark},</if>
            <if test="null != authorityDTO.frontendUniqueKey">frontend_unique_key = #{authorityDTO.frontendUniqueKey},
            </if>
            <if test="null != authorityDTO.backendUniqueKey">backend_unique_key = #{authorityDTO.backendUniqueKey},</if>
            <if test="null != authorityDTO.showStatus">show_status = #{authorityDTO.showStatus},</if>
            <if test="null != authorityDTO.seq">seq=#{authorityDTO.seq},</if>
        </trim>
        where
        id = #{authorityDTO.id}
        and platform_type=#{platformType}

    </update>

</mapper>