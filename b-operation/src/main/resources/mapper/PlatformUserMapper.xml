<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.operation.role.auth.mapper.PlatformUserMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.operation.role.auth.entity.po.PlatformUser">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="account_type" jdbcType="TINYINT" property="accountType"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="realname" jdbcType="VARCHAR" property="realname"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, organization_id, account_type, username, password, realname, nickname, phone,
        status, create_id, update_id, create_time, update_time
    </sql>

    <select id="findByUsername" resultMap="BaseResultMap">

        select
        *
        from platform_user
        where
        status != #{exceptStatus}
        and organization_id = #{organizationId}
        and username = #{userName}
    </select>

    <select id="findById" resultMap="BaseResultMap">

        select
        *
        from platform_user
        where 1=1
        and id = #{userId}
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
        <if test="exceptStatusCode != null">
            and status != #{exceptStatusCode}
        </if>
    </select>

    <select id="checkEditable" resultMap="BaseResultMap">
        select * from platform_user where account_type = 2 and id = #{userId} and organization_id = #{organizationId}
    </select>

    <update id="updateStatus">

        update platform_user
        <trim prefix="set" suffixOverrides=",">
            <if test="null != status">status = #{status},</if>
            <if test="null != updateId">update_id= #{updateId},</if>
        </trim>
        where
        id = #{userId}
        and organization_id = #{organizationId}
        and account_type = #{accountType}

    </update>

    <select id="findByPage" resultType="com.hengtiansoft.operation.role.auth.entity.vo.PlatformUserVO">

        select
        pu.*,
        group_concat(distinct r.role_name separator ',') as roleNames
        from platform_user pu , user_role ur , role r
        where
        pu.status != #{exceptStatus}
        and pu.id = ur.user_id
        and r.id = ur.role_id
        and pu.organization_id = #{organizationId}
        <if test="userName != null and userName != ''">
            <bind name="userName"
                  value="@com.hengtiansoft.common.util.LikeSearchUtils@allMatch(userName)"/>
            and pu.username like #{userName}
        </if>

        <if test="realName != null and realName != ''">
            <bind name="realName"
                  value="@com.hengtiansoft.common.util.LikeSearchUtils@allMatch(realName)"/>
            and pu.realname like #{realName}
        </if>

        <if test="phone != null and phone != ''">
            <bind name="phone"
                  value="@com.hengtiansoft.common.util.LikeSearchUtils@allMatch(phone)"/>
            and pu.phone like #{phone}
        </if>

        <if test="status != null and status != 0">
            and pu.status = #{status}
        </if>

        <if test="roleId != 0 and roleId != null">
            and r.id = #{roleId}
        </if>
        group by pu.id
        order by pu.create_time desc

    </select>
</mapper>