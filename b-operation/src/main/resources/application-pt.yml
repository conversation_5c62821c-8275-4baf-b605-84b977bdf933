##############请根据不同的环境进行配置###################
spring:
  servlet:
    multipart:
      #是否支持文件上传
      enabled: true
      #单个文件大小
      max-file-size: 100MB
      #总上传大小
      max-request-size: 100MB
      #达到多少阈值以后写入磁盘
      file-size-threshold: 0B
      #是否支持 multipart 上传文件时懒加载
      resolve-lazily: false
  # redis的ip和端口
  redis:
    host: *************
    port: 6379
    database: 2
    password: Hangzhou@123
  # 数据库url和账号，密码
  datasource:
    url: ************************************************************************************************************************************************************************************************************************************************************
    username: test
    password: hengtian@123

#通用mapper配置
mapper:
  # 主键自增回写方法
  identity: MYSQL

#自定义swagger配置展示项目信息
swagger:
  config:
    #是否实例化swagger实例
    enable: true
    #项目描述信息
    info:
      title: SpringBoot项目集成Swagger实例文档dev
      desc: 项目描述dev
      version: beta
#自定义缓存过期时间
cache:
  timeout:
    #默认全局设置缓存过期时间
    def: 100s
    #具体缓存空间的缓存过期时间
    #第一种写法
#    namespaces: {user: 100s,role: 500s}
    #第二种写法
    namespaces:
      user: 100s

#是否开启快速参数验证默认未开启
validator:
  failFast:
    enabled: false
#是否启用连接池，默认不启用
lettuce:
  pool:
    enabled: false

# spring session 配置
server:
  servlet:
    session:
      #是否在重新启动之间保留会话数据
      persistent: true
      #session 无操作失效时间 两小时
      timeout: 7200

activation-code:
  publicUrl: /pages/coupon/getCouponFeedback?code=

#日志拦截器
request:
  logger:
    aspect:
      enable: false