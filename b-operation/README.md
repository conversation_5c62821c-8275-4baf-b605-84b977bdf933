# user-service
#### 主要提供一些示例代码，所有业务代码基本上都在此包编写
1. `config`包主要包含`PageHelperConfiguration`和`PageHelperProperties`这两个组件，主要是为pageHelper提供支持，一般不需要修改。
以下提供`application.yml`文件的配置和注释.
    ```
        #pagehelper分页插件配置
        pagehelper:
          #使用的方言
          helperDialect: mysql
          #
          reasonable: true
          #是否支持
          supportMethodsArguments: true
          #
          params: count=countSql;pageNum=pageNum;pageSize=pageSize;
    ```
2. `swagger`包主要包含 `SwaggerApiInfo`和`SwaggerConfig`两个配置类，主要用来配置swagger api文档。
    ```
        #自定义swagger配置展示项目信息
        swagger:
          config:
            #是否实例化swagger实例
            enable: true
            #项目描述信息
            info:
              title: SpringBoot项目集成Swagger实例文档dev
              desc: 项目描述dev
              version: beta
    ```
3. `util`工具包目前只包含`PageHelperUtil`类，主要提供用来转化pagehelper分页查询出来的`List`结果转换为
    响应对象common包的`PageInfo`，具体使用请查看api注释
4. 示例中`Application`为spring boot 启动类， `@SpringBootApplication`注解主要提供自动配置，组件扫描和属性扫描