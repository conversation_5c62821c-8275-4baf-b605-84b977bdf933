<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>milk-server</artifactId>
		<groupId>com.hengtiansoft</groupId>
		<version>1.0.0</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>b-operation</artifactId>


	<dependencies>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>3.4.1</version>
		</dependency>
        <!-- 工程依赖start -->
		<dependency>
			<groupId>com.hengtiansoft</groupId>
			<artifactId>common</artifactId>
			<version>${project.version}</version>
		</dependency>
		
		      <dependency>
            <groupId>com.hengtiansoft</groupId>
            <artifactId>security-support</artifactId>
            <version>${project.version}</version>
        </dependency>

		<dependency>
			<groupId>com.hengtiansoft</groupId>
			<artifactId>activity-service</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.hengtiansoft</groupId>
			<artifactId>item-service</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.hengtiansoft</groupId>
			<artifactId>order-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		
		<dependency>
            <groupId>com.hengtiansoft</groupId>
            <artifactId>content-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.hengtiansoft</groupId>
            <artifactId>user-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hengtiansoft</groupId>
            <artifactId>third-part</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 工程依赖end -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>


		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>tk.mybatis</groupId>
			<artifactId>mapper-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>


        <dependency>
            <groupId>com.hengtiansoft</groupId>
            <artifactId>address-service</artifactId>
			<version>${project.version}</version>
        </dependency>
		<dependency>
			<groupId>com.hengtiansoft</groupId>
			<artifactId>pay-service</artifactId>
			<version>${project.version}</version>
		</dependency>
        <dependency>
            <groupId>com.hengtiansoft</groupId>
            <artifactId>data-analysis</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-spring-boot-starter</artifactId>
		</dependency>
        
    </dependencies>

	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<profileActive>dev</profileActive>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<profileActive>prod</profileActive>
			</properties>
		</profile>
		<profile>
			<id>qa</id>
			<properties>
				<profileActive>qa</profileActive>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<properties>
				<profileActive>uat</profileActive>
			</properties>
		</profile>
	</profiles>
	<build>
    <finalName>operation-1.0.0</finalName>




		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>templete/**</include>
					<include>application.yml</include>
					<include>application-**.yml</include>
					<include>**/**.xml</include>
					<include>pay/**</include>
					<include>config/**</include>
				</includes>
			</resource>
		</resources>



		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${boot.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration> 
			</plugin>
			
			<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
			
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>${mybatis.generator.plugin.version}</version>
				<configuration>
					<configurationFile>
						${basedir}/src/main/resources/generator/generatorConfig.xml
					</configurationFile>
					<verbose>true</verbose>
					<overwrite>true</overwrite>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>${mysql.version}</version>
					</dependency>
					<dependency>
						<groupId>tk.mybatis</groupId>
						<artifactId>mapper</artifactId>
						<version>${tkMapper.generator.version}</version>
					</dependency>
				</dependencies>
			</plugin>

		</plugins>
	</build>

</project>