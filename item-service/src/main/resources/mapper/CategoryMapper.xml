<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CategoryMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Category">
        <!-- WARNING - @mbg.generated -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cate_code" jdbcType="VARCHAR" property="cateCode"/>
        <result column="cate_name" jdbcType="VARCHAR" property="cateName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="cate_path" jdbcType="VARCHAR" property="catePath"/>
        <result column="name_path" jdbcType="VARCHAR" property="namePath"/>
        <result column="cate_level" jdbcType="INTEGER" property="cateLevel"/>
        <result column="link" jdbcType="VARCHAR" property="link"/>
        <result column="sort" jdbcType="TINYINT" property="sort"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="delflag" jdbcType="INTEGER" property="delflag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operater" jdbcType="VARCHAR" property="operater"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- WARNING - @mbg.generated -->
        id, cate_code, cate_name, parent_id, cate_path, name_path, cate_level, link, sort,
        pic_url, description, delflag, create_time, update_time, operater
    </sql>

    <select id="selectAllSubIds" resultType="java.lang.Long">
        SELECT id FROM item_category b WHERE b.cate_path LIKE CONCAT((SELECT cate_path FROM item_category a WHERE a.id =
        #{cateId}),'%')
    </select>

    <select id="cateList" resultType="com.hengtiansoft.item.entity.po.Category">
        SELECT * FROM item_category WHERE id IN (SELECT ic.parent_id as id FROM item_product ip
        LEFT JOIN item_category ic on ic.id =ip.cate_id WHERE ip.sale_status=1 and ip.delflag=0) ORDER BY
        sort ASC,update_time DESC
    </select>

    <select id="catePublicList" resultType="com.hengtiansoft.item.entity.po.Category">
        SELECT * FROM item_category icc WHERE icc.id IN (
        SELECT ic.parent_id as id FROM item_product ip
        INNER JOIN item_category ic on ic.id =ip.cate_id
        WHERE ip.sale_status=1 and ip.delflag=0 and ic.is_public=0 and ip.product_type in('c','d','s') and ip.privilege_flag=0 and ip.enable_show=1
        <if test="privilegePoductIds != null and privilegePoductIds.size() > 0" >
    UNION
        SELECT ic.parent_id as id FROM item_product ip
        INNER JOIN item_category ic on ic.id =ip.cate_id
        WHERE ip.sale_status=1 and ip.delflag=0 and ic.is_public=0 and ip.product_type in('c','d','s') and ip.privilege_flag=1 and ip.enable_show=1 and
        ip.id in
            <foreach collection="privilegePoductIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    UNION
        SELECT ic.id FROM item_category ic
        WHERE ic.is_public=0 and ic.cate_type = 5 and cate_level = 1
        ) and icc.is_public=0 ORDER BY
        sort ASC,update_time DESC
    </select>

    <select id="secondCates" resultType="com.hengtiansoft.item.entity.po.Category">
        SELECT * FROM item_category ic
        RIGHT JOIN (SELECT distinct cate_id FROM item_product WHERE delflag=0 AND sale_status =1) ip ON ic.id =
        ip.cate_id WHERE ic.parent_id =#{parentId}
        AND
        ic.delflag =0 ORDER BY ic.sort ASC ,ic.update_time DESC
    </select>

    <select id="selectAllSubIdsByParent" resultType="java.lang.Long">
        SELECT id FROM item_category b WHERE b.parent_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="allFirstCategory" resultType="com.hengtiansoft.item.entity.dto.CategoryProductNumDTO">
        select t.id, sum(t.number) as count from (SELECT ic.parent_id as id,count(1) as number FROM item_product ip
        INNER JOIN item_category ic on ic.id =ip.cate_id
        WHERE ip.sale_status=1 and ip.delflag= 0 and ic.is_public=0 and ip.product_type in('c','d','s') and
        ip.privilege_flag=0 and ip.enable_show=1 and ic.parent_id in
        <foreach collection="parentIds" index="index" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        group by ic.parent_id
        <if test="privilegePoductIds != null and privilegePoductIds.size() > 0">
            UNION
            SELECT ic.parent_id as id,count(1) as number FROM item_product ip
            INNER JOIN item_category ic on ic.id =ip.cate_id
            WHERE ip.sale_status=1 and ip.delflag=0 and ic.is_public=0 and ip.product_type in('c','d','s') and
            ip.privilege_flag=1 and ip.enable_show=1 and ic.parent_id in
            <foreach collection="parentIds" index="index" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
            and ip.id in
            <foreach collection="privilegePoductIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            group by ic.parent_id
        </if>
        ) t group by t.id
    </select>
    <select id="selectSubsByParIds" resultMap="BaseResultMap">
        select * from item_category where parent_id in
        <foreach collection="firstCateIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
  
  