<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductFrontClassifyActivityMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductFrontClassifyActivity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="banner_url" jdbcType="VARCHAR" property="bannerUrl" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>
</mapper>