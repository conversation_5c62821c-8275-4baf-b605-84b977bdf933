<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.SkuAttrMapper">
	<resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.SkuAttr">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="sku_id" jdbcType="BIGINT" property="skuId" />
		<result column="attr_name" jdbcType="VARCHAR" property="attrName" />
		<result column="attr_value" jdbcType="VARCHAR" property="attrValue" />
		<result column="sort" jdbcType="TINYINT" property="sort" />
	</resultMap>

</mapper>