<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardMediaMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardMedia">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
    <result column="back_pic_url" jdbcType="VARCHAR" property="backPicUrl" />
    <result column="card_type" jdbcType="INTEGER" property="cardType" />
    <result column="face_pic_url" jdbcType="VARCHAR" property="facePicUrl" />
  </resultMap>
</mapper>