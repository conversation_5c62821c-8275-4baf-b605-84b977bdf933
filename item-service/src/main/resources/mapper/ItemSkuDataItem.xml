<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemSkuDataItemMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemSkuDataItem">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
        <result column="sku_ecode" jdbcType="VARCHAR" property="skuEcode" />
        <result column="number" jdbcType="INTEGER" property="number" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="delflag" jdbcType="TINYINT" property="delflag" />
    </resultMap>
    <update id="deleteBySkuCode">
        update item_sku_data_item set delflag = 1, update_time = now() where sku_code = #{skuCode}
    </update>
    <select id="selectBySkuCodeList" resultType="com.hengtiansoft.item.entity.po.ItemSkuDataItem">
        select * from item_sku_data_item
                 where delflag = 0 and sku_code in
        <foreach collection="skuCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
