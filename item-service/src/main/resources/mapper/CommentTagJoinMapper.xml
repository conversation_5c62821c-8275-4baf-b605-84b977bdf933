<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CommentTagJoinMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CommentTagJoin">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tag_id" jdbcType="BIGINT" property="tagId" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>
  <select id="selectCategoryByTagIds" resultType="com.hengtiansoft.item.entity.dto.CommentTagJoinCategoryDTO">
      SELECT * FROM comment_tag_join tj
      INNER JOIN item_category cate on tj.target_id = cate.id
      WHERE
            tj.type = 2
        AND tj.delflag = 0
        AND cate.cate_level = 1
        AND tj.tag_id in
      <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
  </select>

    <select id="selectTagByTargetIds" resultType="com.hengtiansoft.item.entity.dto.CommentTagJoinCategoryDTO">
        SELECT ct.id as tagId, ct.name, ct.create_time as createTime, tj.target_id as targetId FROM comment_tag_join tj
        INNER JOIN comment_tag ct on tj.tag_id = ct.id
        WHERE
            tj.type = 2
        AND tj.delflag = 0
        AND tj.target_id in
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>