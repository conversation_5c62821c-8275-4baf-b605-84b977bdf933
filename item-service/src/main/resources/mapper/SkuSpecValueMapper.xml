<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.SkuSpecValueMapper">

	<resultMap id="FullBaseResultMap" type="com.hengtiansoft.item.entity.dto.SkuSpecValueDTO">
		<id column="ssv_id" property="id" jdbcType="BIGINT" />
		<result column="ssv_spec_id" property="specId" jdbcType="BIGINT" />
		<result column="ssv_spec_value_id" property="specValueId" jdbcType="BIGINT" />
		<result column="ssv_sku_id" property="skuId" jdbcType="BIGINT" />
		<association property="spec" javaType="com.hengtiansoft.item.entity.dto.SpecDTO">
			<id column="s_id" property="id" jdbcType="BIGINT" />
			<result column="s_spec_group_id" property="specGroupId" jdbcType="BIGINT" />
			<result column="s_spec_name" property="specName" jdbcType="VARCHAR" />
			<result column="s_spec_type" property="specType" jdbcType="VARCHAR" />
			<result column="s_display_type" property="displayType" jdbcType="VARCHAR" />
			<result column="s_sort" property="sort" jdbcType="TINYINT" />
			<result column="s_searching" property="searching" jdbcType="TINYINT" />
		</association>
		<association property="specValue" javaType="com.hengtiansoft.item.entity.dto.SpecValueDTO">
			<id column="sv_id" property="id" jdbcType="BIGINT" />
			<result column="sv_spec_id" property="specId" jdbcType="BIGINT" />
			<result column="sv_value" property="value" jdbcType="VARCHAR" />
			<result column="sv_pic_url" property="picUrl" jdbcType="VARCHAR" />
			<result column="sv_sort" property="sort" jdbcType="TINYINT" />
		</association>
	</resultMap>

	<select id="selectDtoBySkuId" resultMap="FullBaseResultMap">
		SELECT ssv.id as ssv_id , ssv.spec_id as ssv_spec_id , ssv.spec_value_id as ssv_spec_value_id, ssv.sku_id as ssv_sku_id,
		<include refid="com.hengtiansoft.item.mapper.SpecMapper.dtoField" />
		,
		<include refid="com.hengtiansoft.item.mapper.SpecValueMapper.dtoField" />
		FROM item_sku_spec_value ssv , item_spec s , item_spec_value sv
		WHERE ssv.spec_id = s.id AND ssv.spec_value_id = sv.id
		AND ssv.sku_id = #{skuId}
		ORDER BY ssv.spec_id , ssv.spec_value_id
	</select>

	<select id="selectDtoBySkuIds" resultMap="FullBaseResultMap">
		SELECT ssv.id as ssv_id , ssv.spec_id as ssv_spec_id , ssv.spec_value_id as ssv_spec_value_id, ssv.sku_id as ssv_sku_id,
		<include refid="com.hengtiansoft.item.mapper.SpecMapper.dtoField" />
		,
		<include refid="com.hengtiansoft.item.mapper.SpecValueMapper.dtoField" />
		FROM item_sku_spec_value ssv , item_spec s , item_spec_value sv
		WHERE ssv.spec_id = s.id AND ssv.spec_value_id = sv.id
		AND ssv.sku_id IN
		<foreach collection="skuIds" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		ORDER BY ssv.spec_id , ssv.spec_value_id
	</select>
	
	<delete id="deleteByProductIdAndSpecIdsNotIn">
        DELETE ssv FROM item_sku_spec_value ssv , item_sku s WHERE s.id = ssv.sku_id
        AND s.product_id = #{productId}
        AND ssv.spec_id NOT IN
        <foreach collection="specIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>