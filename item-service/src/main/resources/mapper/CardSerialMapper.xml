<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardSerialMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardSerial">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="number_prefix" jdbcType="VARCHAR" property="numberPrefix"/>
        <result column="serial_number" jdbcType="BIGINT" property="serialNumber"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
</mapper>