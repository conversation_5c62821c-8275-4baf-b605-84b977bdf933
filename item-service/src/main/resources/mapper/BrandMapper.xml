<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.BrandMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Brand">
        <!-- WARNING - @mbg.generated -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_code" jdbcType="VARCHAR" property="brandCode"/>
        <result column="brand_name" jdbcType="VARCHAR" property="brandName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="sort" jdbcType="TINYINT" property="sort"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="delflag" jdbcType="INTEGER" property="delflag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operater" jdbcType="VARCHAR" property="operater"/>
    </resultMap>
    <select id="findBigProducts" resultType="java.lang.Long">
        SELECT ip.id FROM item_brand ib,item_product ip WHERE ib.id =ip.brand_id AND ib.sort=0 AND ib.delflag =0 AND
        ip.sale_status=1 AND ip.delflag =0
    </select>

</mapper>