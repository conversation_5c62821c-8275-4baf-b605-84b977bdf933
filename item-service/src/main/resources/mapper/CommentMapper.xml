<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CommentMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Comment">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="rate" jdbcType="TINYINT" property="rate" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="show" jdbcType="TINYINT" property="show" />
    <result column="audit" jdbcType="TINYINT" property="audit" />
    <result column="mode" jdbcType="TINYINT" property="mode" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
    <result column="imgs" jdbcType="LONGVARCHAR" property="imgs" />
  </resultMap>

  <update id="batchUpdate" parameterType="java.util.List">
    update comment
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`show` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.show}
        </foreach>
      </trim>
      <trim prefix="`operator` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.operator}
        </foreach>
      </trim>
      <trim prefix="`audit` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.audit}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="getSpuList" resultMap="BaseResultMap">
    select * from comment c
    inner join comment_tag_join ctj on ctj.target_id=c.id
    where c.delflag=0 and ctj.delflag=0 and ctj.type=1 and c.`show`=1
    and c.spu_id=#{spuId} and ctj.tag_id=#{tagId}
    order by date_format(c.create_time,'%Y-%m-%d') desc , c.rate asc, c.mode desc, c.create_time desc
  </select>
    <select id="statisticCommentUser" resultType="java.lang.Long">
      select distinct(user_id)
      from comment
      where delflag = 0 and `show`= 1 and audit = 1
      <if test="begin != null">
        and create_time >= #{begin}
      </if>
      <if test="end != null">
        and create_time &lt;= #{end}
      </if>
    </select>

</mapper>