<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.AttrMapper">
	<resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Attr">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="attr_group_id" jdbcType="BIGINT" property="attrGroupId" />
		<result column="attr_name" jdbcType="VARCHAR" property="attrName" />
		<result column="attr_type" jdbcType="TINYINT" property="attrType" />
		<result column="sort" jdbcType="TINYINT" property="sort" />
		<result column="searching" jdbcType="TINYINT" property="searching" />
		<result column="delflag" jdbcType="INTEGER" property="delflag" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="operater" jdbcType="VARCHAR" property="operater" />
	</resultMap>

</mapper>