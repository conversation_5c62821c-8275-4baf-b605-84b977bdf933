<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CouponLogMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CouponLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="related_type" jdbcType="TINYINT" property="relatedType"/>
        <result column="related_id" jdbcType="VARCHAR" property="relatedId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="activation_time" jdbcType="INTEGER" property="activationTime"/>
        <result column="dead_time" jdbcType="INTEGER" property="deadTime"/>
        <result column="total_number" jdbcType="INTEGER" property="totalNumber"/>
        <result column="rest_number" jdbcType="INTEGER" property="restNumber"/>
        <result column="free_number" jdbcType="INTEGER" property="freeNumber"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_at" jdbcType="INTEGER" property="createdAt"/>
        <result column="updated_at" jdbcType="INTEGER" property="updatedAt"/>
        <result column="deleted_at" jdbcType="INTEGER" property="deletedAt"/>
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
    </resultMap>
</mapper>