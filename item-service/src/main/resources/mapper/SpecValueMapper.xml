<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.SpecValueMapper">

	<sql id="dtoField">
		sv.id AS sv_id,
		sv.spec_id AS sv_spec_id,
		sv.value AS sv_value,
		sv.pic_url AS sv_pic_url,
		sv.sort sv_sort
	</sql>

	<update id="deleteBySpecIdAndNotExist">
		update item_spec_value set delflag = 1 where spec_id = #{specId}
		AND value NOT IN
		<foreach collection="list" item="value" open="(" separator="," close=")">
			#{value}
		</foreach>
	</update>
</mapper>