<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductSpecMapper">


	<insert id="batchInsertByProductId" useGeneratedKeys="true">
		insert into item_product_spec (
		product_id,spec_id
		)
		values
		<foreach collection="list" item="item" separator=",">
			(
			#{productId,jdbcType=BIGINT},#{item,jdbcType=BIGINT}
			)
		</foreach>
	</insert>

</mapper>
