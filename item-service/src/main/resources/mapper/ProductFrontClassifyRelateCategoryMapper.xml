<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductFrontClassifyRelateCategoryMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductFrontClassifyRelateCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_front_classify_id" jdbcType="BIGINT" property="productFrontClassifyId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>

  <update id="deleteByClassifyId">
    update product_front_classify_relate_category
    set delflag = 1, update_time = now()
    where product_front_classify_id = #{classifyId}
  </update>
</mapper>