<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.SkuStockMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.SkuStock">
        <!-- WARNING - @mbg.generated -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="stock_num" jdbcType="BIGINT" property="stockNum"/>
        <result column="warning_num" jdbcType="BIGINT" property="warningNum"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- WARNING - @mbg.generated -->
        id, product_id, sku_id, stock_num, warning_num, warehouse_id, create_time, update_time
    </sql>


    <!-- 批量插入库存单位 -->
    <insert id="insertBatch" parameterType="java.util.List">

        INSERT INTO
        item_sku_stock

        (product_id, sku_id, stock_num, warning_num, warehouse_id)

        VALUES
        <foreach collection="list" index="index" item="stock" separator=",">
            (#{stock.productId},#{stock.skuId},#{stock.stockNum},#{stock.warningNum},#{stock.warehouseId})
        </foreach>

    </insert>


    <!-- 批量扣减库存单位 -->
    <update id="decrease"  parameterType="com.hengtiansoft.item.entity.dto.StockCalculateDTO">
            update item_sku_stock issk, item_sku isk
            set
            issk.stock_num = issk.stock_num - #{stock.stockNum},
            isk.stock = issk.stock_num - #{stock.stockNum}
            where
            isk.id = #{stock.skuId}
            and issk.sku_id = #{stock.skuId}
            and issk.stock_num >= #{stock.stockNum}
    </update>



    <!-- 批量新增库存单位 -->
    <update id="increase"  parameterType="com.hengtiansoft.item.entity.dto.StockCalculateDTO">
            update item_sku_stock issk, item_sku isk
            set
            issk.stock_num = issk.stock_num + #{stock.stockNum},
            isk.stock = issk.stock_num + #{stock.stockNum}
            where
            isk.id = #{stock.skuId}
            and issk.sku_id = #{stock.skuId}
    </update>

    <!-- 批量更新库存单位 -->
    <update id="updateBatch" parameterType="java.util.List">

        <foreach close="" open="" collection="list" index="index" item="stock" separator=";">
            UPDATE item_sku_stock
            SET

            <trim suffixOverrides=",">
                <if test="stock.stockNum!=null">
                    stock_num = #{stock.stockNum},
                </if>
                <if test="stock.warningNum!=null">
                    warning_num = #{stock.warningNum},
                </if>
            </trim>

            WHERE sku_id = #{stock.skuId}
        </foreach>


    </update>


    <delete id="deleteStockByProductId">

        DELETE FROM item_sku_stock
        WHERE
        product_id = #{productId}

    </delete>
    <delete id="deleteStockBySkuId" parameterType="java.util.List">

        DELETE FROM item_sku_stock
        WHERE
        sku_id IN
        <foreach collection="list" open="(" close=")" separator="," index="index" item="skuId">
            #{skuId}
        </foreach>
    </delete>


    <select id="findStockBySpuId" resultType="com.hengtiansoft.item.entity.vo.ItemSkuStockVO">


        SELECT
        stock.id id,
        product.id product_id,
        product.product_code product_code,
        product.product_name product_name,
        sku.id sku_id,
        stock.stock_num stock_num,
        stock.warning_num warning_num,
        stock.warehouse_id
        warehouse_id

        FROM
        item_sku_stock stock,
        item_sku sku,
        item_product product

        WHERE product.id IN

        <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
            #{id}
        </foreach>

        AND stock.sku_id = sku.id
        AND sku.product_id = product.id;


    </select>


    <select id="findSaleStock" resultType="com.hengtiansoft.item.entity.vo.SkuSaleStockVO">
        SELECT
        product_id,
        sku_id,
        CASE
        WHEN (stock_num - warning_num) <![CDATA[ > ]]> 0 THEN stock_num - warning_num
        ELSE 0
        END stockNum
        FROM
        item_sku_stock
        WHERE
        sku_id IN
        <foreach collection="skuIds" separator="," close=")" open="(" index="index" item="skuId">
            #{skuId}
        </foreach>
    </select>

    <select id="checkStock" resultType="java.lang.Integer">
        SELECT COUNT(0)
        FROM item_sku_stock
        WHERE
        sku_id = #{skuId}
        AND stock_num <![CDATA[ >= ]]> #{stockNum} + warning_num
    </select>

    <select id="checkSafeAndStockByProduct" resultType="java.lang.Integer">
        SELECT COUNT(0)
        FROM item_sku_stock
        WHERE product_id = #{productId}
        AND stock_num <![CDATA[ > ]]> warning_num
    </select>

    <select id="findSaleStockByProductId" resultType="com.hengtiansoft.item.entity.vo.SkuSaleStockVO">
        SELECT
        product_id,
        sku_id,
        CASE
        WHEN (stock_num - warning_num) <![CDATA[ > ]]> 0 THEN stock_num - warning_num
        ELSE 0
        END stockNum
        FROM
        item_sku_stock
        WHERE
        product_id = #{productId}
    </select>
    <select id="findSaleStockBySkuId" resultMap="BaseResultMap">
        SELECT
            product_id,
            sku_id,
            CASE
            WHEN (stock_num - warning_num) <![CDATA[ > ]]> 0
            THEN stock_num - warning_num
            ELSE 0
            END stock_num
        FROM
            item_sku_stock
        WHERE
            sku_id = #{skuId}
    </select>

</mapper>