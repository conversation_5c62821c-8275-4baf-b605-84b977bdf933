<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemProductGroupMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemProductGroup">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="INTEGER" property="delflag" />
  </resultMap>
    <select id="selectByProductIds" resultType="com.hengtiansoft.item.entity.po.ItemProductGroup">
      select ipg.*,
             gt.name
      from item_product_group ipg
      left join group_template gt on ipg.group_id = gt.id
      where ipg.product_id in
      <foreach collection="productIds" item="productId" open="(" separator="," close=")">
        #{productId}
      </foreach>
    </select>

    <select id="groupProductCnt" resultType="com.hengtiansoft.item.entity.vo.GroupTemplateVO">
      SELECT
      ipg.group_id as id,
      count( 1 ) as productCnt
      FROM
      item_product_group ipg
      INNER JOIN item_product ip on ip.id = ipg.product_id
      WHERE
      ip.delflag = 0
      AND ipg.delflag = 0
      AND ipg.group_id IN (
      <foreach collection="ids" item="id" separator=",">
        #{id}
      </foreach>
      )
      GROUP BY
      ipg.group_id;
    </select>
</mapper>