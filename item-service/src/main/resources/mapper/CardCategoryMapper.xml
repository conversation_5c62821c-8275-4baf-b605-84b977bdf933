<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardCategoryMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_count" jdbcType="INTEGER" property="categoryCount" />
    <result column="effective_days" jdbcType="INTEGER" property="effectiveDays" />
    <result column="cap_price" jdbcType="DECIMAL" property="capPrice" />
    <result column="floor_price" jdbcType="DECIMAL" property="floorPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
  </resultMap>
</mapper>