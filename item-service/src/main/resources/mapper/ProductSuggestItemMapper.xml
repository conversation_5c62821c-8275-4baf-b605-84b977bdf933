<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductSuggestItemMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductSuggestItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_suggest_id" jdbcType="BIGINT" property="productSuggestId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>


    <select id="findPageByCondition" resultType="com.hengtiansoft.item.entity.vo.ProductSuggestItemResultVO">
        select psi.id,
               psi.product_suggest_id,
               psi.product_id,
               psi.type,
               psi.sort,
               psi.create_time,
               ip.update_time,
               ip.product_name,
               ip.product_type,
               ip.pic_url,
               ip.cate_id,
               ip.sale_status,
               ip.cycle_flag,
               ip.enable_show
        from product_suggest_item psi
        left join item_product ip on psi.product_id = ip.id
        where psi.delflag = 0 and ip.delflag = 0
        <if test="dto.type != null">
          and psi.type = #{dto.type}
        </if>
        <if test="dto.productSuggestId != null">
          and psi.product_suggest_id = #{dto.productSuggestId}
        </if>
        <if test="dto.productId != null">
          and psi.product_id = #{dto.productId}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
          and ip.product_name like concat('%', #{dto.productName}, '%')
        </if>
        <if test="dto.productType != null and dto.productType != ''">
          and ip.product_type = #{dto.productType}
        </if>
        <if test="dto.cateIds!=null and dto.cateIds.size()!=0">
            AND ip.cate_id IN
            <foreach collection="dto.cateIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.saleStatus!=null and dto.saleStatus.size()!=0">
            AND ip.sale_status IN
            <foreach collection="dto.saleStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.productTypeList!=null and dto.productTypeList.size()!=0">
            AND ip.product_type IN
            <foreach collection="dto.productTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.enableShow != null">
            and ip.enable_show = #{dto.enableShow}
        </if>
        <if test="dto.privilegeFlag != null">
            and ip.privilege_flag = #{dto.privilegeFlag}
        </if>
        order by psi.sort asc
    </select>

    <update id="deleteByTypeAndSuggestId">
        update product_suggest_item
        set delflag = 1, update_time = now()
        where delflag = 0
          and type = #{type}
          and product_suggest_id = #{suggestId}
    </update>

    <delete id="deleteByTypeAndSuggestIdAndProductIds">
        update product_suggest_item
        set delflag = 1, update_time = now(), operator = #{userName}
        where delflag = 0
          and type = #{type}
          and product_suggest_id = #{suggestId}
        and product_id in
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

    <select id="selectGreaterThanSortList" resultMap="BaseResultMap">
        select *
        from product_suggest_item
        where delflag = 0
          and type = #{type}
          and product_suggest_id = #{productSuggestId}
          and sort >= #{sort}
    </select>
    <select id="selectExistByTypeAndSuggestId" resultMap="BaseResultMap">
        select * from product_suggest_item
        where delflag = 0 and type = #{type} and product_suggest_id = #{suggestId} limit 1
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update product_suggest_item
            set sort = #{item.sort}, update_time = now()
            where id = #{item.id}
        </foreach>
    </update>


</mapper>