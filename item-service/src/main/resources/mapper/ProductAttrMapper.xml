<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductAttrMapper">
	<resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductAttr">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="product_id" jdbcType="INTEGER" property="productId" />
		<result column="attr_name" jdbcType="VARCHAR" property="attrName" />
		<result column="attr_value" jdbcType="VARCHAR" property="attrValue" />
		<result column="type" jdbcType="TINYINT" property="type" />
		<result column="sort" jdbcType="TINYINT" property="sort" />
		<result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
	</resultMap>

</mapper>