<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardShareMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardShare">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_id" jdbcType="BIGINT" property="cardId" />
    <result column="giver" jdbcType="BIGINT" property="giver" />
    <result column="receiver" jdbcType="BIGINT" property="receiver" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="share_number" jdbcType="VARCHAR" property="shareNumber" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="bless_word" jdbcType="VARCHAR" property="blessWord" />
    <result column="over_time" jdbcType="TIMESTAMP" property="overTime" />
  </resultMap>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into card_share(card_id,giver,share_number,bless_word,over_time)
    values
    <foreach collection="list" item="item" separator=",">
    (
      #{item.cardId, jdbcType=BIGINT}, #{item.giver, jdbcType=BIGINT},
      #{item.shareNumber, jdbcType=VARCHAR}, #{item.blessWord, jdbcType=VARCHAR},#{item.overTime,jdbcType=TIMESTAMP}
    )
    </foreach>
  </insert>

  <select id="selectNewestShare" resultType="com.hengtiansoft.item.entity.po.CardShare" parameterType="com.hengtiansoft.item.entity.dto.CardShareDTO">
    SELECT b.*
    FROM (SELECT card_id,MAX(id) AS id  FROM card_share GROUP BY card_id) a
    INNER JOIN card_share b ON a.card_id=b.card_id AND b.id=a.id
    <where>
      <if test="status !=null ">
        and  b.status =#{status}
      </if>
      <if test="cardIds !=null and cardIds.size > 0">
        and  b.card_id in
        <foreach collection="cardIds" item="cardId" open="(" separator="," close=")" index="index">
          #{cardId}
        </foreach>
      </if>
    </where>

  </select>
</mapper>