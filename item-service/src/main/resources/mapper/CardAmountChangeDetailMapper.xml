<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardAmountChangeDetailMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="changed_amount" jdbcType="DECIMAL" property="changedAmount"/>
        <result column="changed_time" jdbcType="TIMESTAMP" property="changedTime"/>
        <result column="reconciliation_amount" jdbcType="DECIMAL" property="reconciliationAmount"/>
        <result column="reconciliation_time" jdbcType="TIMESTAMP" property="reconciliationTime"/>
        <result column="milk_amount" jdbcType="INTEGER" property="milkAmount"/>
        <result column="amount_withdrawn" jdbcType="DECIMAL" property="amountWithdrawn"/>
        <result column="card_amount_single" jdbcType="DECIMAL" property="cardAmountSingle"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true">
        insert into card_amount_change_detail
        (card_number, changed_amount, changed_time, reconciliation_amount, reconciliation_time, milk_amount, amount_withdrawn, card_amount_single)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.cardNumber, jdbcType=VARCHAR}, #{item.changedAmount, jdbcType=DECIMAL},
            #{item.changedTime, jdbcType=TIMESTAMP}, #{item.reconciliationAmount, jdbcType=DECIMAL},
            #{item.reconciliationTime, jdbcType=TIMESTAMP}, #{item.milkAmount, jdbcType=INTEGER},
            #{item.amountWithdrawn, jdbcType=DECIMAL},#{item.cardAmountSingle, jdbcType=DECIMAL}
            )
        </foreach>
    </insert>
    <select id="selectByCardNumber" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where 1=1
        and card_number = #{cardNumber}
        and changed_amount is not null
        and date_format(create_time,'%Y-%m-%d') <![CDATA[<= ]]> #{date}
        order by create_time desc,id desc limit 1
    </select>

    <select id="selectByCardNumbers" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where 1=1
        and card_number in
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and changed_amount is not null
        and date_format(create_time,'%Y-%m-%d') <![CDATA[<= ]]> #{date}
        order by create_time asc,id asc
    </select>
    <select id="selectReconciliationByCardNumber"
            resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where 1=1
        and card_number = #{cardNumber}
        and reconciliation_amount is not null
        and date_format(create_time,'%Y-%m-%d') <![CDATA[<= ]]> #{date}
        order by create_time desc,id desc limit 1
    </select>

    <select id="selectReconciliationByCardNumbers"
            resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where 1=1
        and card_number in
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and reconciliation_amount is not null
        and date_format(create_time,'%Y-%m-%d') <![CDATA[<= ]]> #{date}
        order by create_time asc,id asc
    </select>


    <select id="selectByCardNumberReconciliation" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where 1=1
        and card_number = #{cardNumber}
        order by reconciliation_time desc,id desc limit 1
    </select>

    <select id="selectByCardNumbersReconciliation" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where  reconciliation_amount is not null and card_number in
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and reconciliation_time <![CDATA[<= ]]>#{endTime}
        order by create_time desc,id desc
    </select>

    <select id="selectWriteOffAmountByCardNumbers" resultType="com.hengtiansoft.item.entity.vo.CardWriteOffAmountVO">
        select card_number as cardNumber, sum(write_off_amount) as writeOffAmount from card_amount_change_detail
        where card_number in
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and write_off_time <![CDATA[<= ]]>#{endTime}
        group by card_number
    </select>

    <select id="selectByCardNumbersChanged" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where  changed_amount is not null and card_number in
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and changed_time <![CDATA[<= ]]>#{endTime}
        order by create_time desc,id desc
    </select>



    <select id="selectByCardNumberChanged" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where 1=1
        and card_number = #{cardNumber}
        order by changed_time desc,id desc limit 1
    </select>

    <select id="selectByCardNumberWriteOff" resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select * from card_amount_change_detail
        where  write_off_amount is not null and card_number = #{cardNumber}
    </select>

    <select id="selectSumWriteOffAmountByCardNumber" resultType="java.math.BigDecimal">
        select ifnull(sum(write_off_amount),0) from card_amount_change_detail
        where card_number = #{cardNumber} and write_off_amount is not null
    </select>


    <select id="selectSumWriteOffAmountByCardNumbers"
            resultType="com.hengtiansoft.item.entity.po.CardAmountChangeDetail">
        select card_number,ifnull(sum(write_off_amount),0) as writeOffAmount from card_amount_change_detail
        where  card_number in
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and write_off_amount is not null
        group by card_number
    </select>
</mapper>