<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.SkuMapper">
	<resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Sku">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="product_id" jdbcType="BIGINT" property="productId" />
		<result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
		<result column="sku_name" jdbcType="VARCHAR" property="skuName" />
		<result column="spec_value_list" jdbcType="VARCHAR" property="specValueList" />
		<result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
		<result column="description" jdbcType="VARCHAR" property="description" />
		<result column="list_price" jdbcType="DECIMAL" property="listPrice" />
		<result column="sale_price" jdbcType="DECIMAL" property="salePrice" />
		<result column="stock" jdbcType="BIGINT" property="stock" />
		<result column="delflag" jdbcType="INTEGER" property="delflag" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="operater" jdbcType="VARCHAR" property="operater" />
		<result column="sku_special_id" jdbcType="BIGINT" property="skuSpecialId" />
		<result column="sku_special_code" jdbcType="VARCHAR" property="skuSpecialCode" />
		<result column="sku_special_attr" jdbcType="VARCHAR" property="skuSpecialAttr" />
		<result column="reduce_cnt" jdbcType="INTEGER" property="reduceCnt" />
	</resultMap>
	<sql id="Base_Column_List">
		<!-- WARNING - @mbg.generated -->
		id, product_id, sku_code, sku_name, spec_value_list, pic_url, description, list_price,
		sale_price, stock, delflag, create_time, update_time, operater, sku_special_id, sku_special_code,
		sku_special_attr, reduce_cnt
	</sql>

	<select id="selectProductIdBySkuIds" resultType="java.lang.Long">
		SELECT product_id FROM item_sku WHERE id IN
		<foreach collection="skuIds" separator="," close=")" open="(" index="index" item="skuId">
			#{skuId}
		</foreach>
	</select>

	<select id="findByCondition" resultMap="BaseResultMap">
		SELECT s.* FROM item_sku s
		JOIN item_product p ON s.product_id = p.id
		<if test="productName!=null and productName!=''">
			<bind name="bindeName" value="'%' +productName +'%'" />
			AND p.product_name like #{bindeName}
		</if>
		<if test="cateIds!=null and cateIds.size()!=0">
			AND p.cate_id IN
			<foreach collection="cateIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="productTypeList != null and productTypeList.size()!= 0">
			AND p.product_type IN
			<foreach collection="productTypeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
        <if test="productType !=null and productType !=''">AND p.product_type = #{productType}</if>
		<if test="saleStatus!=null">AND p.sale_status=#{saleStatus}</if>
		<if test="delflag!=null">AND p.delflag=#{delflag}</if>
		<if test="skuAttrMap!=null and skuAttrMap.size()!=0">
			JOIN item_sku_attr sa ON s.id = sa.sku_id AND (sa.attr_name,sa.attr_value) IN
			<foreach collection="skuAttrMap.entrySet()" item="value" index="key" open="(" separator="," close=")">
				(#{key},#{value})
			</foreach>
		</if>
		<if test="temperature != null">AND p.temperature = #{temperature}</if>
		<if test="hasInventory !=null">
		      join item_sku_stock st on s.id = st.sku_id 
		      and st.stock_num 
		      <choose>
		          <when test="hasInventory">&gt;</when>
                  <otherwise>&lt;=</otherwise>	      
		      </choose>
		      st.warning_num
		</if>
		<where>
			s.delflag = 0
			<if test="skuCodes != null and skuCodes.size()!=0">
                AND s.sku_code IN
                <foreach collection="skuCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
			<if test="skuCode != null and skuCode !=''">
				AND s.sku_code =  #{skuCode}
			</if>
			<if test="skuIds!=null and skuIds.size()!=0">
				AND s.id IN
				<foreach collection="skuIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="productIds!=null and productIds.size()!=0">
				AND s.product_id IN
				<foreach collection="productIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="enableShow != null">
				AND p.enable_show = #{enableShow}
			</if>
			<if test="cardCategoryIds != null and cardCategoryIds.size() != 0">
				AND p.card_category_id IN
				<foreach collection="cardCategoryIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="youzanFlag != null">
				AND p.youzan_flag = #{youzanFlag}
			</if>
		</where>
		order by s.update_time desc
	</select>

	<select id="findAll" resultType="com.hengtiansoft.item.entity.vo.ProductDiscountVO">
		select ip.*, isu.id as skuId, isu.spec_value_list, isu.list_price, isu.sale_price, isu.sku_code
		from item_sku isu
			inner join item_product ip on ip.id=isu.product_id
		where ip.delflag=0 and isu.delflag=0
		<if test="dto.productIds != null and dto.productIds.size() > 0">
			AND ip.id IN
			<foreach collection="dto.productIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.productTypeList != null and dto.productTypeList.size()!= 0">
			AND ip.product_type IN
			<foreach collection="dto.productTypeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.productType != null and dto.productType !=''">
			AND ip.product_type = #{dto.productType}
		</if>
		<if test="dto.enableShow != null">
			AND ip.enable_show = #{dto.enableShow}
		</if>
		<if test="dto.productName!=null and dto.productName!=''">
			<bind name="bindeName" value="'%' +dto.productName +'%'" />
			AND ip.product_name like #{bindeName}
		</if>
		<if test="dto.cateIds!=null and dto.cateIds.size()!=0">
			AND ip.cate_id IN
			<foreach collection="dto.cateIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by ip.id desc, ip.update_time desc
    </select>

	<select id="selectProductSkuList" resultType="com.hengtiansoft.item.entity.vo.ProductSkuListVO">
		select ip.id,
		       ip.product_name,
		       ip.pic_url,
		       ip.description,
		       ip.store_id,
		       ip.brand_id,
		       ip.cate_id,
		       ip.product_code,
		       ip.sale_status,
		       ip.update_time,
			   ip.product_type,
			   ip.privilege_flag,
			   ip.sort,
			   ip.temperature,
			   ip.cycle_flag,
			   ip.enable_show,
		       isu.id as skuId,
		       isu.sku_name,
		       isu.spec_value_list,
		       isu.list_price,
		       isu.sale_price,
		       isu.sku_code,
		       isu.stock,
		       isu.pic_url as skuPicUrl
		from item_sku isu
		inner join item_product ip on ip.id = isu.product_id
		where ip.delflag = 0 and isu.delflag = 0
		<if test="dto.skuIds != null and dto.skuIds.size()!= 0">
			AND isu.id IN
			<foreach collection="dto.skuIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.productId != null">
			and ip.id = #{dto.productId}
		</if>
		<if test="dto.productTypeList != null and dto.productTypeList.size()!= 0">
			AND ip.product_type IN
			<foreach collection="dto.productTypeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.productIds != null and dto.productIds.size()!= 0">
			AND ip.id IN
			<foreach collection="dto.productIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.productType != null and dto.productType !=''">
			AND ip.product_type = #{dto.productType}
		</if>
		<if test="dto.enableShow != null">
			AND ip.enable_show = #{dto.enableShow}
		</if>
		<if test="dto.saleStatus!=null and dto.saleStatus.size()!=0">
			AND ip.sale_status IN
			<foreach collection="dto.saleStatus" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.cycleFlag != null">
			AND ip.cycle_flag = #{dto.cycleFlag}
		</if>
		<if test="dto.productName != null and dto.productName != ''">
			<bind name="bindeName" value="'%' +dto.productName +'%'" />
			AND ip.product_name like #{bindeName}
		</if>
		<if test="dto.cateIds != null and dto.cateIds.size() > 0">
			AND ip.cate_id IN
			<foreach collection="dto.cateIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.greaterThanStock != null">
			AND isu.stock > #{dto.greaterThanStock}
		</if>
		order by ip.id desc, ip.update_time desc
	</select>
    <select id="findLimitPurchaseAndStock"
            resultType="com.hengtiansoft.item.entity.dto.SkuStockAndPurchaseLimitDTO">
		select isu.product_id,
		    isu.id as skuId,
		       isu.stock,
		       isa.attr_value  as purchase_limit
		from item_sku isu
		left join item_sku_attr isa on isa.sku_id = isu.id
		where isu.delflag = 0
		and isa.attr_name = 'purchase'
		<if test="productIds != null and productIds.size()!=0">
			AND isu.product_id IN
			<foreach collection="productIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="getNormalSelected" resultType="com.hengtiansoft.item.entity.dto.SkuSelectedDTO">
		select
		    sku.product_id,
		    sku.id as skuId,
		    sku.sku_code,
		    sku.sku_name,
		    product.product_name
		from item_sku sku
		left join item_product product on product.id = sku.product_id
		where product.product_type = 'n'
		<if test="dto.codeOrName != null and dto.codeOrName != ''">
			and (sku.sku_code like concat('%',#{dto.codeOrName},'%')
			or product.product_name like concat('%',#{dto.codeOrName},'%'))
		</if>
	</select>
</mapper>