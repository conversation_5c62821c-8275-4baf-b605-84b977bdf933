<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemProductAddonsMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemProductAddons">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="card_category_id" jdbcType="BIGINT" property="cardCategoryId" />
    <result column="card_category_name" jdbcType="VARCHAR" property="cardCategoryName" />
  </resultMap>
</mapper>