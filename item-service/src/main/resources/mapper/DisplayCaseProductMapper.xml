<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.DisplayCaseProductMapper">
    <select id="findProIdsByDisplayCaseId" resultType="com.hengtiansoft.item.entity.po.DisplayCaseProduct">
        select * from item_display_case_product where display_case_id = #{displayCaseId}
    </select>
    <select id="findProIdsBySecondIds" resultType="java.lang.Long">
        select product_id from item_display_case_product where display_case_id in #{displayCaseId}
    </select>
    <select id="searchProducts" resultType="java.lang.Long">
        SELECT ip.id
        FROM item_product ip
        LEFT JOIN item_display_case_product idcp ON ip.id =idcp.product_id
        WHERE idcp.display_case_id =#{displayCaseId} AND ip.sale_status=1 AND ip.delflag=0
        ORDER BY idcp.sort
    </select>
    <select id="findProByDisplayCaseId" resultType="com.hengtiansoft.item.entity.po.DisplayCaseProduct">
        SELECT idcp.*
        FROM item_display_case_product idcp
        LEFT JOIN item_product ip ON idcp.product_id =ip.id
        WHERE idcp.display_case_id IN
        <foreach collection="displayIds" item="displayId" open="(" close=")" separator=",">
            #{displayId}
        </foreach>
        AND ip.sale_status=1 AND ip.delflag =0
    </select>
    <select id="findProductsById" resultType="java.lang.Long">
        SELECT ip.id
        FROM item_product ip
        LEFT JOIN item_display_case_product idcp ON ip.id =idcp.product_id
        WHERE idcp.display_case_id =#{id} AND ip.sale_status=1 AND ip.delflag=0
        ORDER BY idcp.sort
    </select>
</mapper>