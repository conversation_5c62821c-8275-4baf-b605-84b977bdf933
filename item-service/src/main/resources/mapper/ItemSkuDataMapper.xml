<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemSkuDataMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemSkuData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_desc" jdbcType="VARCHAR" property="skuDesc" />
    <result column="sku_type" jdbcType="VARCHAR" property="skuType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>
</mapper>