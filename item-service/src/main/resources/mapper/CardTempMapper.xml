<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardTempMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardTemp">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
        <result column="tar_card_number" jdbcType="VARCHAR" property="tarCardNumber" />
    </resultMap>
</mapper>