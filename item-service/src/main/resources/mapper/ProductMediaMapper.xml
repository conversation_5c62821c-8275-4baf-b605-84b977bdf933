<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductMediaMapper">
	<resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductMedia">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="product_id" jdbcType="BIGINT" property="productId" />
		<result column="sort" jdbcType="TINYINT" property="sort" />
		<result column="media_url" jdbcType="VARCHAR" property="mediaUrl" />
		<result column="media_key" jdbcType="VARCHAR" property="mediaKey" />
		<result column="type" jdbcType="VARCHAR" property="type" />
	</resultMap>

</mapper>