<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductFrontClassifyMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductFrontClassify">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="has_banner" jdbcType="TINYINT" property="hasBanner" />
    <result column="banner_url" jdbcType="VARCHAR" property="bannerUrl" />
    <result column="link_type" jdbcType="TINYINT" property="linkType" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="mini_qrcode" jdbcType="VARCHAR" property="miniQrcode" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="people_limit" jdbcType="TINYINT" property="peopleLimit" />
    <result column="grade" jdbcType="VARCHAR" property="grade" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="relate_target_type" jdbcType="TINYINT" property="relateTargetType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>

  <select id="findByCondition" resultMap="BaseResultMap">
    select *
    from product_front_classify
    where delflag = 0
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="name != null and name != ''">
      and name like concat('%', #{name}, '%')
    </if>
    <if test="status != null">
      <if test="status == 1">
        and (start_time > now() and end_time > now())
      </if>
      <if test="status == 2">
        and (start_time is null or (start_time <![CDATA[ <= ]]> now() and end_time > now()))
      </if>
      <if test="status == 3">
        and end_time <![CDATA[ < ]]> now()
      </if>
    </if>
    <if test="peopleLimitList != null and peopleLimitList.size > 0">
    and people_limit in
      <foreach collection="peopleLimitList" item="peopleLimit" separator="," open="(" close=")">
         #{peopleLimit}
      </foreach>
    </if>
    order by sort asc
  </select>

  <update id="updateGreaterSort">
    update product_front_classify
    set sort = sort+1, update_time = update_time
    where sort >= #{sort} and delflag = 0
  </update>
</mapper>