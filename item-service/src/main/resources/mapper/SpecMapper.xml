<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.SpecMapper">
	<resultMap id="FullBaseResultMap" type="com.hengtiansoft.item.entity.dto.SpecDTO">
		<id column="s_id" property="id" jdbcType="BIGINT" />
		<result column="s_spec_group_id" property="specGroupId" jdbcType="BIGINT" />
		<result column="s_spec_name" property="specName" jdbcType="VARCHAR" />
		<result column="s_spec_type" property="specType" jdbcType="VARCHAR" />
		<result column="s_display_type" property="displayType" jdbcType="VARCHAR" />
		<result column="s_sort" property="sort" jdbcType="TINYINT" />
		<result column="s_searching" property="searching" jdbcType="TINYINT" />
		<collection property="specValues" ofType="com.hengtiansoft.item.entity.dto.SpecValueDTO">
			<id column="sv_id" property="id" jdbcType="BIGINT" />
			<result column="sv_spec_id" property="specId" jdbcType="BIGINT" />
			<result column="sv_value" property="value" jdbcType="VARCHAR" />
			<result column="sv_pic_url" property="picUrl" jdbcType="VARCHAR" />
			<result column="sv_sort" property="sort" jdbcType="TINYINT" />
		</collection>
	</resultMap>

	<sql id="dtoField">
		s.id AS s_id,
		s.spec_group_id AS s_spec_group_id,
		s.spec_name AS s_spec_name,
		s.spec_type AS s_spec_type,
		s.display_type AS s_display_type,
		s.sort AS s_sort,
		s.searching AS s_searching
	</sql>

	<select id="selectSepcDtoByProductId" resultMap="FullBaseResultMap">
		SELECT
		<include refid="com.hengtiansoft.item.mapper.SpecMapper.dtoField" />
		,
		<include refid="com.hengtiansoft.item.mapper.SpecValueMapper.dtoField" />
		FROM item_spec s, item_spec_value sv
		WHERE s.id = sv.spec_id AND s.delflag = 0 AND sv.delflag = 0
		AND s.id IN (SELECT spec_id FROM item_product_spec WHERE product_id =
		#{productId})
		ORDER BY s.id , sv.id
	</select>
</mapper>