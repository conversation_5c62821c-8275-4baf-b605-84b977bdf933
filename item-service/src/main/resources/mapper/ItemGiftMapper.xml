<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemGiftMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemGift">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="delflag" jdbcType="INTEGER" property="delflag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
  </resultMap>

    <select id="findByCondition" resultType="com.hengtiansoft.item.entity.dto.ItemGiftDTO">
    SELECT ig.id, ig.sku_id as skuId, ip.id as productId, ip.product_name as productName, isk.sku_code as skuCode,ip.enable_show,ip.cate_id as cateId,ip.sale_status as saleStatus,
           isk.spec_value_list as specValueList, ig.create_time as createTime, ip.pic_url as picUrl, isk.pic_url as skuPicUrl, iss.stock_num as stockNum,
           isk.sale_price as salePrice,isk.list_price as listPrice, ip.product_type as productType
    FROM item_gift ig
    INNER JOIN item_sku isk ON isk.id = ig.sku_id
    INNER JOIN item_product ip ON ip.id = isk.product_id
    LEFT JOIN item_sku_stock iss on iss.sku_id = isk.id
    where ip.product_type in('d','y')
    and   ig.delflag = 0 and isk.delflag = 0 and ip.delflag = 0
      <if test="productName!=null and productName!=''">
        <bind name="bindeName" value="'%' +productName +'%'" />
        AND ip.product_name like #{bindeName}
      </if>
      <if test="skuCode != null and skuCode !=''">
        AND isk.sku_code =  #{skuCode}
      </if>
      <if test="enableShow != null">
        AND ip.enable_show =  #{enableShow}
      </if>
      <if test="cateIds!=null and cateIds.size()!=0">
        AND ip.cate_id IN
        <foreach collection="cateIds" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="productTypeList!=null and productTypeList.size()!=0">
        AND ip.product_type IN
        <foreach collection="productTypeList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="productId != null">
        AND ip.id =  #{productId}
      </if>
      <if test="saleStatus!=null and saleStatus.size()!=0">
        AND ip.sale_status IN
        <foreach collection="saleStatus" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    order by ig.create_time desc
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into item_gift
    (sku_id,delflag,operater)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.skuId} ,#{item.delflag},#{item.operater}
      )
    </foreach>
  </insert>

</mapper>