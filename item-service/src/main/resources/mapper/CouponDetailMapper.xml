<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CouponDetailMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CouponDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="coupon_id" jdbcType="INTEGER" property="couponId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="card_password" jdbcType="VARCHAR" property="cardPassword"/>
        <result column="threshold_money" jdbcType="DECIMAL" property="thresholdMoney"/>
        <result column="activation_time" jdbcType="INTEGER" property="activationTime"/>
        <result column="dead_time" jdbcType="INTEGER" property="deadTime"/>
        <result column="use_time" jdbcType="INTEGER" property="useTime"/>
        <result column="card_type" jdbcType="TINYINT" property="cardType"/>
        <result column="card_value" jdbcType="VARCHAR" property="cardValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_available" jdbcType="TINYINT" property="isAvailable"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="created_at" jdbcType="INTEGER" property="createdAt"/>
        <result column="updated_at" jdbcType="INTEGER" property="updatedAt"/>
        <result column="deleted_at" jdbcType="INTEGER" property="deletedAt"/>
    </resultMap>
</mapper>