<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Card">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="card_password" jdbcType="VARCHAR" property="cardPassword"/>
        <result column="category_id" jdbcType="BIGINT" property="categoryId"/>
        <result column="category_name" jdbcType="VARCHAR" property="categoryName"/>
        <result column="card_type" jdbcType="INTEGER" property="cardType"/>
        <result column="card_count" jdbcType="INTEGER" property="cardCount"/>
        <result column="card_amount" jdbcType="DECIMAL" property="cardAmount"/>
        <result column="remaining_count" jdbcType="INTEGER" property="remainingCount"/>
        <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount"/>
        <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount"/>
        <result column="usage_status" jdbcType="INTEGER" property="usageStatus"/>
        <result column="card_status" jdbcType="INTEGER" property="cardStatus"/>
        <result column="lock_status" jdbcType="INTEGER" property="lockStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime"/>
        <result column="activate_time" jdbcType="TIMESTAMP" property="activateTime"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo"/>
        <result column="shop_name" jdbcType="VARCHAR" property="shopName"/>
        <result column="bind_channel" jdbcType="VARCHAR" property="bindChannel"/>
        <result column="tm_token" jdbcType="VARCHAR" property="tmToken"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="invoice_type" jdbcType="INTEGER" property="invoiceType"/>
        <result column="invoice_time" jdbcType="TIMESTAMP" property="invoiceTime"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into card
        (card_number, card_password, category_id, category_name, card_type, card_count, remaining_count)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.cardNumber, jdbcType=VARCHAR}, #{item.cardPassword, jdbcType=VARCHAR},
            #{item.categoryId, jdbcType=BIGINT}, #{item.categoryName, jdbcType=VARCHAR},
            #{item.cardType, jdbcType=INTEGER}, #{item.cardCount, jdbcType=INTEGER},
            #{item.remainingCount, jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <update id="batchUpdateStatus" parameterType="java.util.List">
        <foreach close="" collection="list" index="index" item="item" open="" separator=";">
            UPDATE card
            <set>
                card_status = #{item.cardStatus}, update_time = #{item.updateTime}
                <if test="item.usageStatus != null">
                    , usage_status = #{item.usageStatus}
                </if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>
    <select id="countByStatus" resultType="com.hengtiansoft.item.entity.vo.CardCountVO">
        select usage_status as usageStatus, count(1) as count from card
        where delete_flag = 0
        <if test="dto.cardNumber != null and dto.cardNumber != ''">
            and card_number = #{dto.cardNumber}
        </if>
        <if test="dto.userPhone != null and dto.userPhone != ''">
            and user_phone = #{dto.userPhone}
        </if>
        <if test="dto.usageStatus != null and dto.usageStatus != ''">
            and usage_status = #{dto.usageStatus}
        </if>
        <if test="dto.cardStatus != null and dto.cardStatus != ''">
            and card_status = #{dto.cardStatus}
        </if>
        <if test="dto.receiveStartTime != null">
            and receive_time &gt;= #{dto.receiveStartTime}
        </if>
        <if test="dto.receiveEndTime != null">
            and receive_time &lt;= #{dto.receiveEndTime}
        </if>
        <if test="dto.userFlag == true">
            and user_id is not null and card_status != 4
        </if>
        <if test="dto.categoryIdList!=null and dto.categoryIdList.size()!=0">
            and category_id IN
            <foreach collection="dto.categoryIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.usageStatusList!=null and dto.usageStatusList.size()!=0">
            and usage_status IN
            <foreach collection="dto.usageStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.cardStatusList!=null and dto.cardStatusList.size()!=0">
            and card_status IN
            <foreach collection="dto.cardStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by usage_status
    </select>
    <select id="countCategory" resultType="com.hengtiansoft.item.entity.vo.CardCountCategoryVO">
        select category_name as categoryName, count(1) as count from card
        where delete_flag = 0 and usage_status != 0
        <if test="dto.totalFlag == false">
            and card_status != 4 and (activate_time between #{dto.activateStartTime} and #{dto.activateEndTime})
            and usage_status = 4
        </if>
        group by category_name
    </select>
    <select id="countRemainingCount" resultType="com.hengtiansoft.item.entity.vo.CardRemainingCountVO">
        select user_id as userId, max(finish_time) as finishTime, sum(remaining_count) as remainingCount from card
        where delete_flag = 0 and user_id is not null and user_id != 0
        group by user_id
        having remainingCount = 0 and finishTime is not null
    </select>

    <select id="countRemainingCountSum" resultType="java.lang.Integer">
        select count(1) from (select user_id as userId, max(finish_time) as finishTime, sum(remaining_count) as remainingCount from card
        where delete_flag = 0 and user_id is not null and user_id != 0
        group by user_id
        having remainingCount = 0 and finishTime is not null) t
    </select>

    <select id="countRemainingByDate" resultType="com.hengtiansoft.item.entity.vo.CardRemainingCountVO">
        select user_id as userId, max(finish_time) as finishTime, sum(remaining_count) as remainingCount from card
        where delete_flag = 0 and user_id is not null and user_id != 0
        group by user_id
        having remainingCount = 0 and finishTime is not null and date_format(finishTime,'%Y-%m-%d') <![CDATA[= ]]> #{date}
    </select>

    <select id="selectByCardNumbersInBackup" resultType="com.hengtiansoft.item.entity.po.Card">
        select * from card_${date}
        where card_number in
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByCardNumbersInZipper" resultType="com.hengtiansoft.item.entity.po.Card">
        select * from card_his c
        where c.beg_time &lt;= #{date} AND c.end_time &gt; #{date}
        and c.card_number in
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="createTable">
        DROP TABLE IF EXISTS ${newTableName};
        CREATE TABLE ${newTableName} LIKE ${tableName};
    </update>

    <select id="countNumber" resultType="long">
        select count(1) from ${tableName}
    </select>

    <update id="backupsData">
        INSERT INTO ${newTableName} SELECT * FROM ${tableName} WHERE id >= ${id} LIMIT ${rowSize};
    </update>

    <select id="getTableId" resultType="long">
        SELECT id FROM ${tableName} ORDER BY id LIMIT ${beginRow}, 1;
    </select>

    <select id="showAllTables" resultType="java.lang.String">
        SHOW TABLES LIKE 'card_2%'
    </select>

    <update id="updateAdjustAmountInBackup">
        UPDATE card_${date} SET adjust_amount=#{dto.adjustAmount} WHERE card_number=#{dto.cardNumber}
    </update>

    <select id="findCardRemainingDifferent" resultType="com.hengtiansoft.item.entity.dto.CardFixDTO">
        select c.card_number, c.card_count, c.remaining_count, c.card_count - c.remaining_count  as cardUsedCount,
               temp.sumMilkAmount as planCardUsedCount ,c.card_amount, c.remaining_amount, c.card_amount - c.remaining_amount as cardUsedAmount ,
               temp.sumAmount as planCardUsedAmount, c.update_time, c.card_status from card c
        inner join (select plan.card_number, sum(milk_amount) as sumMilkAmount, sum(amount) as sumAmount
        from milk_dispatch_plan plan where plan.push_flag = 2 and plan.delflag = 0 group by plan.card_number) temp on c.card_number = temp.card_number
        where (c.card_count-c.remaining_count) != sumMilkAmount and c.card_status in(1,2)
        <if test="dto.cardNumberList!=null and dto.cardNumberList.size()!=0">
            and c.card_number  IN
            <foreach collection="dto.cardNumberList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.updateTime!=null ">
            and c.update_time &gt;= #{dto.updateTime}
        </if>
    </select>
    <select id="findCardWithParentNo" resultType="com.hengtiansoft.common.entity.vo.CardParentNoVO">
        select c.card_number as cardCode, c.remaining_count,
            CASE
            WHEN oi.channel = 'lpk' AND oi.create_time <![CDATA[ > ]]> '2023-07-28' THEN oi.order_parent_no
            WHEN oi.channel = 'lpk' AND oi.create_time <![CDATA[ <= ]]> '2023-07-28' THEN oi.order_no
            ELSE oi.src_no
            END AS tid
        from card c
        inner join order_sku_card osc on osc.card_number=c.card_number
        inner join order_sku os on os.id=osc.order_sku_id
        inner join order_info oi on oi.order_no=os.order_no
        where 1=1
        <if test="list !=null and list.size()!=0">
            and c.card_number  IN
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="countRemainingCountByPhone" resultType="java.lang.Integer">
        select sum(c.remaining_count) as sumRemainingCount from card c
        where
            c.card_status !=4
        and c.delete_flag = 0
        and c.user_phone = #{phone}
        group by c.user_phone
    </select>
    <select id="findPlanRemindUserPhone" resultType="java.lang.String">
        SELECT DISTINCT
            cu.user_phone
        FROM
            (
                SELECT
                    user_Id,
                    sum( remaining_count ) AS sumRemainingCount
                FROM
                    card
                WHERE
                    card_status not in(3,4)
		        AND user_id > 0
		        AND user_phone IS NOT NULL
		        AND user_phone != ''
		        AND delete_flag = 0
                GROUP BY
                    user_Id
                HAVING
                    sumRemainingCount > 0
            ) AS a
                INNER JOIN card cu ON cu.user_id = a.user_id
        WHERE
              cu.user_phone IS NOT NULL
          AND cu.user_phone != ''
          AND NOT EXISTS ( SELECT 1 FROM milk_dispatch_plan t1 WHERE t1.`plan_status` = 1 AND t1.`delflag` = 0 AND t1.user_Id = a.user_Id )
          AND NOT EXISTS ( SELECT 1 FROM milk_dispatch_plan t2 WHERE t2.create_time <![CDATA[ >= ]]>#{date} AND t2.user_id = a.user_id )
          AND     EXISTS ( SELECT 1 FROM milk_dispatch_plan t3 WHERE t3.push_flag = 2 AND t3.dispatch_date >= '2023-11-01' AND t3.delflag = 0 AND t3.user_id = a.user_id)
          AND NOT EXISTS(  SELECT 1 FROM sms_job t3 WHERE t3.send_flag = 1 AND t3.type = 3 AND t3.delflag = 0 AND t3.send_time <![CDATA[ >= ]]>#{date} AND t3.phone = cu.user_phone)
    </select>
    <select id="countSumRemainCountByPhone" resultType="java.lang.Integer">
         select sum(remaining_count)
         from card
         where delete_flag = 0
         and user_phone = #{userPhone}
    </select>
    <select id="countSumCardCountByPhone" resultType="java.lang.Integer">
        select sum(card_count)
        from card
        where delete_flag = 0
          and user_phone = #{userPhone}
    </select>
    <select id="findFirstByUserIdAndCategoryIds" resultType="com.hengtiansoft.item.entity.po.Card">
    select * from card
    where user_id = #{userId}
      and delete_flag = 0
      and usage_status not in(4, 5)
      and card_status not in(3, 4)
      and card_type in(0, 1)
      and remaining_count > 0
      and category_id in
        <foreach collection="categoryIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    limit 1
    </select>
    <select id="selectMaxReceiveTimeByUserIds" resultType="com.hengtiansoft.item.entity.po.Card">
        select user_id as userId, max(receive_time) as receiveTime
        from card
        where card_type in (1,0) and user_id in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="startTime != null">
            AND receive_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND receive_time <![CDATA[<= ]]> #{endTime}
        </if>
        group by user_id
    </select>
    <select id="selectByPhones" resultType="java.lang.String">
        select user_phone from card where user_phone in
        <foreach collection="phones" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by user_phone
    </select>
</mapper>