<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.DisplayCaseMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.DisplayCase">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="display_case_code" jdbcType="VARCHAR" property="displayCaseCode"/>
        <result column="display_case_name" jdbcType="VARCHAR" property="displayCaseName"/>
        <result column="display_case_path" jdbcType="VARCHAR" property="displayCasePath"/>
        <result column="display_case_level" jdbcType="INTEGER" property="displayCaseLevel"/>
        <result column="sort" jdbcType="TINYINT" property="sort"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="list_pic_url" jdbcType="VARCHAR" property="listPicUrl"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="is_homepage" jdbcType="INTEGER" property="isHomepage"/>
        <result column="index_style" jdbcType="INTEGER" property="indexStyle"/>
        <result column="delflag" jdbcType="INTEGER" property="delflag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operater" jdbcType="VARCHAR" property="operater"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, store_id, parent_id, display_case_code, display_case_name, display_case_path,
        display_case_level, sort, pic_url, list_pic_url, description, type, is_homepage,
        index_style, delflag, create_time, update_time, operater
    </sql>

    <select id="findSecondIdsById" resultType="java.lang.Long">
        select id from item_display_case where parent_id = #{displayCaseId} and delflag =0
    </select>
    <select id="displayList" resultType="com.hengtiansoft.item.entity.po.DisplayCase">
        SELECT * FROM item_display_case ic WHERE id in (SELECT idc.parent_id AS id FROM item_display_case idc WHERE id
        IN
        (SELECT idcp.display_case_id FROM item_product ip
        LEFT JOIN item_display_case_product idcp on idcp.product_id =ip.id WHERE ip.sale_status=1 and ip.delflag=0) AND
        idc.`type` IS NULL AND idc.display_case_level =2) AND ic.is_homepage=1 ORDER BY ic.sort ASC ,ic.update_time DESC
    </select>
    <select id="secondDisplays" resultType="com.hengtiansoft.item.entity.po.DisplayCase">
        SELECT * FROM item_display_case ic
        RIGHT JOIN (SELECT distinct display_case_id FROM item_display_case_product) ip ON ic.id = ip.display_case_id
        WHERE ic.parent_id =#{id} AND ic.delflag =0 ORDER BY ic.sort
    </select>
    <select id="homepageInfo" resultType="com.hengtiansoft.item.entity.vo.DisplayVO">
        SELECT c.parent_id as id, COUNT(1) as number FROM item_display_case c, item_display_case_product cp WHERE c.id =
        cp.display_case_id AND c.display_case_level=2 GROUP BY c.parent_id
    </select>
    <select id="displayListInfo" resultType="com.hengtiansoft.item.entity.po.DisplayCase">
        SELECT * FROM item_display_case ic WHERE id in (SELECT idc.parent_id AS id FROM item_display_case idc WHERE id
        IN
        (SELECT idcp.display_case_id FROM item_product ip
        LEFT JOIN item_display_case_product idcp on idcp.product_id =ip.id WHERE ip.sale_status=1 and ip.delflag=0) AND
        idc.`type` IS NULL AND idc.display_case_level =2) AND ic.is_homepage=1 ORDER BY ic.sort ASC
    </select>
</mapper>