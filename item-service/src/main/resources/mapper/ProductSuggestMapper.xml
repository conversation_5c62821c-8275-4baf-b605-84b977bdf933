<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductSuggestMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ProductSuggest">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="position" jdbcType="INTEGER" property="position" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="people_limit" jdbcType="INTEGER" property="peopleLimit" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>
  <update id="updateGreaterSort">
    update product_suggest
    set sort = sort+1, update_time = update_time
    where sort >= #{sort} and position = #{position}
  </update>
  <select id = "findInProgress">
    select * from product_suggest
    where status = 2 and delflag = 0
    order by sort asc
    limit 4
  </select>
  <select id="findHasItemSuggests" resultType="com.hengtiansoft.item.entity.po.ProductSuggest">
    SELECT DISTINCT
      ps.*
    FROM
      product_suggest ps
        INNER JOIN product_suggest_item psi ON psi.product_suggest_id = ps.id
    WHERE
      psi.delflag = 0 and ps.delflag = 0
      <if test="position != null">
        and ps.position = #{position}
      </if>
      <if test="status != null">
        and ps.status = #{status}
      </if>
      and EXISTS (
        SELECT
          1
        FROM
          item_product ip
        WHERE
          ip.id = psi.product_id
          AND ip.delflag = 0
          AND ip.privilege_flag = 0
          AND ip.product_type IN ( 'c', 'd', 's' )
          AND ip.enable_show = 1
          AND ip.sale_status = 1
      )
    ORDER BY sort ASC
  </select>

</mapper>