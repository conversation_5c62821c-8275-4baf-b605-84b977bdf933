<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.DiscountActivityMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.DiscountActivity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="people_limit" jdbcType="INTEGER" property="peopleLimit" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="notice" jdbcType="INTEGER" property="notice" />
    <result column="notice_hour" jdbcType="INTEGER" property="noticeHour" />
    <result column="sharing_discount" jdbcType="INTEGER" property="sharingDiscount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="countdown" jdbcType="INTEGER" property="countdown" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="deal_count" jdbcType="INTEGER" property="dealCount" />
    <result column="new_deal_count" jdbcType="INTEGER" property="newDealCount" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="INTEGER" property="delflag" />
  </resultMap>

  <select id="findByCondition" resultMap="BaseResultMap">
    select da.*
    from discount_activity da
    where da.delflag = 0
    <if test="(skuCode != null and skuCode != '') or productId != null">
      and da.id in (
      select distinct(dar.discount_activity_id)
      from discount_activity_range dar
      left join item_sku sku on dar.sku_id = sku.id
      where dar.delflag = 0
      and sku.delflag = 0
      <if test="productId != null">
        and dar.product_id = #{productId}
      </if>
      <if test="skuCode != null and skuCode != ''">
        and sku.sku_code = #{skuCode}
      </if>
      )
    </if>
    <if test="name != null and name != ''">
      and da.name like concat('%', #{name}, '%')
    </if>
    <if test="status != null">
      and da.status = #{status}
    </if>
    <if test="statusList != null and statusList.size > 0">
      and da.status in
      <foreach collection="statusList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="endTime != null">
      and da.end_time >= #{endTime}
    </if>
    order by da.create_time desc
  </select>

  <select id="findBySkuIds" resultMap="BaseResultMap">
    select da.*, dar.sku_id  from discount_activity da
        inner join discount_activity_range dar on dar.discount_activity_id=da.id
    where da.delflag =0 and dar.delflag=0 and da.status in (1,2) and dar.sku_id in
    <foreach item="item" index="index" collection="skuIds" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="findStartNoticeDiscount" resultMap="BaseResultMap">
    select * from discount_activity
    where delflag = 0
      and status = 1
      and (notice = 2 or (notice = 3 and DATE_SUB(start_time, INTERVAL notice_hour HOUR) <![CDATA[ <= ]]> now()))
      and start_time <![CDATA[<= ]]> #{startTimeLess}
  </select>

  <select id="findNewNoticeDiscount" resultMap="BaseResultMap">
    select * from discount_activity
    where delflag = 0
      and (status = 1 and (notice = 2 or (notice = 3 and DATE_SUB(start_time, INTERVAL notice_hour HOUR) <![CDATA[ <= ]]> now())) or status = 2)
  </select>
  <select id="getDiscountActivityProductList"
          resultType="com.hengtiansoft.item.entity.vo.DiscountActivityProductVO">
    select da.id,
           da.label,
           dar.product_id,
           dar.sku_id,
           ip.product_name,
           ip.pic_url,
           dar.limit_num,
           dar.discount_price,
           da.start_time,
           da.end_time,
           da.`status`,
           SUM(dar.stock)       as stock,
           SUM(dar.used)        as used
    from discount_activity da
           LEFT JOIN discount_activity_range dar on da.id = dar.discount_activity_id
           LEFT JOIN item_product ip on dar.product_id = ip.id
    where da.`status` = #{status} and da.delflag = 0 and dar.delflag = 0
      and ip.enable_show = 1 and ip.sale_status = 1 and ip.delflag = 0 and ip.product_type in ('c','d', 's')
    <if test="discountActivityIds != null and discountActivityIds.size() > 0">
      and da.id in
      <foreach collection="discountActivityIds" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    GROUP BY dar.product_id
    ORDER BY (SUM(dar.stock) - SUM(dar.used) > 0) desc,da.start_time asc
  </select>
  <select id="manualFindByCondition" resultType="com.hengtiansoft.item.entity.po.DiscountActivity">
    select *
    from discount_activity
    where delflag = 0 and page_show = 1 and status = #{dto.status}
    <if test="dto.status == 1">
      and (notice = 2 or (notice = 3 and DATE_SUB(start_time, INTERVAL notice_hour HOUR) <![CDATA[ <= ]]> now()))
    </if>
  </select>
</mapper>