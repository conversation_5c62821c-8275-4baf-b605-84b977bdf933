<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CouponGiftRangeMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CouponGiftRange">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into coupon_gift_range
    (
     rule_id,
     sku_id,
     count,
     sort
     )
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.ruleId},
       #{item.skuId},
      #{item.count},
      #{item.sort}
      )
    </foreach>
  </insert>
</mapper>