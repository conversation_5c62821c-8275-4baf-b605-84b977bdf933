<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemDeliveryAddressMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemDeliveryAddress">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="contains_area" jdbcType="LONGVARCHAR" property="containsArea" />
  </resultMap>
</mapper>