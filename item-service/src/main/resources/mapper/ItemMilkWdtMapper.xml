<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ItemMilkWdtMapper">
    <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.ItemMilkWdt">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo"/>
        <result column="shopId" jdbcType="BIGINT" property="shopid"/>
        <result column="shop_name" jdbcType="VARCHAR" property="shopName"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="spec_name" jdbcType="VARCHAR" property="specName"/>
        <result column="goods_id" jdbcType="VARCHAR" property="goodsId"/>
        <result column="spec_id" jdbcType="VARCHAR" property="specId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
</mapper>