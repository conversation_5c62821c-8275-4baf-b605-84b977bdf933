<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.ProductMapper">
	<resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.Product">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="store_id" jdbcType="BIGINT" property="storeId" />
		<result column="brand_id" jdbcType="BIGINT" property="brandId" />
		<result column="cate_id" jdbcType="BIGINT" property="cateId" />
		<result column="product_code" jdbcType="VARCHAR" property="productCode" />
		<result column="product_name" jdbcType="VARCHAR" property="productName" />
		<result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
		<result column="description" jdbcType="VARCHAR" property="description" />
		<result column="sale_status" jdbcType="TINYINT" property="saleStatus" />
		<result column="on_shelves_time" jdbcType="TIMESTAMP" property="onShelvesTime" />
		<result column="off_shelves_time" jdbcType="TIMESTAMP" property="offShelvesTime" />
		<result column="min_price" jdbcType="DECIMAL" property="minPrice" />
		<result column="max_price" jdbcType="DECIMAL" property="maxPrice" />
		<result column="sale_qty" jdbcType="BIGINT" property="saleQty" />
		<result column="delflag" jdbcType="INTEGER" property="delflag" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="operater" jdbcType="VARCHAR" property="operater" />
		<result column="product_type" jdbcType="VARCHAR" property="productType" />
		<result column="privilege_flag" jdbcType="INTEGER" property="privilegeFlag"/>
		<result column="private_url" jdbcType="VARCHAR" property="privateUrl" />
		<result column="temperature" jdbcType="INTEGER" property="temperature" />
		<result column="minimum_count" jdbcType="INTEGER" property="minimumCount" />
		<result column="purchase" jdbcType="INTEGER" property="purchase" />
		<result column="short_link" jdbcType="VARCHAR" property="shortLink" />
	</resultMap>

	<sql id="condition">
		<where>
			p.delflag = 0
			<if test="productTypeList != null and productTypeList.size()!= 0">
				AND p.product_type IN
				<foreach collection="productTypeList" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="productType != null and productType !=''">
			    AND p.product_type = #{productType}
            </if>
			<if test="cycleFlag != null ">
				AND p.cycle_flag = #{cycleFlag}
			</if>
			<if test="enableShow != null">
				AND p.enable_show = #{enableShow}
			</if>
			<if test="temperature != null">
				AND p.temperature = #{temperature}
			</if>
			<if test="productIds!=null and productIds.size()!=0">
				AND p.id IN
				<foreach collection="productIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="productId!=null">
				AND p.id = #{productId}
			</if>
			<if test="excludeProductIds!=null and excludeProductIds.size()!=0">
				AND p.id NOT IN
				<foreach collection="excludeProductIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="storeIds!=null and storeIds.size()!=0">
				AND p.store_id IN
				<foreach collection="storeIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="cateIds!=null and cateIds.size()!=0">
				AND p.cate_id IN
				<foreach collection="cateIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="excludeCateIds!=null and excludeCateIds.size()!=0">
				AND p.cate_id NOT IN
				<foreach collection="excludeCateIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="brandIds!=null and brandIds.size()!=0">
				AND p.brand_id IN
				<foreach collection="brandIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="saleStatus!=null and saleStatus.size()!=0">
				AND p.sale_status IN
				<foreach collection="saleStatus" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="productCode!=null and productCode!=''">AND p.product_code =#{productCode}</if>

			<if test="productName!=null and productName!=''">
				<bind name="bindeName" value="'%' +productName +'%'" />
				AND p.product_name like #{bindeName}
			</if>
			<if test="privilegeFlag != null">
				and p.privilege_flag = #{privilegeFlag}
			</if>
			<if test="minPrice!=null">
				AND min_price <![CDATA[ > ]]>#{minPrice}
			</if>
			<if test="maxPrice!=null">
				AND min_price <![CDATA[ <= ]]>#{maxPrice}
			</if>
			<if test="(skuIds!=null and skuIds.size()!=0) or (skuAttrMap!=null and skuAttrMap.size()!=0)">
				AND p.id IN (
				SELECT s.product_id FROM item_sku s
				<if test="skuAttrMap!=null and skuAttrMap.size()!=0">
					JOIN item_sku_attr sa ON s.id = sa.sku_id AND (sa.attr_name,sa.attr_value) IN
					<foreach collection="skuAttrMap.entrySet()" item="value" index="key" open="(" separator="," close=")">
						(#{key},#{value})
					</foreach>
				</if>
				WHERE s.delflag = 0
				<if test="skuIds!=null and skuIds.size()!=0">
					AND s.id IN
					<foreach collection="skuIds" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				)
			</if>
			<if test="skuCodes != null and skuCodes.size()!=0">
                AND p.id IN (
                SELECT s.product_id FROM item_sku s
                WHERE s.delflag = 0
                AND s.sku_code IN
                <foreach collection="skuCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
			<if test="attrMap!=null and attrMap.size()!=0">
				AND p.id IN (
				SELECT s.product_id FROM item_product_attr pa WHERE (pa.attr_name,pa.attr_value) IN
				<foreach collection="attrMap.entrySet()" item="value" index="key" open="(" separator="," close=")">
					(#{key},#{value})
				</foreach>
				)
			</if>
			<if test="displayIds!=null and displayIds.size()!=0">
				AND p.id IN (
				SELECT product_id FROM item_display_case_product where display_case_id IN
				<foreach collection="displayIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				)
			</if>
			<if test="cardCategoryIds != null and cardCategoryIds.size() != 0">
				AND p.card_category_id IN
				<foreach collection="cardCategoryIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="excludeCardCategoryIds != null and excludeCardCategoryIds.size() != 0">
				AND p.card_category_id NOT IN
				<foreach collection="excludeCardCategoryIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="discountActivityIds != null and discountActivityIds.size()!=0">
				And p.id in(select product_id from discount_activity_range where  delflag = 0 and discount_activity_id in
				<foreach collection="discountActivityIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				)
			</if>
			<if test="groupPromoVisible != null and groupPromoVisible == 1">
				AND p.id in(
				select ipg.product_id from item_product_group ipg
				inner join group_template gt on gt.id = ipg.group_id
				where gt.delflag = 0 and ipg.delflag =0 and gt.promo_visible = 1
				)
			</if>
			<if test="youzanFlag != null ">
				AND p.youzan_flag = #{youzanFlag}
			</if>
		</where>
	</sql>


	<update id="updatePrice">
		update item_product set
		min_price =
		<foreach collection="list" item="item" open=" case Id " close=" end " separator=" ">
			when #{item.id} then #{item.minPrice}
		</foreach>
		, max_price =
		<foreach collection="list" item="item" open=" case Id " close=" end " separator=" ">
			when #{item.id} then #{item.maxPrice}
		</foreach>
		where id in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.id}
		</foreach>
	</update>

	<update id="updatePrivateUrl">
		update item_product set
		private_url=#{privateUrl}
		where id =#{id}
	</update>


	<select id="findByCondition" resultMap="BaseResultMap">
		SELECT p.* FROM item_product p
		<include refid="condition"></include>
	</select>

	<select id="findByConditionSort" resultMap="BaseResultMap">
		SELECT p.* FROM item_product p
		<include refid="condition"></include>
		ORDER BY sort asc, update_time desc
	</select>

	<select id="selectByConditionSort" resultMap="BaseResultMap">
		SELECT p.* FROM item_product p
		<include refid="condition"></include>
		ORDER BY ${sortType}
	</select>

	<select id="findAutoOnShelves" resultType="java.lang.Long">
		select id from item_product
		WHERE sale_status = #{oldSaleStatus}
		AND on_shelves_time <![CDATA[ <= ]]>now()
		AND (off_shelves_time <![CDATA[ > ]]>now()
		OR off_shelves_time IS NULL OR off_shelves_time <![CDATA[ < ]]>
		on_shelves_time)
	</select>

	<update id="autoOnShelves">
		UPDATE item_product set sale_status = #{saleStatus}
		WHERE 1=1
		and id in
		<foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
			#{item}
		</foreach>
	</update>

	<select id="findAutoOffShelves" resultType="java.lang.Long">
		select id from item_product
		WHERE sale_status = #{oldSaleStatus}
		AND off_shelves_time <![CDATA[ <= ]]>now()
		AND off_shelves_time <![CDATA[ > ]]>on_shelves_time
	</select>
	<update id="autoOffShelves">
		UPDATE item_product set sale_status = #{saleStatus}
		WHERE 1=1
		and id in
		<foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
			#{item}
		</foreach>
	</update>

	<select id="findIdByCondition" resultType="java.lang.Long">
		SELECT p.id FROM item_product p
		<include refid="condition"></include>
	</select>
	<select id="isShowBig" resultType="com.hengtiansoft.item.entity.po.Product">
		SELECT * FROM item_product WHERE brand_id IN (SELECT id FROM item_brand WHERE sort =0 and delflag=0) and sale_status =1
	</select>

	<select id="hotList" resultType="com.hengtiansoft.item.entity.dto.ProductHotDTO">
		SELECT ip.id as productId,SUM(COUNT) AS sumCount,AVG(sk.origin_price) AS avgOriginPrice
		FROM order_info oi
		INNER JOIN order_sku sk ON oi.order_no = sk.order_no
		INNER JOIN item_product ip ON ip.id = sk.product_id
		WHERE oi.CREATE_time <![CDATA[ >= ]]> #{date}
		AND ip.privilege_flag = 0
		AND ip.product_type IN('c','d','s')
		AND ip.sale_status = 1
		and ip.delflag = 0
		AND oi.channel='lpk'
		AND oi.order_status IN(1,2,3)
		<if test="productIdList!=null and productIdList.size()!=0">
			AND ip.id IN
			<foreach collection="productIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="enableShow!=null">
			AND ip.enable_show = #{enableShow}
		</if>
		<if test="cycleFlag!=null">
			AND ip.cycle_flag = #{cycleFlag}
		</if>
		GROUP BY product_id ORDER BY sumCount desc,avgOriginPrice DESC
	</select>

	<select id="hotListV2" resultType="com.hengtiansoft.item.entity.dto.ProductHotDTO">
		SELECT ip.id as productId,ip.pic_url
		FROM item_product ip
		WHERE ip.privilege_flag = 0
		AND ip.product_type IN('c','d','s')
		AND ip.sale_status = 1
		AND ip.delflag = 0
		<if test="productIdList != null and productIdList.size()!=0">
			AND ip.id IN
			<foreach collection="productIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="enableShow!=null">
			AND ip.enable_show = #{enableShow}
		</if>
		<if test="cycleFlag!=null">
			AND ip.cycle_flag = #{cycleFlag}
		</if>
		ORDER BY if(isnull(ip.hot_list_sort),99999,ip.hot_list_sort) ASC
	</select>

	<select id="hotListAll" resultType="com.hengtiansoft.item.entity.dto.ProductHotDTO">
		SELECT ip.id as productId, SUM(COUNT) AS sumCount, AVG(sk.origin_price) AS avgOriginPrice
		FROM order_info oi
		INNER JOIN order_sku sk ON oi.order_no = sk.order_no
		INNER JOIN item_product ip ON ip.id = sk.product_id
		WHERE oi.CREATE_time <![CDATA[ >= ]]> #{date}
		AND ip.privilege_flag = 0
		AND ip.product_type IN('c','d','s')
		AND ip.delflag = 0
		AND oi.channel = 'lpk'
		AND oi.order_status IN(1,2,3)
		GROUP BY product_id ORDER BY sumCount DESC, avgOriginPrice DESC
	</select>

	<select id="couponProductList" resultType="com.hengtiansoft.item.entity.dto.ProductHotDTO">
		SELECT
		ip.id AS productId,
		ifnull( SUM( temp.COUNT ), 0 ) AS sumCount,
		ifnull( AVG( temp.origin_price ), 0 ) AS avgOriginPrice
		FROM
		item_product ip
		LEFT JOIN (
		SELECT
		sk.product_id,sk.count,sk.origin_price
		FROM
		order_info oi
		INNER JOIN order_sku sk ON sk.order_no = oi.order_no
		WHERE
		oi.CREATE_time <![CDATA[ >= ]]> #{date}
		AND oi.channel = 'lpk'
		AND oi.order_status IN ( 1, 2, 3 )) temp ON temp.product_id = ip.id
		WHERE
		ip.privilege_flag = 0
		AND ip.product_type IN ( 'c', 'd', 's' )
		AND ip.enable_show = 1
		AND ip.sale_status = 1
		<if test="productIdList!=null and productIdList.size()!=0">
			AND ip.id IN
			<foreach collection="productIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		GROUP BY
		ip.id
		ORDER BY
		sumCount DESC,
		avgOriginPrice DESC
	</select>

    <select id="findByIdsOrderField" resultType="com.hengtiansoft.item.entity.po.Product">
        SELECT * FROM item_product p
        WHERE id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY field(id,
        <foreach collection="list" index="index" item="item"  separator="," close=")">
            #{item}
        </foreach>
    </select>

	<select id="findOnSaleByIdsSort" resultType="com.hengtiansoft.item.entity.po.Product">
		SELECT * FROM item_product p
		WHERE p.sale_status = 1
		AND p.id IN
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		ORDER BY field(id,
		<foreach collection="list" index="index" item="item"  separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="findOnSaleProductByIdsOrderField" resultType="com.hengtiansoft.item.entity.po.Product">
		SELECT * FROM item_product p
		WHERE
		    p.delflag = 0
		AND p.privilege_flag = 0
		AND p.product_type IN ( 'c', 'd', 's' )
		AND p.enable_show = 1
		AND p.sale_status = 1
		And id IN
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		ORDER BY field(id,
		<foreach collection="list" index="index" item="item"  separator="," close=")">
			#{item}
		</foreach>
	</select>

	<update id="cleanHotSort">
		update item_product
		set hot_list_sort = null,
			update_time = update_time
	</update>

	<select id="selectTopKByCateIds" resultMap="BaseResultMap">
		select id
		from item_product
		where delflag = 0
		and product_type in ('c', 'd', 's')
		and enable_show = 1
		and sale_status = 1
		and hot_list_sort is not null
		and cate_id in
		<foreach collection="cateIds" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by hot_list_sort asc
		limit 0, 3
	</select>

	<select id="checkOnSaleByIds" resultMap="BaseResultMap">
		select id
		from item_product
		where delflag = 0
	    and product_type in ('c', 'd', 's')
	  	and enable_show = 1
	  	and sale_status = 1
		and id in
		<foreach collection="productIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	<select id="selectLowMilkProduct" resultType="com.hengtiansoft.item.entity.po.Product">
		select * from item_product where delflag = 0 and temperature = 1 and (product_type = 'n' or product_type = 'd' and cycle_flag = 1)
	</select>
    <select id="selectFullTrialActivityProductList"
            resultType="com.hengtiansoft.item.entity.po.Product">
		SELECT p.*, sa.share_discount,
		       sap.free_trial_sub_activity_id,
		       sap.sku_id as fullTrialSkuId,
		       sap.price as fullTrialPrice,
		       sap.type as fullTrialMainOrGiftType
		FROM free_trial_sub_activity_product sap
		left join item_product p on sap.target_id = p.id
		left join free_trial_sub_activity sa on sap.free_trial_sub_activity_id = sa.id
		where p.product_type IN
		<foreach collection="dto.productTypeList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND p.enable_show = #{dto.enableShow}
		<if test="dto.productIds!=null and dto.productIds.size()!=0">
			AND p.id IN
			<foreach collection="dto.productIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dto.saleStatus!=null and dto.saleStatus.size()!=0">
			AND p.sale_status IN
			<foreach collection="dto.saleStatus" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and sap.target_type = 1 and sap.delflag = 0 and sa.delflag = 0
	</select>
	<select id="queryProductByKeyword" resultType="java.lang.Long">
		select ip.id from item_product ip
		<if test="null != dto.matchGroup and '' != dto.matchGroup">
			inner join item_product_group tpg on ip.id = tpg.product_id
			inner join group_template gt on gt.id = tpg.group_id
		</if>
		<if test="null != dto.minPrice or null != dto.maxPrice">
			inner join item_sku sk on sk.product_id = ip.id and sk.delflag = 0
		</if>
		where ip.delflag = 0 and ip.product_type in('c', 'd', 's')
		  and ip.sale_status = 1
		  and ip.enable_show = 1
		  and not exists (select 1 from black_list bl where type = 1 and bl.value = ip.id )
		<if test="null != dto.productId">
			and ip.id = #{dto.productId}
		</if>
		<if test="null != dto.keyword and '' != dto.keyword">
			and ip.product_name MATCH_ALL #{dto.keyword}
		</if>
		<if test="null != dto.matchGroup and '' != dto.matchGroup">
			and gt.name MATCH_ANY #{dto.matchGroup}
		</if>
		<if test="null != dto.minPrice">
			and sk.sale_price <![CDATA[ >= ]]> #{dto.minPrice}
		</if>
		<if test="null != dto.maxPrice">
			and sk.sale_price <![CDATA[ <= ]]> #{dto.maxPrice}
		</if>
		group by ip.id
	</select>
</mapper>