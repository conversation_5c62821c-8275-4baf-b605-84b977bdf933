<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.DiscountActivityRangeMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.DiscountActivityRange">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="discount_activity_id" jdbcType="BIGINT" property="discountActivityId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="discount_price" jdbcType="DECIMAL" property="discountPrice" />
    <result column="stock" jdbcType="BIGINT" property="stock" />
    <result column="limit_num" jdbcType="BIGINT" property="limitNum" />
    <result column="used" jdbcType="BIGINT" property="used" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="INTEGER" property="delflag" />
  </resultMap>
    <update id="batchUpdate">
      update discount_activity_range
      <trim prefix="set" suffixOverrides=",">
        <trim prefix="discount_price = case" suffix="end,">
          <foreach collection="list" index="index" item="item">
            <if test="item.discountPrice != null">
                when id = #{item.id,jdbcType=BIGINT} then #{item.discountPrice}
            </if>
          </foreach>
        </trim>
        <trim prefix="stock = case" suffix="end,">
          <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=BIGINT} then #{item.stock}
          </foreach>
        </trim>
        <trim prefix="limit_num = case" suffix="end,">
          <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=BIGINT} then #{item.limitNum}
          </foreach>
        </trim>
        <trim prefix="minimum_count = case" suffix="end,">
          <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=BIGINT} then #{item.minimumCount}
          </foreach>
        </trim>
        <trim prefix="update_time = case" suffix="end,">
          <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime}
          </foreach>
        </trim>
      </trim>
      where id in
      <foreach collection="list" item="item" open="(" separator=", " close=")">
        #{item.id,jdbcType=BIGINT}
      </foreach>
    </update>
  <update id="batchUpdateStock">
    update discount_activity_range
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="stock = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stock}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" item="item" open="(" separator=", " close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <select id="selectByProductIds" resultType="com.hengtiansoft.item.entity.vo.DiscountActivityProductVO">
    select
      da.id,
      dar.product_id,
      dar.sku_id,
      sku.list_price,
      dar.discount_price
    from discount_activity da
           LEFT JOIN discount_activity_range dar on da.id = dar.discount_activity_id
           LEFT JOIN item_product ip on dar.product_id = ip.id
           LEFT JOIN item_sku sku on dar.sku_id = sku.id
    where dar.product_id in
    <foreach collection="productIds" item="productId" open="(" separator=", " close=")">
      #{productId}
    </foreach>
  </select>
  <select id="findProductByActivityId" resultType="com.hengtiansoft.item.entity.dto.DiscountProductDTO">
    select dar.discount_activity_id, dar.product_id, dar.sku_id, dar.discount_price,da.start_time,da.end_time,ip.product_name
    from discount_activity_range dar
    inner join discount_activity da on da.id = dar.discount_activity_id
    inner join item_sku isk on isk.id = dar.sku_id
    inner join item_product ip on ip.id = dar.product_id
    where dar.delflag = 0 and isk.delflag = 0 and ip.delflag = 0 and dar.discount_activity_id = #{discountActivityId}
  </select>
  <update id="decreaseStock">
    update discount_activity_range
    set
      used = used + #{stockNum},
      update_time = update_time
    where
      discount_activity_id = #{discountActivityId}
      and sku_id = #{skuId}
      and stock - used >= #{stockNum}
  </update>
  <update id="increaseStock">
    update discount_activity_range
    set
      used = used - #{stockNum},
      update_time = update_time
    where
      discount_activity_id = #{discountActivityId}
      and sku_id = #{skuId}
      and used > 0
  </update>
  <select id="checkStock" resultType="java.lang.Integer">
    SELECT COUNT(0)
    FROM discount_activity_range
    WHERE
      discount_activity_id = #{discountActivityId}
      AND sku_id = #{skuId}
      AND stock <![CDATA[ >= ]]> #{stockNum} + used
  </select>
    <select id="selectInProgressNoShareCouponProductIds" resultType="java.lang.Long">
      select dar.product_id
      from discount_activity_range dar
      inner join discount_activity da on da.id = dar.discount_activity_id
      where dar.delflag = 0 and da.delflag = 0
      and da.status = 2 and da.sharing_discount= 0
    </select>
</mapper>