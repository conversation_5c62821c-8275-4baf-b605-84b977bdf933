<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CardLogMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CardLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>