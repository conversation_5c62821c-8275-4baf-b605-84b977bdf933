<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.item.mapper.CommentTagMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.item.entity.po.CommentTag">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="show" jdbcType="VARCHAR" property="show" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
  </resultMap>

  <sql id="condition">
    <where>
      t.delflag = 0
      <if test="fuzzyName != null and fuzzyName !=''">
        <bind name="bindeName" value="'%' + fuzzyName + '%'" />
        AND t.name like #{bindeName}
      </if>
      <if test="tagType != null">
        AND t.type = #{tagType}
      </if>
      <if test="name != null and name !=''">
        AND t.name = #{name}
      </if>
      <if test="targetId != null">
        AND t.id in(
        select tag_id from comment_tag_join where type = 2 and target_id = #{targetId}
        )
      </if>
      <if test="tagIds != null and tagIds.size()!=0">
        AND t.id in
        <foreach collection="tagIds" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </sql>

  <select id="findByCondition" resultMap="BaseResultMap">
    SELECT t.* FROM comment_tag t
    <include refid="condition"></include>
    order by t.update_time desc
  </select>

  <select id="selectTagBySpuId" resultMap="BaseResultMap">
    SELECT
      ct.*
    FROM
      item_product ip
        INNER JOIN item_category ic ON ip.cate_id = ic.id
        INNER JOIN comment_tag_join ctj ON ctj.target_id = ic.parent_id
        INNER JOIN comment_tag ct ON ctj.tag_id = ct.id
    WHERE
       ct.delflag = 0
      AND ctj.delflag = 0
      AND ctj.type = 2
      AND ip.id = #{spuId}
  </select>

  <select id="selectCustomTagCountBySpuId" resultType="com.hengtiansoft.item.entity.vo.CommentTagCountVO">
    SELECT ctj.tag_id as tagId,ct.name, count(1) as num from comment c
    inner join comment_tag_join ctj on c.id = ctj.target_id
    inner join comment_tag ct on ct.id = ctj.tag_id
    where c.show = 1
    and c.delflag = 0
    and ctj.delflag = 0
    and ctj.type = 1
    and ct.type = 1
    and c.spu_id = #{spuId}
    and ct.id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item,jdbcType=INTEGER}
    </foreach>
    group by ctj.tag_id
    order by ct.create_time asc
  </select>

  <select id="selectSysTagCountBySpuId" resultType="com.hengtiansoft.item.entity.vo.CommentTagCountVO">
    SELECT
      ct.id as tagId,
      ct.name,
      ifnull( t.num, 0 ) as num
    FROM
      comment_tag ct
        LEFT JOIN (
        SELECT
          ctj.tag_id,
          count( 1 ) AS num
        FROM
          `comment` c
            INNER JOIN comment_tag_join ctj ON ctj.target_id = c.id
        WHERE
          c.SHOW = 1
          AND c.delflag = 0
          AND ctj.delflag = 0
          AND ctj.type = 1
          AND c.spu_id = #{spuId}
        GROUP BY ctj.tag_id
      ) t ON ct.id = t.tag_id
    WHERE
      type = 2
    ORDER BY
      ct.system_type ASC,
      ct.create_time ASC
  </select>
</mapper>