<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- Mybatis generator配置，默认针对MySQL数据库，具体参考 http://www.mybatis.org/generator/configreference/xmlconfig.html -->
<generatorConfiguration>
    <!-- 配置文件的路径 -->
    <properties resource="generator/mybatisGenerator.properties"/>
    <!-- 每一张表只生成一个实体类 -->
    <context id="default" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <!-- 生成的Java文件的编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 自动识别数据库关键字，默认false，如果设置为true，根据SqlReservedWords中定义的关键字列表；
             一般保留默认值，遇到数据库关键字（Java关键字），使用columnOverride覆盖 -->
        <property name="autoDelimitKeywords" value="false"/>
        <!-- beginningDelimiter和endingDelimiter：指明数据库的用于标记数据库对象名的符号，
        比如ORACLE就是双引号 \&quot;，MYSQL默认是`反引号 -->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <!-- tk -->
        <!--这里最关键的参数就是 mappers，配置后生成的 Mapper 接口都会自动继承上改接口-->
        <!--
        其他参数的含义：
        •   caseSensitive 是否区分大小写，默认值 false。如果数据库区分大小写，这里就需要配置为 true，这样当表名为 USER 时，会生成 @Table(name = "USER") 注解，否则使用小写 user 时会找不到表。
        •   forceAnnotation 是否强制生成注解，默认 false，如果设置为 true，不管数据库名和字段名是否一致，都会生成注解（包含 @Table 和 @Column）。
        •   beginningDelimiter 和 endingDelimiter 开始和结束分隔符，对于有关键字的情况下适用。
        •   useMapperCommentGenerator 是否使用通用 Mapper 提供的注释工具，默认 true 使用，这样在生成代码时会包含字段的注释（目前只有 mysql 和 oracle 支持），设置 false 后会用默认的，或者你可以配置自己的注释插件。
        •   generateColumnConsts 在生成的 model中，增加字段名的常量，便于使用 Example 拼接查询条件的时候使用。
        •   lombok 增加 model 代码生成时，可以直接生成 lombok 的 @Date 注解， 使用者在插件配置项中增加 <property name="lombok" value="Getter,Setter,ToString,Accessors"/> 即可生成对应包含注解的 model 类。
        -->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <!-- <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/> -->
            <property name="mappers" value="com.hengtiansoft.common.baseMapper.CrudMapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value="`"/>
            <property name="endingDelimiter" value="`"/>
            <property name="lombok" value="Data"/>
            <property name="swagger" value="true"/>
        </plugin>
        
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin">
            <property name="suppressJavaInterface" value="false"/>
        </plugin>



        <!-- 数据库连接的信息：驱动类、连接地址、用户名、密码 -->
        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.username}"
                        password="${jdbc.password}">
            <!-- 用于获取表的注释信息等 -->
            <property name="useInformationSchema" value="true"/>
        </jdbcConnection>


        <!-- Java类型解析配置 -->
        <javaTypeResolver>
            <!-- 将JDBC DECIMAL和NUMERIC类型解析为java.math.BigDecimal -->
            <property name="forceBigDecimals" value="false"/>
            <!-- 将JDBC DATE,TIME和TIMESTAMP类型解析为java.util.Date -->
            <property name="useJSR310Types" value="false"/>
        </javaTypeResolver>



        <!-- targetPackage生成Domain类的包路径，targetProject生成Domain类的物理路径 -->
        <javaModelGenerator targetPackage="${model.target.package}" targetProject="${model.target.project}" >
            <!-- 不使用schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
            <!-- 不为String类型的setter方法自动加上trim -->
            <property name="trimStrings" value="false"/>
            
           <!--  <property name="rootClass" value="com.hengtiansoft.common.baseMapper.BaseEntity"/> -->
        </javaModelGenerator>



        <!-- targetPackage生成XML文件的包路径，targetProject生成XML文件的物理路径-->
        <sqlMapGenerator targetPackage="${mapper.target.package}" targetProject="${mapper.target.project}">
            <!-- 不使用schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>




        <!-- targetPackage生成DAO接口的包路径，targetProject生成DAO接口的物理路径 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="${dao.target.package}" targetProject="${dao.target.project}">
            <!-- 不使用schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>


        <table tableName="item_product"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
               <generatedKey column="id" sqlStatement="JDBC"/>
        </table>

       
    </context>
</generatorConfiguration>