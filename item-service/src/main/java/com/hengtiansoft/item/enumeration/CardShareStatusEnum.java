package com.hengtiansoft.item.enumeration;

/**
 * 分享状态enum
 *
 * <AUTHOR>
 * @since 10.09.2020
 */
public enum CardShareStatusEnum {
    /**
     * 分享状态enum
     */
    SHARING(0, "分享中"),
    SUCCESS(1, "分享成功"),
    FAIL(2, "分享失败"),
    CANCEL(3, "分享取消");

    private  int code;
    private  String desc;

    CardShareStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
