package com.hengtiansoft.item.enumeration;

public enum ProductCycleEnum {

    SINGLE(0, "单次"), CYCLE(1, "周期");

    private  Integer code;
    private  String desc;

    ProductCycleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        ProductCycleEnum[] types = ProductCycleEnum.values();
        for (ProductCycleEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

}
