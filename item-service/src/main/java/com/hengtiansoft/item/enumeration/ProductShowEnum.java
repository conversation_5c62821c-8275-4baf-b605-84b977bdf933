/*
 * Project Name: item-service
 * File Name: ProductType.java
 * Class Name: ProductType
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.enumeration;

/**
 * Class Name: ProductType Description:
 *
 * <AUTHOR>
 *
 */
public enum ProductShowEnum {

    DISABLE(0, "不展示"), ENABLE(1, "展示");

    private  Integer code;
    private  String desc;

    ProductShowEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        ProductShowEnum[] types = ProductShowEnum.values();
        for (ProductShowEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

}
