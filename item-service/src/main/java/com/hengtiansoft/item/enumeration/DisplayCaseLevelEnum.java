package com.hengtiansoft.item.enumeration;

import org.apache.commons.lang3.StringUtils;

/**
 * Description: 分类级别枚举
 *
 * <AUTHOR>
 * @since 23.03.2020
 */
public enum DisplayCaseLevelEnum {

    FIRSTCATE(1, "一级分类"),

    SECONDCATE(2, "二级分类");


    private int code;

    private String desc;

    DisplayCaseLevelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static DisplayCaseLevelEnum getCodeByDesc(String desc) {
        for (DisplayCaseLevelEnum cateLevelEnum : DisplayCaseLevelEnum.values()) {
            if (desc.equals(cateLevelEnum.getDesc())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(int code) {
        DisplayCaseLevelEnum[] types = DisplayCaseLevelEnum.values();
        for (DisplayCaseLevelEnum type : types) {
            if (type.getCode() == code) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static DisplayCaseLevelEnum getEnumByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (DisplayCaseLevelEnum cateLevelEnum : DisplayCaseLevelEnum.values()) {
            if (name.equals(cateLevelEnum.name())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static DisplayCaseLevelEnum getEnumByCode(Integer code) {
        DisplayCaseLevelEnum[] values = DisplayCaseLevelEnum.values();
        for (DisplayCaseLevelEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
