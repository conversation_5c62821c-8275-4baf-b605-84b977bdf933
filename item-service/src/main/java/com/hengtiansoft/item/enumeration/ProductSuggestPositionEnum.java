package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ProductSuggestPositionEnum {

    HOME(1, "首页"),
    SHOP_CART(2, "购物车"),
    USER_CENTER(3, "个人中心"),
    CHECKIN_CENTER(4, "签到中心"),
    NEW_USER_AREA(5, "新人专区"),
    COUPON_ENTRY(6, "领券入口"),
    FRONT_CLASSIFY(7, "分类")
    ;


    private Integer code;

    private String desc;

    public static ProductSuggestPositionEnum getEnumByDesc(String desc) {
        for (ProductSuggestPositionEnum discountActivityStatusEnum : ProductSuggestPositionEnum.values()) {
            if (desc.equals(discountActivityStatusEnum.getDesc())) {
                return discountActivityStatusEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        ProductSuggestPositionEnum[] types = ProductSuggestPositionEnum.values();
        for (ProductSuggestPositionEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static ProductSuggestPositionEnum getEnumByCode(Integer code) {
        ProductSuggestPositionEnum[] values = ProductSuggestPositionEnum.values();
        for (ProductSuggestPositionEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static ProductSuggestPositionEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("商品推荐位置-枚举类型出错");
        }
        return Arrays.stream(ProductSuggestPositionEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("商品推荐位置-枚举类型出错"));
    }
}
