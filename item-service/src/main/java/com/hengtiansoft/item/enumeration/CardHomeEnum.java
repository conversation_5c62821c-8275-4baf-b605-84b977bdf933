package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 首页奶卡统计信息枚举
 *
 * <AUTHOR>
 * @date 2020/10/16 17:40
 */
@Getter
@AllArgsConstructor
public enum CardHomeEnum {

    ACTIVATE_COUNT("总计激活奶卡数"),
    ACTIVATE_RATE("近30天奶卡激活率"),
    TOTAL_NO_COUNT_USER("总计剩余0提用户数"),
    DAILY_NO_COUNT_USER("每日新增剩余0提用户量"),
    DAILY_NO_COUNT_USER_EXPORT("每日新增剩余0提用户量-下载"),
    CONTINUE_COUNT("总到期续卡用户"),
    SEVEN_DAYS_CONTINUE("过期7天内奶卡用户续卡占比"),
    FIFTEEN_DAYS_CONTINUE("过期15天内奶卡用户续卡占比");

    private String desc;
}
