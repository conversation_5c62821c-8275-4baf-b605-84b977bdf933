package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 商品操作类型
 * @author: shike
 */
@AllArgsConstructor
@Getter
public enum SpuOptTypeEnum {
    INSERT(1, "新增"),
    UPDATE(2, "修改"),
    ;
    private Integer code;
    private String desc;

    public static String getDescByCode(String code){
        if(code == null) {
            throw new BusinessException("商品操作类型-枚举类型出错");
        }
        return Arrays.stream(SpuOptTypeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("商品操作类型-枚举类型出错"))
                .getDesc();
    }

    public static SpuOptTypeEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("商品操作类型-枚举类型出错");
        }
        return Arrays.stream(SpuOptTypeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("商品操作类型-枚举类型出错"));
    }
}
