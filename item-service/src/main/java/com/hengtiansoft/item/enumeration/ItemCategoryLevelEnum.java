package com.hengtiansoft.item.enumeration;

import org.apache.commons.lang3.StringUtils;

/**
 * Description: 商品分类枚举
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
public enum ItemCategoryLevelEnum {
    FIRSTCATE(1, "一级分类"),

    SECONDCATE(2, "二级分类");


    private int code;

    private String desc;

    ItemCategoryLevelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ItemCategoryLevelEnum getCodeByDesc(String desc) {
        for (ItemCategoryLevelEnum cateLevelEnum : ItemCategoryLevelEnum.values()) {
            if (desc.equals(cateLevelEnum.getDesc())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(int code) {
        ItemCategoryLevelEnum[] types = ItemCategoryLevelEnum.values();
        for (ItemCategoryLevelEnum type : types) {
            if (type.getCode() == code) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static ItemCategoryLevelEnum getEnumByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ItemCategoryLevelEnum cateLevelEnum : ItemCategoryLevelEnum.values()) {
            if (name.equals(cateLevelEnum.name())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static ItemCategoryLevelEnum getEnumByCode(Integer code) {
        ItemCategoryLevelEnum[] values = ItemCategoryLevelEnum.values();
        for (ItemCategoryLevelEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
