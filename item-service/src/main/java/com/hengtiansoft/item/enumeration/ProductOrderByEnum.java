/*
 * Project Name: item-service
 * File Name: ProductOrderBy.java
 * Class Name: ProductOrderBy
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.enumeration;

/**
 * Class Name: ProductOrderBy Description:
 * 
 * <AUTHOR>
 *
 */
public enum ProductOrderByEnum {

    PRICE_ASC("priceAsc", "min_price"), 
    PRICE_DESC("priceDesc", "min_price desc"), 
    DATE_DESC("dateDesc", "on_shelves_time desc"), 
    SALES_DESC("salesDesc", "sale_qty desc"), 
    CREATETIME_DESC("createTimeDesc", "create_time desc"),
    CREATETIME_ASC("createTimeAsc", "create_time asc"),
    UPDATETIME_DESC("updateTimeDesc", "update_time desc"),
    ECARD_SORT_ASC("sortAsc", "sort"),
    ECARD_SORT_DESC("sortDesc", "sort desc"),
    HOT_LIST_ASC("hotListAsc", "case when hot_list_sort is null then 9999 else hot_list_sort end asc"),
    HOT_LIST_DESC("hotListDesc","case when hot_list_sort is null then 9999 else hot_list_sort end desc"),
    MIN_PRICE_DESC("minPriceDesc","min_price desc"),
    MIN_PRICE_ASC("minPriceAsc","min_price asc"),
    WEIGHT_ASC("weightAsc","weight asc"),
    WEIGHT_DESC("weightDesc","weight desc"),
    PRIVILEGE_DESC("privilegeDesc","privilege_flag desc"),
    ;

    private final String code;
    private final String desc;

    ProductOrderByEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        ProductOrderByEnum[] types = ProductOrderByEnum.values();
        for (ProductOrderByEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

}
