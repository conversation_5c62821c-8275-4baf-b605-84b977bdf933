package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.enumeration.CodeDesc;

/**
 * Description: 首页样式枚举
 *
 * <AUTHOR>
 * @since 01.04.2020
 */

public enum IndexStyleEnum implements CodeDesc {

    /**
     * 样式
     */
    LIGHT(1, "闪购样式"),
    FLOOR(2, "楼层样式"),
    ;

    private int code;

    private String desc;

    IndexStyleEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static IndexStyleEnum getEnumByCode(Integer code) {
        IndexStyleEnum[] values = IndexStyleEnum.values();
        for (IndexStyleEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
