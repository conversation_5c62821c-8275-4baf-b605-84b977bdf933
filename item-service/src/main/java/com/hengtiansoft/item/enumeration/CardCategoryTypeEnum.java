package com.hengtiansoft.item.enumeration;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 **/
public enum CardCategoryTypeEnum {


    year(1, "年卡", 24),
    half_a_year(2, "半年卡", 12),
    quarter(3, "季卡", 6),
    mouth(4, "月卡", 2);

    private Integer code;
    private String desc;
    private Integer num;

    CardCategoryTypeEnum(Integer code, String desc, Integer num) {
        this.code = code;
        this.desc = desc;
        this.num = num;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public static Integer getNumByCode(Integer code){
        if (Objects.isNull(code)) {
            return 0;
        }
        for (CardCategoryTypeEnum value : CardCategoryTypeEnum.values()) {
            if (code.equals(value.code)){
                return value.num;
            }
        }
        return 0;
    }

}


