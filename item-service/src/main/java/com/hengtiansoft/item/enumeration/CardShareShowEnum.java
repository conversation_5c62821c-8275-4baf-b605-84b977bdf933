package com.hengtiansoft.item.enumeration;

/**
 * 分享展示枚举
 *
 * <AUTHOR>
 * @since 17.09.2020
 */
public enum CardShareShowEnum {

    SUCCESS(1, "领取成功"),
    ALREADY_RECEIVE(2, "自己已领取"),
    OTHER_RECEIVE(3, "别人已领取"),
    OVERTIME(4, "超时"),
    ONESELF_RECEIVE_ONESELF(5, "自己领取自己的奶卡"),
    CANCEL_RECEIVE(6, "分享已取消");

    private int code;
    private String desc;

    CardShareShowEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
