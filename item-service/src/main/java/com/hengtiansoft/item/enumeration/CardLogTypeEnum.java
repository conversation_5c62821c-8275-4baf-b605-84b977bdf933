/**
 * @(#)CardLogTypeEnum.java, 2021/1/13.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 奶卡操作类型
 *
 * <AUTHOR>
 * @date 2021/1/13 17:05
 */
@AllArgsConstructor
@Getter
public enum CardLogTypeEnum {

    FROZEN(3, "冻结奶卡"),
    VOIDED(4, "作废奶卡"),
    CHANGE_PHONE(5, "奶卡换绑");

    private Integer code;
    private String desc;
}
