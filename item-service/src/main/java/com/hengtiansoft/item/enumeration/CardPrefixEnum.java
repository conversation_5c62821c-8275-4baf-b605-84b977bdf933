package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum CardPrefixEnum {

    NK(24, "NK"),
    B<PERSON>(12, "BNK"),
    J<PERSON>(6, "JK"),
    YK(2, "YK");

    private Integer code;

    private String desc;

    public static String getDescByCode(Integer code) {
        for(CardPrefixEnum cardPrefixEnum : values()) {
            if(cardPrefixEnum.code.equals(code)) {
                return cardPrefixEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}
