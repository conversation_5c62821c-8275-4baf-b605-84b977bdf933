package com.hengtiansoft.item.enumeration;

import org.apache.commons.lang3.StringUtils;

public enum ItemCategoryTypeEnum {
    VIRTUAL_CARD(0, "电子奶卡"),
    SINGLE_PRODUCT(1, "单品商品"),
    PRODUCT(2, "普通商品（提奶商品）"),
    ENTITY_CARD(3, "实体奶卡"),
    DERIVATIVE(4, "衍生品"),
    CYCLE(5, "周期购");


    private Integer code;

    private String desc;

    ItemCategoryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ItemCategoryTypeEnum getCodeByDesc(String desc) {
        for (ItemCategoryTypeEnum cateLevelEnum : ItemCategoryTypeEnum.values()) {
            if (desc.equals(cateLevelEnum.getDesc())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        ItemCategoryTypeEnum[] types = ItemCategoryTypeEnum.values();
        for (ItemCategoryTypeEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static ItemCategoryTypeEnum getEnumByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ItemCategoryTypeEnum cateLevelEnum : ItemCategoryTypeEnum.values()) {
            if (name.equals(cateLevelEnum.name())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static ItemCategoryTypeEnum getEnumByCode(Integer code) {
        ItemCategoryTypeEnum[] values = ItemCategoryTypeEnum.values();
        for (ItemCategoryTypeEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
