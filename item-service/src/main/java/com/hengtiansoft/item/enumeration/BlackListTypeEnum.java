package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BlackListTypeEnum {

    PRODUCT_KNOWLEDGE(1, "商品知识库");


    private Integer code;

    private String desc;

    public static BlackListTypeEnum getEnumByDesc(String desc) {
        for (BlackListTypeEnum blackListTypeEnum : BlackListTypeEnum.values()) {
            if (desc.equals(blackListTypeEnum.getDesc())) {
                return blackListTypeEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        BlackListTypeEnum[] types = BlackListTypeEnum.values();
        for (BlackListTypeEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static BlackListTypeEnum getEnumByCode(Integer code) {
        BlackListTypeEnum[] values = BlackListTypeEnum.values();
        for (BlackListTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
