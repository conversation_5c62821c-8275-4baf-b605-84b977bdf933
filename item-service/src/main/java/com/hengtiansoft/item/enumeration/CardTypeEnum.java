package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 奶卡类型枚举
 *
 * <AUTHOR>
 * @date 2020/9/4 15:19
 */
@Getter
@AllArgsConstructor
public enum CardTypeEnum {

    ENTITY_CARD(0, "实体卡"),
    ELECTRONIC_CARD(1, "电子卡"),
    CYCLE_CARD(2, "周期购虚拟卡");

    private Integer code;
    private String desc;

    public static String getDescBycode(Integer code) {
        if(null == code) {
            return "";
        }
        for(CardTypeEnum cardTypeEnum : values()) {
            if(cardTypeEnum.getCode().equals(code)) {
                return cardTypeEnum.getDesc();
            }
        }
        return "";
    }
}
