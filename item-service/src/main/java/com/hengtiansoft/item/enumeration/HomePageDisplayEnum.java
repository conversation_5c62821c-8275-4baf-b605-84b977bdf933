package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.enumeration.CodeDesc;

/**
 * Description: 首页运营位枚举
 *
 * <AUTHOR>
 * @since 21.04.2020
 */

public enum HomePageDisplayEnum implements CodeDesc {

    /**
     * 运营位枚举
     */
    NEW(1, "上新"),
    HOT(2, "热销"),
    SELFSUPPORT(3, "自营"),
    BIG(4, "大牌"),
    GLEELY(5, "点点"),
    ;


    private int code;

    private String desc;

    HomePageDisplayEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
