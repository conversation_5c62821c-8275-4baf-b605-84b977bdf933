package com.hengtiansoft.item.enumeration;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommentTagTypeEnum {

    CUSTOM(1, "自定义"),

    SYSTEM(2, "系统");


    private Integer code;

    private String desc;


    public static CommentTagTypeEnum getEnumByDesc(String desc) {
        for (CommentTagTypeEnum commentTagJoinTypeEnum : CommentTagTypeEnum.values()) {
            if (desc.equals(commentTagJoinTypeEnum.getDesc())) {
                return commentTagJoinTypeEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        CommentTagTypeEnum[] types = CommentTagTypeEnum.values();
        for (CommentTagTypeEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static CommentTagTypeEnum getEnumByCode(Integer code) {
        CommentTagTypeEnum[] values = CommentTagTypeEnum.values();
        for (CommentTagTypeEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
