/*
 * Project Name: item
 * File Name: ProductSaleStatus.java
 * Class Name: ProductSaleStatus
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 商品上下架状态
 * <AUTHOR>
 *
 */
public enum ProductSaleStatusEnum {

    DRAFT(-1, "已创建"), NEW(0, "未上架"), ON_SHELVES(1, "已上架");
    // , OFF_SHELVES(2, "已下架")

    private static final List<Integer> USEING_STATUS = new ArrayList<>();
    static {
        USEING_STATUS.add(NEW.getCode());
        USEING_STATUS.add(ON_SHELVES.getCode());
    }

    private final Integer code;

    private final String desc;

    ProductSaleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(int code) {
        ProductSaleStatusEnum[] types = ProductSaleStatusEnum.values();
        for (ProductSaleStatusEnum type : types) {
            if (type.getCode() == code) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static List<Integer> getUseingStatus() {
        return USEING_STATUS;
    }

    public static ProductSaleStatusEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("商品上下架状态-枚举类型出错");
        }
        return Arrays.stream(ProductSaleStatusEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("商品上下架状态-枚举类型出错"));
    }

    public static boolean isOnShelves(ProductBaseDTO baseDTO){
        if(baseDTO == null){
            return false;
        }
        return ON_SHELVES.getCode().equals(baseDTO.getSaleStatus());
    }



}
