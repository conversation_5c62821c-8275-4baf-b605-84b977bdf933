package com.hengtiansoft.item.enumeration;


import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 系统标签类型
 */
@Getter
@AllArgsConstructor
public enum CommentTagSystemTypeEnum {

    ALL(1, "全部"),

    PIC(2, "有图");


    private Integer code;

    private String desc;

    public static CommentModeEnum getEnum(Integer code){
        if(code == null){
            return null;
        }
        return Arrays.stream(CommentModeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("系统标签类型-枚举类型出错"));
    }

}
