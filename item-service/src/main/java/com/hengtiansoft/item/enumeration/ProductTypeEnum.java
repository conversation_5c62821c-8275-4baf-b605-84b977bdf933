/*
 * Project Name: item-service
 * File Name: ProductType.java
 * Class Name: ProductType
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Class Name: ProductType Description:
 *
 * <AUTHOR>
 *
 */
public enum ProductTypeEnum {

    NORMAL("n", "提奶商品"),
    PACKAGE("c", "电子奶卡商品") ,
    SINGLE_PRODUCT("d","单品商品"),
    ENTITY_CARD("s", "实体奶卡"),
    DERIVATIVE("y", "衍生品");

    ;

    private final String code;
    private final String desc;

    ProductTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        ProductTypeEnum[] types = ProductTypeEnum.values();
        for (ProductTypeEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static ProductTypeEnum getEnum(String code){
        if(code == null){
            throw new BusinessException("商品类型枚举出错");
        }
        return Arrays.stream(ProductTypeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("商品类型枚举出错"));
    }

    public static boolean needStock(String code, Integer buyType){
        ProductTypeEnum resultEnum = getEnum(code);
        if(ProductTypeEnum.SINGLE_PRODUCT == resultEnum){
            // ShopCartBuyTypeEnum 周期购不用库存
            if(Objects.equals(2, buyType)){
                return false;
            }else{
                return true;
            }
        } else if (ProductTypeEnum.ENTITY_CARD == resultEnum){
            return true;
        } else if (ProductTypeEnum.PACKAGE == resultEnum) {
            return true;
        }
        return false;
    }

    public static boolean needStockByFlag(String code, Integer cycleFlag){
        ProductTypeEnum resultEnum = getEnum(code);
        if(ProductTypeEnum.SINGLE_PRODUCT == resultEnum){
            if(Objects.equals(1, cycleFlag)){
                return false;
            }else{
                return true;
            }
        }else if (ProductTypeEnum.ENTITY_CARD == resultEnum){
            return true;
        }else if (ProductTypeEnum.PACKAGE == resultEnum) {
            return true;
        }
        return false;
    }

}
