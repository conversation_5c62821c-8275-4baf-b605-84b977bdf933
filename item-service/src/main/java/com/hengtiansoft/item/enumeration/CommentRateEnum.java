package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 评价等级
 * @author: shike
 */
@AllArgsConstructor
@Getter
public enum CommentRateEnum {
    GOOD(1, "好评"),
    MID(2, "中评"),
    BAD(3, "差评"),
    ;
    private Integer code;
    private String desc;

    public static CommentRateEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("评价等级-枚举类型出错");
        }
        return Arrays.stream(CommentRateEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("评价等级-枚举类型出错"));
    }
}
