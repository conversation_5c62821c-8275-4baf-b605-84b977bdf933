package com.hengtiansoft.item.enumeration;

/**
 * @Author: ji<PERSON><PERSON><PERSON><PERSON>@hengtiansoft.com
 */
public enum ProductUnitEnum {

    /**
     * 提奶商品单位
     */
    TI("1", "提"),
    DAI("2", "袋"),
    HE("3", "盒"),
    XIANG("4", "箱"),
    ZHANG("5","张"),
    GUAN("6","罐"),
    GE("7","个"),
    JIAN("8","件");

    private String code;

    private String desc;

    ProductUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        if (null == code) {
            return "";
        }
        for (ProductUnitEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return "";
    }
}
