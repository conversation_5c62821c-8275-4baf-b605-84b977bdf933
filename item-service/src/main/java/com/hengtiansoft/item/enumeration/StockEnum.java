package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.enumeration.CodeDesc;

public enum StockEnum implements CodeDesc {

    ENOUGH(0, "充足"),

    NOT_ENOUGH(1, "不足");

    private Integer code;
    private String desc;

    StockEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
