package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum DiscountActivityStatusEnum {

    NOT_STARTED(1, "未开始"),
    IN_PROGRESS(2, "进行中"),
    END(3, "已结束");


    private Integer code;

    private String desc;

    public static DiscountActivityStatusEnum getEnumByDesc(String desc) {
        for (DiscountActivityStatusEnum discountActivityStatusEnum : DiscountActivityStatusEnum.values()) {
            if (desc.equals(discountActivityStatusEnum.getDesc())) {
                return discountActivityStatusEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        DiscountActivityStatusEnum[] types = DiscountActivityStatusEnum.values();
        for (DiscountActivityStatusEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static DiscountActivityStatusEnum getEnumByCode(Integer code) {
        DiscountActivityStatusEnum[] values = DiscountActivityStatusEnum.values();
        for (DiscountActivityStatusEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static DiscountActivityStatusEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("限时折扣活动状态-枚举类型出错");
        }
        return Arrays.stream(DiscountActivityStatusEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("限时折扣活动状态-枚举类型出错"));
    }
}
