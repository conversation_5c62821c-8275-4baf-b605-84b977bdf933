package com.hengtiansoft.item.enumeration;

public enum ProductTemperatureEnum {

    NORMAL(0, "非低温奶"), LOW(1, "低温奶");

    private  Integer code;
    private  String desc;

    ProductTemperatureEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        ProductTemperatureEnum[] types = ProductTemperatureEnum.values();
        for (ProductTemperatureEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

}
