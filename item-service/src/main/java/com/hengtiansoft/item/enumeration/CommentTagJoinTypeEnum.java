package com.hengtiansoft.item.enumeration;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommentTagJoinTypeEnum {

    COMMENT(1, "评论"),

    CATEGORY(2, "类目");


    private Integer code;

    private String desc;


    public static CommentTagJoinTypeEnum getEnumByDesc(String desc) {
        for (CommentTagJoinTypeEnum commentTagJoinTypeEnum : CommentTagJoinTypeEnum.values()) {
            if (desc.equals(commentTagJoinTypeEnum.getDesc())) {
                return commentTagJoinTypeEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        CommentTagJoinTypeEnum[] types = CommentTagJoinTypeEnum.values();
        for (CommentTagJoinTypeEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static CommentTagJoinTypeEnum getEnumByCode(Integer code) {
        CommentTagJoinTypeEnum[] values = CommentTagJoinTypeEnum.values();
        for (CommentTagJoinTypeEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
