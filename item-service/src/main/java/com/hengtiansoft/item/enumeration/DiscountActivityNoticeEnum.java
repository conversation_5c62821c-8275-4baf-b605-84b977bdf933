package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DiscountActivityNoticeEnum {

    NOT_NOTICE(1, "不预告"),
    SUCCESS_NOTICE(2, "创建成功后立即预告"),
    AFTER_HOUR_NOTICE(3, "创建n小时后预告");


    private Integer code;

    private String desc;

    public static DiscountActivityNoticeEnum getEnumByDesc(String desc) {
        for (DiscountActivityNoticeEnum discountActivityNoticeEnum : DiscountActivityNoticeEnum.values()) {
            if (desc.equals(discountActivityNoticeEnum.getDesc())) {
                return discountActivityNoticeEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        DiscountActivityNoticeEnum[] types = DiscountActivityNoticeEnum.values();
        for (DiscountActivityNoticeEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static DiscountActivityNoticeEnum getEnumByCode(Integer code) {
        DiscountActivityNoticeEnum[] values = DiscountActivityNoticeEnum.values();
        for (DiscountActivityNoticeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
