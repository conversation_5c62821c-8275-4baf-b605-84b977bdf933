package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DiscountActivityPeopleLimitEnum {

    NO_LIMIT(1, "不限"),
    NEW_USER(2, "新用户"),
    LABEL(3, "其他");


    private Integer code;

    private String desc;

    public static DiscountActivityPeopleLimitEnum getEnumByDesc(String desc) {
        for (DiscountActivityPeopleLimitEnum discountActivityNoticeEnum : DiscountActivityPeopleLimitEnum.values()) {
            if (desc.equals(discountActivityNoticeEnum.getDesc())) {
                return discountActivityNoticeEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        DiscountActivityPeopleLimitEnum[] types = DiscountActivityPeopleLimitEnum.values();
        for (DiscountActivityPeopleLimitEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static DiscountActivityPeopleLimitEnum getEnumByCode(Integer code) {
        DiscountActivityPeopleLimitEnum[] values = DiscountActivityPeopleLimitEnum.values();
        for (DiscountActivityPeopleLimitEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
