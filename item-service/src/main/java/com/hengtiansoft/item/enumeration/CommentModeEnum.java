package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 评价模式
 * @author: shike
 */
@AllArgsConstructor
@Getter
public enum CommentModeEnum {
    AUTO(1, "自动"),
    MANUAL(2, "手动"),
    ;
    private Integer code;
    private String desc;

    public static CommentModeEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("评价模式-枚举类型出错");
        }
        return Arrays.stream(CommentModeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("评价模式-枚举类型出错"));
    }
}
