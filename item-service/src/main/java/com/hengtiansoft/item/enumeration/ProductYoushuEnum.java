package com.hengtiansoft.item.enumeration;

public enum ProductYoushuEnum {

    NO_SYNCHRONIZED(0, "未同步"), SYNCHRONIZED(1, "已同步");

    private  Integer code;
    private  String desc;

    ProductYoushuEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        ProductYoushuEnum[] types = ProductYoushuEnum.values();
        for (ProductYoushuEnum type : types) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

}
