package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 奶卡状态枚举
 *
 * <AUTHOR>
 * @date 2020/9/3 15:03
 */
@Getter
@AllArgsConstructor
public enum CardStatusEnum {

    SPENDABLE(1, "可使用"),
    USED(2, "已使用"),
    FROZEN(3, "已冻结"),
    VOIDED(4, "已作废");

    private Integer code;
    private String desc;

    public static CardStatusEnum getEnumByCode(int code) {
        for (CardStatusEnum card : values()) {
            if (card.getCode().equals(code)) {
                return card;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        if(null == code) {
            return "";
        }
        for(CardStatusEnum cardStatusEnum : values()) {
            if(cardStatusEnum.getCode().equals(code)) {
                return cardStatusEnum.getDesc();
            }
        }
        return "";
    }
}
