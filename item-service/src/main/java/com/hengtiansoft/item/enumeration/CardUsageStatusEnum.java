package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 奶卡使用状态枚举
 *
 * <AUTHOR>
 * @date 2020/9/3 14:58
 */
@Getter
@AllArgsConstructor
public enum CardUsageStatusEnum {

    UNRECEIVED(0, "未领取"),
    UNACTIVATED(1, "未使用"),
    INSERVICE(2, "服务中"),
    PENDING(3, "暂停中"),
    SERVICECOMPLETED(4, "服务完成"),
    EXPIRED(5, "已过期");

    private Integer code;
    private String desc;

    /**
     * 获取订完的使用状态
     *
     * @return
     */
    public static List<Integer> getUnEffected() {
        return Arrays.asList(SERVICECOMPLETED.getCode(), EXPIRED.getCode());
    }

    public static final String getDescByCode(Integer code) {
        for (CardUsageStatusEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}
