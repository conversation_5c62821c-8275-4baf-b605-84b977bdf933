package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum DateTypeEnum {

    DISPATCH_DATE(1, "提奶日期"),
    DELIVERY_TIME(2, "发货日期");

    private Integer code;
    private String desc;

    public static DateTypeEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("日期类型-枚举类型出错");
        }
        return Arrays.stream(DateTypeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("日期类型-枚举类型出错"));
    }
}
