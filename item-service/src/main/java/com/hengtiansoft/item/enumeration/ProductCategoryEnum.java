package com.hengtiansoft.item.enumeration;

import lombok.Getter;

import java.util.Objects;

/**
 * @program: milk-card-server
 * @description: 产品类别枚举
 * @author: haiyang
 * @create: 2024-03-07 17:45
 **/
public enum ProductCategoryEnum {
    NORMAL_MILK(1, "常温奶"),
    MILK_POWDER(2, "奶粉"),
    FROZEN_MILK(3, "冷藏奶"),
    ICE_CREAM(4, "冰淇淋"),
    BEEF(5, "牛肉"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String desc;

    ProductCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isLowTemperature(Integer code) {
        if (Objects.isNull(code)) return false;
        return code.equals(FROZEN_MILK.code);
    }

}
