package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.enumeration.CodeDesc;

/**
 * Description: 商品失效枚举
 *
 * <AUTHOR>
 * @since 21.04.2020
 */
public enum InvalidEnum implements CodeDesc {

    /**
     * 商品失效枚举
     */
    IS(1, "失效"),
    NOT(2, "未失效"),
    ;

    private int code;

    private String desc;

    InvalidEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
