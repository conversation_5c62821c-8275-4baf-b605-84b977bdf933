package com.hengtiansoft.item.enumeration;

import org.apache.commons.lang3.StringUtils;

/**
 * Description: 商品分类枚举
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
public enum CategoryTypeEnum {
    MILK_CARD(0, "奶卡商品"),

    SINGLE_PRODUCT(1, "单品商品"),

    NORMAL(2, "提奶商品");


    private Integer code;

    private String desc;

    CategoryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static CategoryTypeEnum getCodeByDesc(String desc) {
        for (CategoryTypeEnum cateLevelEnum : CategoryTypeEnum.values()) {
            if (desc.equals(cateLevelEnum.getDesc())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        CategoryTypeEnum[] types = CategoryTypeEnum.values();
        for (CategoryTypeEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static CategoryTypeEnum getEnumByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (CategoryTypeEnum cateLevelEnum : CategoryTypeEnum.values()) {
            if (name.equals(cateLevelEnum.name())) {
                return cateLevelEnum;
            }
        }
        return null;
    }

    public static CategoryTypeEnum getEnumByCode(Integer code) {
        CategoryTypeEnum[] values = CategoryTypeEnum.values();
        for (CategoryTypeEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
