package com.hengtiansoft.item.enumeration;

import lombok.Getter;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-21 10:40
 **/
public enum ProductSuggestItemTypeEnum {
    APPOINT_RECOMMEND(1, "指定推荐"),
    AUTO_RECOMMEND(2, "自动推荐")
    ;

    @Getter
    private Integer code;

    @Getter
    private String desc;

    ProductSuggestItemTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
