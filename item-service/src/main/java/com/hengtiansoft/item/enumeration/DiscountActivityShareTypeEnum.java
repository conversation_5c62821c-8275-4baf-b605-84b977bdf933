package com.hengtiansoft.item.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DiscountActivityShareTypeEnum {

    NO_COUPON(0, "与优惠券互斥"),

    COUPON(1, "与优惠券不互斥");


    private Integer code;

    private String desc;

    public static DiscountActivityShareTypeEnum getEnumByDesc(String desc) {
        for (DiscountActivityShareTypeEnum discountActivityShareEnum : DiscountActivityShareTypeEnum.values()) {
            if (desc.equals(discountActivityShareEnum.getDesc())) {
                return discountActivityShareEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        DiscountActivityShareTypeEnum[] types = DiscountActivityShareTypeEnum.values();
        for (DiscountActivityShareTypeEnum type : types) {
            if (type.getCode().equals(code) ) {
                return type.getDesc();
            }
        }
        return null;
    }


    public static DiscountActivityShareTypeEnum getEnumByCode(Integer code) {
        DiscountActivityShareTypeEnum[] values = DiscountActivityShareTypeEnum.values();
        for (DiscountActivityShareTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
