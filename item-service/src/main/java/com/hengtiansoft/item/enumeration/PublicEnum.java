package com.hengtiansoft.item.enumeration;

public enum PublicEnum {

    PUBLIC(0, "公开"),
    PRIVATE(1, "不公开")
    ;

    private Integer code;


    private String desc;

    PublicEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        if(null == code) {
            return "";
        }
        for(PublicEnum data : values()) {
            if(data.getCode().equals(code)) {
                return data.getDesc();
            }
        }
        return "";
    }
}

