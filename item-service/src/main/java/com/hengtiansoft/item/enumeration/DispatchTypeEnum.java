package com.hengtiansoft.item.enumeration;

import com.hengtiansoft.common.entity.exception.BusinessException;

import java.util.Arrays;

/**
 * @Author: jian<PERSON><PERSON><EMAIL>
 */
public enum DispatchTypeEnum {

    /**
     * 发货类型
     */
    WEEK(1, "按每周几"),
    MONTH(2, "按每月几号"),
    WEEK_AFTER_FIRST_DISPATCH(3, "按周（首次发货算）"),
    DAY_AFTER_FIRST_DISPATCH(4, "按天（首次发货算）");

    private Integer code;

    private String desc;

    DispatchTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (null == code) {
            return "";
        }
        for (DispatchTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return "";
    }
    public static DispatchTypeEnum getEnum(Integer code){
        if(code == null){
            throw new BusinessException("枚举类型出错");
        }
        return Arrays.stream(DispatchTypeEnum.values())
                .filter(envEnum -> envEnum.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException("枚举类型出错"));
    }

}
