package com.hengtiansoft.item.interfaces;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.GroupTemplateDTO;
import com.hengtiansoft.item.entity.dto.GroupTemplateSearchDTO;
import com.hengtiansoft.item.entity.vo.GroupTemplateVO;

import java.util.List;

public interface GroupTemplateManager {

    void save(GroupTemplateDTO dto);

    List<GroupTemplateVO> list(GroupTemplateSearchDTO searchDTO);

    PageVO<GroupTemplateVO> page(GroupTemplateSearchDTO searchDTO);

    void delete(Long id, String userName);
}
