package com.hengtiansoft.item.interfaces;

import java.util.List;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.ItemDeliveryAddressConditionDTO;
import com.hengtiansoft.item.entity.dto.ItemDeliveryAddressDTO;
import com.hengtiansoft.item.entity.vo.ItemDeliveryAddressVO;

public interface ItemDeliveryAddressManager {

    /**
     * 新增配送区域配置
     * 
     * @param dto
     */
    void insertOne(ItemDeliveryAddressDTO dto);

    /**
     * 校验地址是否可配送
     * 
     * @param id
     * @param provinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    Boolean checkCanDelivery(Long productId, String provinceCode, String cityCode, String districtCode);

    /**
     * 删除配送区域配置
     * 
     * @param id
     */
    void delete(Long id);

    /**
     * 根据id查询配送区域
     * 
     * @param id
     * @return
     */
    ItemDeliveryAddressVO selectById(Long id);

    /**
     * 条件查询配送区域
     * 
     * @param condition
     * @return
     */
    List<ItemDeliveryAddressVO> listByCondition(ItemDeliveryAddressConditionDTO condition);

    /**
     * 分页条件查询配送区域配置
     * 
     * @param condition
     * @return
     */
    PageVO<ItemDeliveryAddressVO> listPageByCondition(ItemDeliveryAddressConditionDTO condition);

    void updateOne(ItemDeliveryAddressDTO dto);

}