/*
 * Project Name: item-service
 * File Name: BrandManager.java
 * Class Name: BrandManager
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.interfaces;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.BrandDTO;
import com.hengtiansoft.item.entity.dto.BrandSearchDTO;
import com.hengtiansoft.item.entity.dto.SearchProductByDisplayDTO;

import java.util.List;

/**
 * Class Name: BrandManager Description:
 * 
 * <AUTHOR>
 *
 */
public interface BrandManager {

    BrandDTO detail(Long id);

    BrandDTO save(BrandDTO dto);

    int delete(Long id);

    PageVO<BrandDTO> search(BrandSearchDTO dto);

    List<Long> searchBigProducts(SearchProductByDisplayDTO dto);

}
