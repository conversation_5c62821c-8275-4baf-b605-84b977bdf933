package com.hengtiansoft.item.interfaces;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.DeliveryAddressTemplateConditionDTO;
import com.hengtiansoft.item.entity.dto.DeliveryAddressTemplateDTO;
import com.hengtiansoft.item.entity.po.DeliveryAddressTemplate;
import com.hengtiansoft.item.entity.vo.DeliveryAddressTemplateSimpleVO;
import com.hengtiansoft.item.entity.vo.DeliveryAddressTemplateVO;

import java.util.List;

public interface DeliveryAddressTemplateManager {

    /**
     * 分页条件查询配送区域配置
     *
     * @param condition
     * @return
     */
    PageVO<DeliveryAddressTemplateVO> listPageByCondition(DeliveryAddressTemplateConditionDTO condition);

    /**
     * 查询不配送地区明细
     *
     * @param id
     * @return
     */
    DeliveryAddressTemplateVO selectById(Long id);

    /**
     * 查询不配送地区模板
     *
     * @param id
     * @return
     */
    DeliveryAddressTemplate selectSimpleById(Long id);


    /**
     * 保存不配送地区模板
     *
     * @param dto
     * @return
     */
    void save(DeliveryAddressTemplateDTO dto);

    /**
     * 条件查询配送区域详情配置
     *
     * @param condition
     * @return
     */
    List<DeliveryAddressTemplateVO> listByCondition(DeliveryAddressTemplateConditionDTO condition);

    /**
     * 条件查询配送区域简单模板
     *
     * @param condition
     * @return
     */
    List<DeliveryAddressTemplateSimpleVO> querySimpleList(DeliveryAddressTemplateConditionDTO condition);

    /**
     * 校验地址是否可配送
     *
     * @param productId
     * @param provinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    Boolean checkCanDelivery(Long productId, String provinceCode, String cityCode, String districtCode);
}