package com.hengtiansoft.item.interfaces;

import com.hengtiansoft.item.entity.dto.ItemGiftDTO;
import com.hengtiansoft.item.entity.dto.ItemGiftSaveDTO;
import com.hengtiansoft.item.entity.dto.ItemGiftSearchDTO;
import com.hengtiansoft.item.entity.vo.ItemGiftVO;

import java.util.List;

public interface GiftManager {

    List<ItemGiftDTO> search(ItemGiftSearchDTO dto);

    void save(ItemGiftSaveDTO dto);

    void delete(ItemGiftDTO dto);

    ItemGiftVO detail(Long id);
}
