/*
 * Project Name: item-service
 * File Name: ProductMediaManager.java
 * Class Name: ProductMediaManager
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.interfaces;

import java.util.List;
import java.util.Map;

import com.hengtiansoft.item.entity.dto.ProductMediaDTO;

/**
 * Class Name: ProductMediaManager Description:
 * 
 * <AUTHOR>
 *
 */
public interface ProductMediaManager {

    List<ProductMediaDTO> listByProductId(Long productId);

    Map<Long, List<ProductMediaDTO>> listByProductIds(List<Long> productIds);

    int saveByProdcut(List<ProductMediaDTO> productMedias, Long productId);

    int deleteByProduct(Long productId);

    int save(Long productId, List<ProductMediaDTO> productMedias);

}
