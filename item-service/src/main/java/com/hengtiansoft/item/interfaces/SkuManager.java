/*
 * Project Name: item-service
 * File Name: SkuManager.java
 * Class Name: SkuManager
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.interfaces;

import com.github.pagehelper.Page;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.Sku;
import com.hengtiansoft.item.entity.vo.ProductDiscountVO;

import java.util.List;
import java.util.Map;

/**
 * Class Name: SkuManager Description:
 * 
 * <AUTHOR>
 *
 */
public interface SkuManager {

    SkuBaseDTO detail(Long id);

    List<SkuBaseDTO> details(List<Long> ids);

    List<SkuBaseDTO> detailsNoDel(List<Long> ids);

    List<SkuBaseDTO> detailsBySkuCodes(List<String> skuCodes);

    List<SkuBaseDTO> getAllSkuByProdcut(Long productId);

    List<SkuBaseDTO> getAllSkuByProdcut(Long productId, boolean skuSpecValueFlag);

    SkuBaseDTO getSpecificSku( Long skuId, boolean skuSpecValueFlag);

    int save(SkuBaseDTO dto);

    int updateSku(SkuBaseDTO dto);

    int updateSkuAll(SkuBaseDTO dto);

    int save(Long productId, List<SkuBaseDTO> skus);

    PageVO<SkuBaseDTO> search(SkuBaseSearchDTO dto);

    List<SkuProductBaseDTO> skuProductList(List<Long> ids);

    List<SkuProductBaseDTO> skuProductNoDelList(List<Long> ids);

    List<SkuProductBaseDTO> skuProductWithStockList(List<Long> ids);

    List<SkuProductBaseDTO> skuProductFromRedisWithStock(List<Long> ids);

    /**
     * 
     * Description: 更新Sku属性
     *
     * @param skuId
     * @param attrName
     * @param attrValue
     * @return
     */
    int updateSkuAttr(Long skuId, String attrName, String attrValue);

    /**
     * 
     * Description: 根据productIds查询SkuMap
     *
     * @param productIds
     * @return
     */
    Map<Long, List<SkuBaseDTO>> getSkuMapByProductIds(List<Long> productIds);

    /**
     * Description: 根据productIds查询SkuMap
     *
     * @param prodcutIds
     * @param skuSpecValueFlag
     */
    Map<Long, List<SkuBaseDTO>> getAllSkuMapByProdcutIds(List<Long> prodcutIds, boolean skuSpecValueFlag);

    int deleteByProductId(Long productId);

    SkuBaseDTO detail(Long id, boolean attrsFlag);

    PageVO<ProductDiscountVO> findAll(ProductBaseSearchDTO dto);

    List<Sku> selectByIds(List<Long> ids);

    Sku selectById(Long id);

    List<SkuSelectedDTO> getNormalSelected(SkuSelectedQryDTO dto);
}
