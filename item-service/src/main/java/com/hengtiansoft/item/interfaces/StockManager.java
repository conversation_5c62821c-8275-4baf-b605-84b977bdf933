package com.hengtiansoft.item.interfaces;

import java.util.List;

import com.hengtiansoft.item.entity.dto.SkuStockDTO;
import com.hengtiansoft.item.entity.dto.StockCalculateDTO;
import com.hengtiansoft.item.entity.dto.StockUpdateDTO;
import com.hengtiansoft.item.entity.po.SkuStock;
import com.hengtiansoft.item.entity.vo.ItemSkuStockVO;
import com.hengtiansoft.item.entity.vo.SkuSaleStockVO;

/**
 * 库存服务
 *
 * <AUTHOR> Lin
 */
public interface StockManager {

    /**
     * 查询库存详情
     *
     * @param skuIds skuId 数组
     */
    List<SkuStock> find(List<Long> skuIds);

    /**
     * 库存扣减校验接口
     *
     * @param stockCheckDTOS 库存扣减校验
     * @return 库存扣减失败skuId 数组
     */
    List<Long> checkStock(List<StockCalculateDTO> stockCheckDTOS);

    /**
     * 库存扣减
     *
     * @param stockCalculates 库存计算对象数组
     * @return 是否扣减成功
     */
    boolean decrease(List<StockCalculateDTO> stockCalculates);

    /**
     * 查询可售库存详情
     *
     * @param skuIds skuId 数组
     * @return 可售库存
     */
    List<SkuSaleStockVO> findSaleStock(List<Long> skuIds);

    /**
     * 新增库存单元
     *
     * @param skuStockDTOS sku库存单元
     * @return 是否保存成功
     */
    boolean save(List<SkuStockDTO> skuStockDTOS);

    /**
     * 预占库存
     *
     * @param stockCalculates 库存计算对象数组
     * @return 是否预占成功
     */
    boolean preempted(List<StockCalculateDTO> stockCalculates);

    /**
     * 释放库存
     *
     * @param stockCalculates 库存计算对象数组
     * @return 是否释放成功
     */
    boolean release(List<StockCalculateDTO> stockCalculates);

    /**
     * 补充库存
     *
     * @param stockCalculates 库存计算对象数组
     * @return 是否补充成功
     */
    boolean increase(List<StockCalculateDTO> stockCalculates);

    /**
     * 更新库存
     *
     * @param stockUpdateDTOS 库存计算对象数组
     * @return 是否更新成功
     */
    boolean update(List<StockUpdateDTO> stockUpdateDTOS);

    /**
     * 查询库存信息通过商品ID列表
     *
     * @param productIds 商品ID列表
     * @return 库存展示对象
     */
    List<ItemSkuStockVO> findByProductIds(List<Long> productIds);

    /**
     * 删除库存单元
     *
     * @param productId 商品ID
     */

    void deleteStockByProductId(Long productId);


    /**
     * @param skuIds skuId 数组
     */
    void deleteStockBySkuId(List<Long> skuIds);

    /**
     * 判断商品库存和安全库存
     *
     * @param productId 商品ID
     * @return 数量
     */
    int checkStock(Long productId);
    
    /**
     * 查询可售库存详情
     *
     * @param skuIds skuId 数组
     * @return 可售库存
     */
    List<SkuSaleStockVO> findSaleStockByProductId(Long prodcutId);

    SkuStock findSaleStockBySkuId(Long skuId);
}
