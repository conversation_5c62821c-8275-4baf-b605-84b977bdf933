/*
 * Project Name: item-service
 * File Name: ProductSpecManager.java
 * Class Name: ProductSpecManager
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.interfaces;

import java.util.List;

/**
 * Class Name: ProductSpecManager Description:
 * 
 * <AUTHOR>
 *
 */
public interface ProductSpecManager {

    int deleteByProduct(Long productId);

    int addByProduct(Long productId, List<Long> specIds);

}
