/*
 * Project Name: item-service
 * File Name: SpecManager.java
 * Class Name: SpecManager
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.interfaces;

import java.util.List;

import com.hengtiansoft.item.entity.dto.SpecDTO;

/**
 * Class Name: SpecManager Description:
 * 
 * <AUTHOR>
 *
 */
public interface SpecManager {

    List<SpecDTO> getAllSpecByProdcutId(Long productId);

    int save(SpecDTO specDTO);

    int save(Long productId, List<SpecDTO> specs);

    int deleteByProductId(Long productId);

}
