/*
 * Project Name: item
 * File Name: ProductManager.java
 * Class Name: ProductManager
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.item.interfaces;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.enumeration.ProductSaleStatusEnum;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Class Name: ProductManager Description:
 * 
 * <AUTHOR>
 *
 */
public interface ProductManager {

    ProductBaseDTO basicDetail(Long id);

    ProductBaseDTO basicDetailNoSku(Long id);

    ProductBaseDTO save(ProductBaseDTO dto);

    int delete(Long id);

    /**
     * 
     * Description: 查询Product
     *
     * @param dto
     * @return
     */
    PageVO<ProductBaseDTO> search(ProductBaseSearchDTO dto);

    List<ProductBaseDTO> searchList(ProductBaseSearchDTO dto, boolean isCache);

    List<ProductBaseDTO> buildProductFisrtCate(List<ProductBaseDTO> dtoList);

    List<ProductBaseDTO> buildProductFisrtMapCate(List<ProductBaseDTO> dtoList);



    /**
     * 
     * Description: 查询商品列表
     *
     * @param productIds
     * @return
     */
    List<ProductBaseDTO> detailList(List<Long> productIds);

    /**
     * Description: 查询商品列表(完整带有skuattr)
     *
     * @param productIds
     * @param isAll
     * @return
     */
    List<ProductBaseDTO> detailCompleteList(List<Long> productIds, boolean isAll);

    /**
     *
     * Description: 查询商品列表(完整带有skuattr)
     *
     * @param productIds
     * @param productType
     * @param cycleFlag
     * @return
     */
    List<ProductBaseDTO> detailCompleteList(List<Long> productIds, String productType, Integer cycleFlag, Integer enableShow);

    /**
     *
     * Description: 查询商品(简单)列表
     *
     * @param productIds
     * @return
     */
    List<ProductBaseDTO> detailSimpleList(List<Long> productIds);

    /**
     * 
     * Description: 商品基础更新
     *
     * @param dto
     * @return
     */
    int singleUpdate(ProductBaseDTO dto);

    void updatePrivateUrl(String url,Long id);

    /**
     * 
     * Description: 商品基础获取
     *
     * @param id
     * @return
     */
    ProductBaseDTO singleGetOne(Long id);

    /**
     * Description: 自动上下架
     *
     */
    List<Long> productSaleStatusUpdate();

    /**
     * 
     * Description: 查询ProductId
     *
     * @param dto
     * @return
     */
    List<Long> searchProductIds(ProductBaseSearchDTO dto);

    /**
     * 
     * Description: 更新商品状态
     *
     * @param dto
     * @return
     */
    int updateSaleStatus(Long prodcutId, ProductSaleStatusEnum saleStatus);

    void updateSort(Long prodcutId, Integer sort);

    List<ProductHotDTO> hotProductList(List<Long> productIdList, Integer enableShow, Integer cycleFlag);

    List<ProductHotDTO> couponProductList(Date date, List<Long> productIdList);

    List<ProductBaseDTO> findByConditionSort(ProductBaseSearchDTO dto);

    Product getOne(Long id);


    /**
     *
     * Description: 查询在线商品列表(完整带有skuattr)
     *
     * @param productIds
     * @return
     */
    List<ProductBaseDTO> onSaleProductDetailList(List<Long> productIds);

    List<Product> findByIds(List<Long> productIds);

    List<Product> findNoDelByIds(List<Long> productIds);

    /**
     * 封装周期信息
     * @param spuId
     */
    ProductCycleDTO buildProductCycle(Long spuId);

    Product findById(Long id);

    List<Product> selectByConditionSort(ProductBaseSearchDTO dto);

    List<Product> selectTopKByCateIds(Long cateId, List<Long> cateIds);

    Map<Long, Integer> countByFirstCateIds(List<Long> firstCateIds);

    ProductCycleDTO buildProductCycle(Long id, List<ProductAttrDTO> productAttrList);
}
