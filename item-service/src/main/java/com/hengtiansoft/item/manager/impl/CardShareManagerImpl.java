package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.item.dao.CardShareDao;
import com.hengtiansoft.item.entity.dto.CardShareDTO;
import com.hengtiansoft.item.entity.po.CardShare;
import com.hengtiansoft.item.manager.CardShareManager;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 送卡manager
 *
 * <AUTHOR>
 * @since 08.09.2020
 */
@Repository
public class CardShareManagerImpl implements CardShareManager {

    @Resource
    private CardShareDao cardShareDao;

    @Override
    public void insert(CardShareDTO cardShareDTO) {
        CardShare cardShare = BeanUtils.copy(cardShareDTO, CardShare::new);
        cardShareDao.insert(cardShare);
    }

    @Override
    public void batchInsert(List<CardShareDTO> cardShareList) {
        List<CardShare> cardShareInsertList = BeanUtils.copyList(cardShareList, CardShare::new);
        cardShareDao.batchInsert(cardShareInsertList);
    }

    @Override
    public void delete(Long id) {
        cardShareDao.delete(id);
    }

    @Override
    public List<CardShare> findByNumber(String shareNumber) {
        return cardShareDao.findByNumber(shareNumber);
    }

    @Override
    public int updateOne(CardShareDTO cardShareDTO) {
        CardShare cardShare = BeanUtils.copy(cardShareDTO, CardShare::new);
        return cardShareDao.update(cardShare);
    }

    @Override
    public int receiveCard(CardShareDTO cardShareDTO) {
        CardShare cardShare = BeanUtils.copy(cardShareDTO, CardShare::new);
        return cardShareDao.receiveCard(cardShare);
    }

    @Override
    public List<CardShare> findByCondition(CardShareDTO cardShareDTO) {
        CardShare cardShare = BeanUtils.copy(cardShareDTO, CardShare::new);

        return cardShareDao.findByCondition(cardShare);
    }


    @Override
    public List<CardShare> findByCardIds(List<Long> cardIds) {
        return cardShareDao.findByCardIds(cardIds);
    }

    @Override
    public void cancelCardShare(List<Long> cardShareIds, CardShare cardShare) {
        cardShareDao.updateByPrimaryKey(cardShareIds, cardShare);
    }

    @Override
    public List<CardShare> selectNewestShare(CardShareDTO cardShareDTO) {
        return cardShareDao.selectNewestShare(cardShareDTO);
    }
}
