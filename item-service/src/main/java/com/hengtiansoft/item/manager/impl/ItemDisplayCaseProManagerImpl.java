package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.item.dao.DisplayCaseDao;
import com.hengtiansoft.item.dao.DisplayCaseProDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSearchDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProUpdateDTO;
import com.hengtiansoft.item.entity.dto.HomePageProSearchDTO;
import com.hengtiansoft.item.entity.dto.HomePageSaveDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.dto.SearchProductByDisplayDTO;
import com.hengtiansoft.item.entity.po.DisplayCase;
import com.hengtiansoft.item.entity.po.DisplayCaseProduct;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.vo.HomePageProListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseProListVO;
import com.hengtiansoft.item.entity.vo.SecondProListVO;
import com.hengtiansoft.item.enumeration.HomePageDisplayEnum;
import com.hengtiansoft.item.enumeration.InvalidEnum;
import com.hengtiansoft.item.enumeration.ProductSaleStatusEnum;
import com.hengtiansoft.item.manager.ItemDisplayCaseProManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description: 运营分类商品分类Manager实现类
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
@Component
public class ItemDisplayCaseProManagerImpl implements ItemDisplayCaseProManager {

    @Autowired
    private DisplayCaseProDao displayCaseProDao;

    @Autowired
    private DisplayCaseDao displayCaseDao;

    @Autowired
    private ProductDao productDao;

    private static final int NEW_LIMIT = 10;

    private static final int HOT_LIMIT = 5;

    private static final int ZERO = 0;

    private static final int ONE = 1;

    @Override
    public void add(DisplayCaseProSaveDTO dto) {
        List<Long> productIds = new ArrayList<>(dto.getProductIds());
        for (int i = 0; i < productIds.size(); i++) {
            DisplayCaseProduct displayCaseProduct = new DisplayCaseProduct();
            displayCaseProduct.setProductId(productIds.get(i));
            displayCaseProduct.setSort(i + 1);
            displayCaseProduct.setDisplayCaseId(dto.getDisplayCaseId());
            displayCaseProDao.insert(displayCaseProduct);
        }
    }

    @Override
    public void delete(Long displayCaseId) {
        displayCaseProDao.deleteById(displayCaseId);
    }

    @Override
    public List<ItemDisplayCaseProListVO> list(DisplayCaseProSearchDTO dto) {
        DisplayCase displayCase = displayCaseDao.findById(dto.getDisplayCaseId());
        List<Product> productList = new ArrayList<>();
        List<ItemDisplayCaseProListVO> listVOS = new ArrayList<>();
        List<DisplayCaseProduct> caseProducts = displayCaseProDao.findByDisplayCaseId(dto.getDisplayCaseId());
        ProductBaseSearchDTO seatchDTO = new ProductBaseSearchDTO();
        seatchDTO.setPageSize(Integer.MAX_VALUE);
        List<Integer> saleStatus = new ArrayList<>();
        saleStatus.add(ProductSaleStatusEnum.ON_SHELVES.getCode());
        Map<Long, Integer> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(caseProducts)) {
            List<Long> productIds = caseProducts.stream().map(DisplayCaseProduct::getProductId)
                    .collect(Collectors.toList());
            caseProducts.stream().forEach(data -> {
                map.put(data.getProductId(), data.getSort());
            });
            // 调用商品列表接口查商品
            seatchDTO.setProductIds(productIds);
        }
        List<Product> productLists = productDao.findByCondition(seatchDTO);
        List<Product> products = new ArrayList<>();
        if (displayCase.getType() != null && displayCase.getType() == HomePageDisplayEnum.NEW.getCode()) {
            // 默认显示的新品商品
            products = productLists.stream()
                    .filter(k -> ProductSaleStatusEnum.ON_SHELVES.getCode().equals(k.getSaleStatus()))
                    .sorted(Comparator.comparing(Product::getOnShelvesTime).reversed()).limit(NEW_LIMIT)
                    .collect(Collectors.toList());
        } else {
            products = productLists.stream()
                    .filter(n -> Objects.nonNull(n.getSaleQty())
                            && ProductSaleStatusEnum.ON_SHELVES.getCode().equals(n.getSaleStatus()))
                    .sorted(Comparator.comparing(Product::getOnShelvesTime).reversed()).limit(HOT_LIMIT)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(caseProducts)) {
            return new ArrayList<>();
        } else if (!CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(caseProducts)) {
            productList.addAll(products);
        } else {
            productList.addAll(productLists);
        }
        productList.stream().forEach(n -> {
            ItemDisplayCaseProListVO itemDisplayCaseProListVO = new ItemDisplayCaseProListVO();
            BeanUtils.copy(n, itemDisplayCaseProListVO);
            itemDisplayCaseProListVO
                    .setSort(Optional.ofNullable(map.get(itemDisplayCaseProListVO.getId())).orElse(ZERO));
            if (ProductSaleStatusEnum.ON_SHELVES.getCode().equals(n.getSaleStatus())) {
                itemDisplayCaseProListVO.setIsInvalid(InvalidEnum.NOT.getCode());
            } else {
                itemDisplayCaseProListVO.setIsInvalid(InvalidEnum.IS.getCode());
            }
            listVOS.add(itemDisplayCaseProListVO);
        });
        return listVOS;
    }

    @Override
    public List<SecondProListVO> secondList(HomePageProSearchDTO dto) {
        List<SecondProListVO> list = new ArrayList<>();
        // 找到一级分类下的所有的二级分类id
        List<Long> secondIds = displayCaseDao.findSecondIdsById(dto.getDisplayCaseId());
        if (secondIds.isEmpty()) {
            return new ArrayList<>();
        }
        // 根据二级分类id查所有的productIds
        List<DisplayCaseProduct> bySecondIds = displayCaseProDao.findProIdsBySecondIds(secondIds);
        if (bySecondIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> productIds = bySecondIds.stream().map(DisplayCaseProduct::getProductId).collect(Collectors.toList());
        // 调商品列表接口查商品
        ProductBaseSearchDTO seatchDTO = new ProductBaseSearchDTO();
        seatchDTO.setProductIds(productIds);
        List<Product> productList = productDao.findByCondition(seatchDTO);
        productList.stream().forEach(data -> {
            SecondProListVO proListVO = BeanUtils.copy(data, SecondProListVO::new);
            if (ProductSaleStatusEnum.ON_SHELVES.getCode().equals(data.getSaleStatus())) {
                proListVO.setIsInvalid(InvalidEnum.NOT.getCode());
            } else {
                proListVO.setIsInvalid(InvalidEnum.IS.getCode());
            }
            list.add(proListVO);
        });
        return list;
    }

    @Override
    public void addHomePage(HomePageSaveDTO dto) {
        // 清空之前的数据
        displayCaseProDao.delete(dto.getDisplayCaseId());
        List<Long> productIds = new ArrayList<>(dto.getProductIds());
        for (int i = 0; i < productIds.size(); i++) {
            DisplayCaseProduct displayCaseProduct = new DisplayCaseProduct();
            displayCaseProduct.setProductId(productIds.get(i));
            displayCaseProduct.setSort(i + 1);
            displayCaseProduct.setDisplayCaseId(dto.getDisplayCaseId());
            displayCaseProDao.insert(displayCaseProduct);
        }
    }

    @Override
    public List<HomePageProListVO> homePageList(HomePageProSearchDTO dto) {
        List<HomePageProListVO> list = new ArrayList<>();
        List<DisplayCaseProduct> proIdsByDisplayCaseId = displayCaseProDao
                .findProIdsByDisplayCaseId(dto.getDisplayCaseId());
        List<Long> productIds = proIdsByDisplayCaseId.stream().map(DisplayCaseProduct::getProductId)
                .collect(Collectors.toList());
        if (productIds.isEmpty()) {
            return new ArrayList<>();
        }
        // 调用商品列表接口
        ProductBaseSearchDTO seatchDTO = new ProductBaseSearchDTO();
        seatchDTO.setProductIds(productIds);
        List<Product> productList = productDao.findByCondition(seatchDTO);
        productList.stream().forEach(data -> {
            HomePageProListVO homePageProListVO = BeanUtils.copy(data, HomePageProListVO::new);
            if (ProductSaleStatusEnum.ON_SHELVES.getCode().equals(data.getSaleStatus())) {
                homePageProListVO.setIsInvalid(InvalidEnum.NOT.getCode());
            } else {
                homePageProListVO.setIsInvalid(InvalidEnum.IS.getCode());
            }
            list.add(homePageProListVO);
        });
        return list;
    }

    @Override
    public Map<Long, Integer> selectAll() {
        Map<Long, Integer> rs = new HashMap<>();
        List<DisplayCaseProduct> displayCaseProducts = displayCaseProDao.selectAll();
        displayCaseProducts.stream().forEach(pro -> {
            int old = rs.getOrDefault(pro.getDisplayCaseId(), ZERO);
            rs.put(pro.getDisplayCaseId(), old + ONE);
        });
        return rs;
    }

    @Override
    public void update(DisplayCaseProUpdateDTO dto) {
        DisplayCaseProduct displayCaseProduct = BeanUtils.deepCopy(dto, DisplayCaseProduct.class);
        displayCaseProDao.update(displayCaseProduct);
    }

    @Override
    public List<DisplayCaseProduct> selectByDisplayCaseId(Long id) {
        List<DisplayCaseProduct> displayCasePro = displayCaseProDao.findProIdsByDisplayCaseId(id);
        return displayCasePro;
    }

    @Override
    public List<Long> selectByDisplayCaseIds(List<Long> displayIds) {
        List<DisplayCaseProduct> byDisplayCaseIds = displayCaseProDao.findByDisplayCaseIds(displayIds);
        List<Long> productIds = byDisplayCaseIds.stream().map(DisplayCaseProduct::getProductId)
                .collect(Collectors.toList());
        return productIds;
    }

    @Override
    public List<DisplayCaseProduct> selectProByDisplayCaseId(List<Long> displayIds) {
        return displayCaseProDao.findProByDisplayCaseId(displayIds);
    }

    @Override
    public void deleteByExample(Long displayCaseId, Long productId) {
        displayCaseProDao.deleteByExample(displayCaseId, productId);
    }

    @Override
    public PageVO<ItemDisplayCaseProListVO> listByWX(DisplayCaseProSearchDTO dto, Integer pageNum, Integer pageSize) {
        List<DisplayCaseProduct> caseProducts = displayCaseProDao.findByDisplayCaseId(dto.getDisplayCaseId());
        ProductBaseSearchDTO seatchDTO = new ProductBaseSearchDTO();
        seatchDTO.setPageSize(Integer.MAX_VALUE);
        List<Integer> saleStatus = new ArrayList<>();
        saleStatus.add(ProductSaleStatusEnum.ON_SHELVES.getCode());
        Map<Long, Integer> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(caseProducts)) {
            List<Long> productIds = caseProducts.stream().map(DisplayCaseProduct::getProductId)
                    .collect(Collectors.toList());
            caseProducts.stream().forEach(data -> {
                map.put(data.getProductId(), data.getSort());
            });
            // 调用商品列表接口查商品
            if (pageNum != null && pageSize != null) {
                seatchDTO.setPageNum(pageNum);
                seatchDTO.setPageSize(pageSize);
            }
            seatchDTO.setProductIds(productIds);
            seatchDTO.setSaleStatus(saleStatus);

        }
        List<Product> productLists = productDao.findByCondition(seatchDTO);
        return PageUtils.convert(productLists, n -> {
            ItemDisplayCaseProListVO itemDisplayCaseProListVO = new ItemDisplayCaseProListVO();
            BeanUtils.copy(n, itemDisplayCaseProListVO);
            itemDisplayCaseProListVO
                    .setSort(Optional.ofNullable(map.get(itemDisplayCaseProListVO.getId())).orElse(ZERO));
            if (ProductSaleStatusEnum.ON_SHELVES.getCode().equals(n.getSaleStatus())) {
                itemDisplayCaseProListVO.setIsInvalid(InvalidEnum.NOT.getCode());
            } else {
                itemDisplayCaseProListVO.setIsInvalid(InvalidEnum.IS.getCode());
            }
            return itemDisplayCaseProListVO;
        });
    }

    @Override
    public List<Long> searchProducts(SearchProductByDisplayDTO dto) {
        return displayCaseProDao.searchProducts(dto);
    }

    @Override
    public List<Long> searchProductsById(Long id) {
        return displayCaseProDao.searchProductsById(id);
    }
}
