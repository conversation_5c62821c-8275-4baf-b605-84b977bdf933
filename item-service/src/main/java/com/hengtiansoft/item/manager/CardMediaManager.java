package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.CardMediaDTO;
import com.hengtiansoft.item.entity.vo.CardMediaVO;
import com.hengtiansoft.item.enumeration.CardTypeEnum;

/**
 * <AUTHOR>
 * @description 奶卡信息
 **/
public interface CardMediaManager {

    /**
     * 获取奶卡相应内容
     * @param cardNum 奶卡号
     * @param cardTypeEnum 奶卡类型
     * @return
     */
     CardMediaVO findByCardNumAndCardType(String cardNum, CardTypeEnum cardTypeEnum);

    /**
     * 保存奶卡
     * @param cardMediaDTO 奶卡内容信息
     * @return
     */
    int saveOne(CardMediaDTO cardMediaDTO);

}
