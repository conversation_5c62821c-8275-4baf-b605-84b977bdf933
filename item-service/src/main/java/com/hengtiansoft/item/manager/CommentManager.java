package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.CommentDTO;
import com.hengtiansoft.item.entity.dto.CommentSpuDTO;
import com.hengtiansoft.item.entity.po.Comment;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommentManager {
    void insert(Comment comment);

    List<Comment> getList(CommentDTO dto);

    void batchUpdate(List<Comment> list);

    void delete(Comment po);

    Comment get(Long id);

    List<Comment> getSpuList(CommentSpuDTO dto);

    void insertList(List<Comment> comments4Insert);

    void deleteByOrder(String orderNo, Long userId);
}
