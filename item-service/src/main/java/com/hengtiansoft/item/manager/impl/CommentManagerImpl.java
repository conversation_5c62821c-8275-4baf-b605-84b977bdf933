package com.hengtiansoft.item.manager.impl;

import cn.hutool.core.lang.Assert;
import com.hengtiansoft.item.dao.CommentDao;
import com.hengtiansoft.item.dao.CommentTagJoinDao;
import com.hengtiansoft.item.entity.dto.CommentDTO;
import com.hengtiansoft.item.entity.dto.CommentSpuDTO;
import com.hengtiansoft.item.entity.po.Comment;
import com.hengtiansoft.item.manager.CommentManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommentManagerImpl implements CommentManager {

    @Resource
    private CommentDao commentDao;
    @Resource
    private CommentTagJoinDao commentTagJoinDao;

    @Override
    public void insert(Comment comment) {
        commentDao.insert(comment);
    }

    @Override
    public List<Comment> getList(CommentDTO dto) {
        return commentDao.findByCondition(dto);
    }

    @Override
    public void batchUpdate(List<Comment> list) {
        commentDao.batchUpdate(list);
    }

    @Override
    public void delete(Comment po) {
        commentDao.updateById(po);
    }

    @Override
    public Comment get(Long id) {
        Assert.notNull(id, "id参数不能为空");
        return commentDao.findById(id);
    }

    @Override
    public List<Comment> getSpuList(CommentSpuDTO dto) {
        return commentDao.getSpuList(dto);
    }

    @Override
    public void insertList(List<Comment> list) {
        commentDao.insertList(list);
    }

    @Override
    public void deleteByOrder(String orderNo, Long userId) {
        commentDao.deleteByOrder(orderNo, userId);
    }
}
