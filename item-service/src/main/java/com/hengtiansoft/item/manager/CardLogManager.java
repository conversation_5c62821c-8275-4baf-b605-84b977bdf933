/**
 * @(#)CardLogManager.java, 2021/1/13.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.po.CardLog;

import java.util.List;

/**
 * 奶卡操作日志manager
 *
 * <AUTHOR>
 * @date 2021/1/13 17:10
 */
public interface CardLogManager {

    /**
     * 插入日志
     *
     * @param cardLog
     * @return
     */
    Long insert(CardLog cardLog);

    /**
     * 查询日志
     *
     * @param cardNumber
     * @return
     */
    List<CardLog> selectByCardNumber(String cardNumber);

    Integer batchInsert(List<CardLog> cardLogs);
}
