package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.CardCategoryDTO;
import com.hengtiansoft.item.entity.dto.CardCategoryListDTO;
import com.hengtiansoft.item.entity.po.CardCategory;

import java.util.List;

/**
 * 奶卡类别manager
 *
 * <AUTHOR>
 * @date 2020/9/2 15:54
 */
public interface CardCategoryManager {

    CardCategory selectById(Long id);

    /**
     * 根据类别名称批量查询
     *
     * @param names
     * @return
     */
    List<CardCategory> selectByNames(List<String> names);

    List<Long> selectByCategoryCount(Integer categoryCount);

    List<Long> findByECardSpecialSql (List<Integer> categoryCount);

    List<CardCategory> selectCardCategoryByCategoryCount(Integer categoryCount);
    /**
     * 获取所有类别名称
     *
     * @return
     */
    List<CardCategory> getAll();

    /**
     * 新建/编辑
     *
     * @param dto
     * @return
     */
    Long save(CardCategoryDTO dto);



    /**
     * 列表查询
     *
     * @param dto
     * @return
     */
    List<CardCategory> findByCondition(CardCategoryListDTO dto);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    CardCategory selectByPrimaryKey(Long categoryId);
}
