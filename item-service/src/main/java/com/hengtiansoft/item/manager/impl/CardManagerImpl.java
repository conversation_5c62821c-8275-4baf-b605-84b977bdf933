package com.hengtiansoft.item.manager.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.hengtiansoft.common.constant.CardConstant;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.CardParentNoVO;
import com.hengtiansoft.common.excel.ExcelImportHelper;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CardCategoryDao;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.dao.CardSerialDao;
import com.hengtiansoft.item.entity.dto.CardBatchDTO;
import com.hengtiansoft.item.entity.dto.CardBatchUpdateDTO;
import com.hengtiansoft.item.entity.dto.CardBindDTO;
import com.hengtiansoft.item.entity.dto.CardCountCategoryDTO;
import com.hengtiansoft.item.entity.dto.CardDTO;
import com.hengtiansoft.item.entity.dto.CardListDTO;
import com.hengtiansoft.item.entity.dto.CardPwdListDTO;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.po.CardCategory;
import com.hengtiansoft.item.entity.po.CardSerial;
import com.hengtiansoft.item.entity.vo.*;
import com.hengtiansoft.item.enumeration.CardLockStatusEnum;
import com.hengtiansoft.item.enumeration.CardStatusEnum;
import com.hengtiansoft.item.enumeration.CardTypeEnum;
import com.hengtiansoft.item.enumeration.CardUsageStatusEnum;
import com.hengtiansoft.item.manager.CardManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 奶卡manager
 *
 * <AUTHOR>
 * @date 2020/9/2 15:50
 */
@Slf4j
@Service
public class CardManagerImpl implements CardManager {

    @Autowired
    private CardDao cardDao;

    @Autowired
    private CardCategoryDao cardCategoryDao;

    @Autowired
    private CardSerialDao cardSerialDao;


    /**
     * 判断类别下有无奶卡
     *
     * @param name
     * @return
     */
    @Override
    public Boolean checkRelevance(String name) {
        return cardDao.checkRelevance(name);
    }

    /**
     * 新建/编辑
     *
     * @param dto
     * @return
     */
    @Override
    public Long save(CardDTO dto) {

        // 卡号校验
        Long checkId = cardDao.checkNumber(dto.getCardNumber());
        Long id = dto.getId();
        if (Objects.isNull(id)) {
            CardCategory cardCategory = cardCategoryDao.selectByPrimaryKey(dto.getCategoryId());
            Card card = new Card();
            BeanUtils.copy(dto, card);
            card.setCategoryName(cardCategory.getCategoryName());
            card.setCardCount(cardCategory.getCategoryCount());
            card.setRemainingCount(cardCategory.getCategoryCount());
            card.setCardType(CardTypeEnum.ENTITY_CARD.getCode());
            // 新建
            if (Objects.nonNull(checkId)) {
                throw new BusinessException("卡号已存在，请重试！");
            }
            id = cardDao.insert(card);
        } else {
            Card card = cardDao.selectByPrimaryKey(id);
            BeanUtils.copy(dto, card);
            if (!CardUsageStatusEnum.UNRECEIVED.getCode().equals(card.getUsageStatus()) || !CardStatusEnum.FROZEN.getCode().equals(card.getCardStatus())) {
                throw new BusinessException("只有未领取已冻结的奶卡可以编辑，请确认状态！");
            }
            // 编辑
            if (Objects.nonNull(checkId) && !checkId.equals(id)) {
                throw new BusinessException("卡号已存在，请重试！");
            }
            id = cardDao.update(card);
        }
        return id;
    }

    /**
     * 更新
     *
     * @param card
     * @return
     */
    @Override
    public Long update(Card card) {
        return cardDao.update(card);
    }

    /**
     * 批量更新
     *
     * @return
     */
    @Override
    public void updateByPrimaryKey(List<Long> ids, Card card) {
        cardDao.updateByPrimaryKey(ids, card);
    }

    /**
     * 插入
     *
     * @param card
     * @return
     */
    @Override
    public Long insert(Card card) {
        return cardDao.insert(card);
    }

    /**
     * 根据id查询奶卡
     *
     * @param id
     * @return
     */
    @Override
    public Card selectByPrimaryKey(Long id) {
        return cardDao.selectByPrimaryKey(id);
    }

    /**
     * 查询奶卡列表
     *
     * @param ids
     * @return
     */
    @Override
    public List<Card> selectByPrimaryKeyList(List<Long> ids) {
        return cardDao.selectByPrimaryKeyList(ids);
    }

    /**
     * 根据卡号卡密查询奶卡
     *
     * @param dto
     * @return
     */
    @Override
    public Card findByNumberAndPassword(CardBindDTO dto) {
        Card card = new Card();
        BeanUtils.copy(dto, card);
        return cardDao.findByNumberAndPassword(card);
    }

    /**
     * 批量插入
     *
     * @param cardList
     */
    @Override
    public void batchInsert(List<Card> cardList) {
        cardDao.batchInsert(cardList);
    }

    @Override
    public List<Card> createV2(CardBatchDTO dto) {
        // 后台生成的卡都是实体卡
        Long categoryId = dto.getCategoryId();
        String numberPrefix = dto.getCardNumber();
        String passwordPrefix = dto.getCardPassword();
        int number = dto.getNumber();
        List<Card> inCards = new ArrayList<>();
        // 奶卡类别
        CardCategory cardCategory = cardCategoryDao.selectByPrimaryKey(categoryId);
        // 随机组合卡号卡密
        List<String> numberList = this.generate(numberPrefix, number, new ArrayList<>());
        numberList.forEach(cardNumber -> {
            Card card = new Card();
            String password = this.combine(passwordPrefix, CardConstant.PASSWORD_LENGTH);
            card.setCategoryId(cardCategory.getId());
            card.setCategoryName(cardCategory.getCategoryName());
            card.setCardCount(cardCategory.getCategoryCount());
            card.setRemainingCount(cardCategory.getCategoryCount());
            card.setCardNumber(cardNumber);
            card.setCardPassword(password);
            card.setCardType(dto.getCardType());
            inCards.add(card);
        });
        cardDao.batchInsert(inCards);
        return inCards;
    }

    /**
     * 批量周期购创建
     *
     * @param dto
     */
    @Override
    public List<String> createCycleCard(CardBatchDTO dto,Integer count) {
        // 后台生成的卡都是实体卡
        Long categoryId = dto.getCategoryId();
        String numberPrefix = dto.getCardNumber();
        String passwordPrefix = dto.getCardPassword();
        int number = dto.getNumber();
        List<Card> inCards = new ArrayList<>();
        // 随机组合卡号卡密
        List<String> numberList = this.generate(numberPrefix, number, new ArrayList<>());
        numberList.forEach(cardNumber -> {
            Card card = new Card();
            String password = this.combine(passwordPrefix, CardConstant.PASSWORD_LENGTH);
            card.setCategoryId(categoryId);
            card.setCategoryName("周期购虚拟卡");
            card.setCardCount(count);
            card.setRemainingCount(count);
            card.setCardNumber(cardNumber);
            card.setCardPassword(password);
            card.setCardType(dto.getCardType());
            inCards.add(card);
        });
        cardDao.batchInsert(inCards);
        return StreamUtils.toList(inCards, Card::getCardNumber);
    }

    /**
     * 递归获取不重复的卡号
     *
     * @param numberPrefix 卡号前缀
     * @param number       生成数量
     * @param numberList   原始卡号
     * @return
     */
    private List<String> generate(String numberPrefix, int number, List<String> numberList) {
        if (number == CardConstant.ZERO) {
            return new ArrayList<>();
        }
        int length = CardConstant.NUMBER_LENGTH - numberPrefix.length();
        StringBuilder format = new StringBuilder(StringUtils.EMPTY);
        while (length > 0) {
            format.append(CardConstant.ZERO);
            length--;
        }
        Long serial = null;
        int size = 0;
        for (int i = 0; i < number; i++) {
            String cardNumber;
            do {
                JSONObject object = this.serial(numberPrefix, serial, format.toString());
                cardNumber = object.getString("number");
                serial = object.getLong("serial");
            } while (numberList.contains(cardNumber));
            numberList.add(cardNumber);
            size++;
        }
        CardSerial cardSerial = new CardSerial();
        cardSerial.setNumberPrefix(numberPrefix);
        cardSerial.setSerialNumber(serial);
        cardSerialDao.updateByPrefix(cardSerial);

        List<Card> sameCardList = cardDao.selectByNumberList(numberList);
        size -= sameCardList.size();
        numberList.removeAll(StreamUtils.toList(sameCardList, Card::getCardNumber));
        this.generate(numberPrefix, number - size, numberList);
        return numberList;
    }

    /**
     * 组合卡密
     *
     * @param prefix   前缀
     * @param constant 长度
     * @return
     */
    private String combine(String prefix, int constant) {
        int length = constant - prefix.length();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            Random random = new Random();
            int number = random.nextInt(CardConstant.STRING_DATA.length());
            sb.append(CardConstant.STRING_DATA.charAt(number));
        }
        return prefix + sb.toString();
    }

    /**
     * 组合卡号
     *
     * @param prefix 前缀
     * @param serial 序列号
     * @param format 格式化的序列号string
     * @return
     */
    private JSONObject serial(String prefix, Long serial, String format) {
        CardSerial cardSerial = cardSerialDao.selectByPrefix(prefix);
        if (Objects.isNull(cardSerial)) {
            // 这个前缀第一次生成卡，插入记录，序列号从1开始
            serial = CardConstant.LONG_ZERO;
            cardSerial = new CardSerial();
            cardSerial.setNumberPrefix(prefix);
            cardSerial.setSerialNumber(serial);
            cardSerialDao.insert(cardSerial);
        } else {
            // 已有记录，读取序列号
            if (Objects.isNull(serial)) {
                // 第一次进来，读取数据库中的序列号
                serial = cardSerial.getSerialNumber();
            }
        }
        serial++;
        DecimalFormat decimalFormat = new DecimalFormat(format);
        String number = decimalFormat.format(serial);
        JSONObject object = new JSONObject();
        object.put("number", prefix + number);
        object.put("serial", serial);
        return object;
    }

    /**
     * 删除奶卡
     *
     * @param id
     */
    @Override
    public void delete(Long id) {
        cardDao.delete(id);
    }

    /**
     * 批量更新状态（封装）
     *
     * @param dto
     */
    @Override
    public void batchUpdateStatus(CardBatchUpdateDTO dto) {

        List<Card> cardList = new ArrayList<>();
        Integer cardStatus = dto.getCardStatus();
        Date now = new Date();
        List<Long> idList = dto.getIdList();
        idList.forEach(id -> {
            Card card = new Card();
            card.setId(id);
            card.setCardStatus(cardStatus);
            card.setUpdateTime(now);
            cardList.add(card);
        });
        cardDao.batchUpdateStatus(cardList);
    }

    /**
     * 批量更新状态
     *
     * @param cardList
     */
    @Override
    public void batchUpdateStatus(List<Card> cardList) {
        cardDao.batchUpdateStatus(cardList);
    }

    /**
     * 奶卡列表
     *
     * @param dto
     * @return
     */
    @Override
    public CardPageVO<CardListVO> list(CardListDTO dto) {
        if (!dto.getPageFlag() ||
                (dto.getPageNum().equals(CardConstant.ZERO) && dto.getPageSize().equals(CardConstant.ZERO))) {
            if (cardDao.countByCondition(dto) > CardConstant.MAX_LIST_LENGTH) {
                // 导出时若数据大于5万条直接报错
                throw new BusinessException("导出数据限制5万条");
            }
        }
        if (dto.getPageFlag()) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<Card> cardList = cardDao.findByCondition(dto);

        List<CardCountVO> countVOList = new ArrayList<>();
        if (dto.getUserFlag()) {
            countVOList = cardDao.countByStatus(dto);
        }
        CardPageVO<CardListVO> result = new CardPageVO<>();
        if (dto.getPageFlag()) {
            result.setPagination(PageUtils.extract(cardList));
        }
        result.setList(BeanUtils.deepListCopy(cardList, CardListVO.class));
        result.setCountVOList(countVOList);
        return result;
    }

    @Override
    public List<CardCreateListVO> findCardPwdList(CardPwdListDTO dto) {
        if (cardDao.countCardPwd(dto) > CardConstant.MAX_LIST_LENGTH) {
            throw new BusinessException("导出数据限制5万条");
        }
        return BeanUtils.deepListCopy(cardDao.findCardPwdList(dto), CardCreateListVO.class);
    }

    /**
     * 用户未过期奶卡列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<CardListVO> userList(Long userId) {
        return BeanUtils.deepListCopy(cardDao.findByUserWithEffect(userId), CardListVO.class);
    }

    /**
     * 用户已订完列表（服务完成/已过期）
     *
     * @param userId
     * @return
     */
    @Override
    public List<CardListVO> finishedList(Long userId) {
        return BeanUtils.deepListCopy(cardDao.findByUserWithFinished(userId), CardListVO.class);
    }

    /**
     * 导入
     *
     * @param file
     * @return
     */
    @Override
    public List<CardVO> importCards(MultipartFile file) {
        List<CardVO> cardVOList;
        try (InputStream is = file.getInputStream()) {
            cardVOList = ExcelImportHelper.loadExcelDataAtSheet0Fully(
                    ExcelImportHelper.getExcelTypeByName(file.getOriginalFilename()), is, CardVO.class);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("导入的文件格式不正确");
        } catch (Exception e) {
            throw new BusinessException("导入有误");
        }
        if (CollectionUtils.isEmpty(cardVOList)) {
            throw new BusinessException("导入数据为空");
        }
        List<CardVO> filterVOList = cardVOList.stream().filter(cardVO -> {
            if (Objects.isNull(cardVO.getCategoryName()) && Objects.isNull(cardVO.getCardNumber()) && Objects.isNull(cardVO.getCardPassword())) {
                return false;
            }
            if (StringUtils.isBlank(cardVO.getCardNumber())) {
                throw new BusinessException((cardVOList.indexOf(cardVO) + 2) + "行数据奶卡卡号不能为空");
            }
            if (StringUtils.isBlank(cardVO.getCardPassword())) {
                throw new BusinessException((cardVOList.indexOf(cardVO) + 2) + "行数据奶卡卡密不能为空");
            }
            return true;
        }).collect(Collectors.toList());
        List<CardCategory> categoryList = cardCategoryDao.getAll();
        List<String> categoryNameList = StreamUtils.toList(categoryList, CardCategory::getCategoryName);
        List<Card> cardList = cardDao.findByNumbers(StreamUtils.convertDistinct(filterVOList, CardVO::getCardNumber, String::compareTo));
        List<String> cardNumberList = StreamUtils.toList(cardList, Card::getCardNumber);
        List<String> excelNumberList = new ArrayList<>();
        for (int i = 0; i < cardVOList.size(); i++) {
            if (Objects.isNull(cardVOList.get(i).getCategoryName()) && Objects.isNull(cardVOList.get(i).getCardNumber()) && Objects.isNull(cardVOList.get(i).getCardPassword())) {
                continue;
            }
            if (StringUtils.isBlank(cardVOList.get(i).getCategoryName())) {
                throw new BusinessException((i + 2) + "行数据奶卡类别不能为空");
            }
            if (!categoryNameList.contains(cardVOList.get(i).getCategoryName())) {
                throw new BusinessException((i + 2) + "行数据奶卡类别" + cardVOList.get(i).getCategoryName() + "不存在");
            }
            if (cardVOList.get(i).getCardNumber().length() != CardConstant.NUMBER_LENGTH) {
                throw new BusinessException((i + 2) + "行数据卡号位数不符，必须为" + CardConstant.NUMBER_LENGTH + "位");
            }
            if (cardVOList.get(i).getCardPassword().length() != CardConstant.PASSWORD_LENGTH) {
                throw new BusinessException((i + 2) + "行数据卡密位数不符，必须为" + CardConstant.PASSWORD_LENGTH + "位");
            }
            if (cardNumberList.contains(cardVOList.get(i).getCardNumber())) {
                throw new BusinessException((i + 2) + "行数据卡号已存在");
            }
            if (excelNumberList.contains(cardVOList.get(i).getCardNumber())) {
                throw new BusinessException((i + 2) + "行数据卡号重复");
            } else {
                excelNumberList.add(cardVOList.get(i).getCardNumber());
            }
            if (!cardVOList.get(i).getCardNumber().matches(CardConstant.REGEX)) {
                throw new BusinessException((i + 2) + "行数据卡号格式不正确");
            }
            if (!cardVOList.get(i).getCardPassword().matches(CardConstant.REGEX)) {
                throw new BusinessException((i + 2) + "行数据卡密格式不正确");
            }
        }
        return filterVOList;
    }

    @Override
    public Card selectByNumber(String number) {
        return cardDao.selectByNumber(number);
    }

    @Override
    public List<Card> selectByNumberList(List<String> numberList) {
        return cardDao.selectByNumberList(numberList);
    }

    @Override
    public BigDecimal countActivateCards(boolean dateFlag) {
        return cardDao.countActivateCards(dateFlag);
    }

    @Override
    public List<CardRemainingCountVO> countRemainingCount() {
        return cardDao.countRemainingCount();
    }

    @Override
    public int countRemainingCountSum() {
        return cardDao.countRemainingCountSum();
    }

    @Override
    public List<CardRemainingCountVO> countRemainingByDate(String date) {
        return cardDao.countRemainingByDate(date);
    }

    @Override
    public void noRemainingHis(String date, ExcelWriter excelWriter) {
        List<CardRemainingCountVO> selectList = new ArrayList<>();
        WriteSheet writeSheet = EasyExcel.writerSheet(date).head(CardListExportVO.class).build();
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            PageHelper.startPage(pageNum, pageSize);
            List<CardRemainingCountVO> remainingCount = this.countRemainingByDate(date);
            Pagination pagination = PageUtils.extract(remainingCount);
            pages = pagination.getPages();
            pageNum++;
            selectList.addAll(remainingCount);

            if (CollectionUtils.isNotEmpty(selectList)) {
                List<Card> cardList = this.findInHome(StreamUtils.toList(selectList, CardRemainingCountVO::getUserId),date);
                List<CardListExportVO> cardListVOS = BeanUtils.copyList(cardList, CardListExportVO::new);
                for (CardListExportVO exportVO: cardListVOS) {
                    exportVO.setCardStatusDesc(CardStatusEnum.getDescByCode(exportVO.getCardStatus()));
                    exportVO.setUsageStatusDesc(CardUsageStatusEnum.getDescByCode(exportVO.getUsageStatus()));
                    exportVO.setExpirationTimeString(DateUtil.dateToString(exportVO.getExpirationTime(),DateUtil.SIMPLE_FMT));
                    exportVO.setReceiveTimeString(DateUtil.dateToString(exportVO.getReceiveTime(),DateUtil.SIMPLE_FMT));
                }
                excelWriter.write(cardListVOS, writeSheet);
            }
        } while (pageNum <= pages);
    }

    @Override
    public List<Card> findExpiration(Integer days, List<Long> userIdList) {
        return cardDao.findExpiration(days, userIdList);
    }

    @Override
    public BigDecimal countUsageStatus(Integer usageStatus) {
        return cardDao.countUsageStatus(usageStatus);
    }

    @Override
    public List<CardCountCategoryVO> countCategory(CardCountCategoryDTO dto) {
        return cardDao.countCategory(dto);
    }

    @Override
    public List<Card> findInHome(List<Long> userId,String date) {
        return cardDao.findInHome(userId,date);
    }

    private void matchDay(String date) {
        List<String> tableNames = cardDao.showAllTables();
        List<String> result = new ArrayList<>();
        tableNames.forEach(name -> {
            String[] strings = StringUtils.split(name, "_");
            result.add(strings[strings.length - 1]);
        });
        if (!result.contains(date)) {
            throw new BusinessException("无该日期的静态数据！");
        }
    }
    @Override
    public Card activateAndCheckCard(String cardNumber) {
        if(StringUtils.isBlank(cardNumber)){
            throw new BusinessException("卡号缺失!");
        }
        Card card = cardDao.selectByNumber(cardNumber);
        if (null == card) {
            throw new BusinessException("卡号【" + cardNumber +  "】不存在");
        }
        if(card.getLockStatus().equals(CardLockStatusEnum.YES.getCode())){
            throw new BusinessException("奶卡已锁定，不能激活");
        }
        if (null == card.getUserId() || card.getUserId() <= 0) {
            throw new BusinessException("奶卡未绑定");
        }
        if (card.getCardStatus().equals(CardStatusEnum.SPENDABLE.getCode())
                && card.getUsageStatus().equals(CardUsageStatusEnum.UNACTIVATED.getCode())) {
            Card cardRecord = new Card();
            cardRecord.setCardStatus(CardStatusEnum.USED.getCode());
            cardRecord.setUsageStatus(CardUsageStatusEnum.INSERVICE.getCode());
            cardRecord.setId(card.getId());
            cardDao.updateByIdNoUpdateTime(cardRecord);
        }
        return card;
    }

    @Override
    public List<CardParentNoVO> findCardWithParentNo(List<String> cardNumberList) {
        return cardDao.findCardWithParentNo(cardNumberList);
    }


}
