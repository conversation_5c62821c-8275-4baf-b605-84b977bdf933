package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.po.ProductFrontClassifyRelateItem;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:41
 **/
public interface ProductFrontClassifyRelateItemManager {

    void deleteByClassifyId(Long classifyId);

    void batchInsert(Long classifyId, List<Long> itemIds, String userName);

    List<ProductFrontClassifyRelateItem> findByClassifyId(Long classifyId);
}
