package com.hengtiansoft.item.manager.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.DiscountActivityDao;
import com.hengtiansoft.item.dao.DiscountActivityRangeDao;
import com.hengtiansoft.item.entity.dto.DiscountActivityDTO;
import com.hengtiansoft.item.entity.dto.SkuDiscountActivityDTO;
import com.hengtiansoft.item.entity.dto.SkuProductBaseDTO;
import com.hengtiansoft.item.entity.po.DiscountActivity;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.vo.DiscountActivityProductVO;
import com.hengtiansoft.item.enumeration.DiscountActivityStatusEnum;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.manager.DiscountActivityManager;
import com.hengtiansoft.item.mapper.DiscountActivityMapper;
import com.hengtiansoft.item.utils.DiscountUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class DiscountActivityManagerImpl implements DiscountActivityManager {
    @Resource
    private DiscountActivityDao discountActivityDao;

    @Resource
    private DiscountActivityMapper discountActivityMapper;
    @Resource
    private DiscountActivityRangeDao discountActivityRangeDao;
    @Resource
    RedisOperation redisOperation;
    @Resource
    private SkuManager skuManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdate(DiscountActivityDTO dto) {
        if (Objects.isNull(dto.getLabel())) {
            dto.setLabel("");
        }
        DiscountActivity po;
        if (dto.getId() == null) {
            po = DiscountUtil.buildPO(dto);
            Assert.notEmpty(po.getRangeList(), "请选择商品范围");
            po.setStatus(DiscountUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
            validParam(po);
            log.info("insertOrUpdate.po: {}", JSON.toJSONString(po));
            discountActivityDao.insert(po);
            po.getRangeList().forEach(range -> {
                range.setDiscountActivityId(po.getId());
                range.setUsed(0L);
            });
            discountActivityRangeDao.insertList(po.getRangeList());
            redisOperation.hset(DiscountUtil.DISCOUNT_ACTIVITYID, po.getId().toString(), po);
            log.info("insertOrUpdate.step1");
            for (DiscountActivityRange range : po.getRangeList()) {
                SkuDiscountActivityDTO  skuDTO = DiscountUtil.buildSkuDTO(po, range);
                redisOperation.hset(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), po.getId().toString(), JSON.toJSONString(skuDTO));
            }
        } else {
            DiscountActivity poFromDb = discountActivityDao.findById(dto.getId());
            Assert.notNull(poFromDb, "限时折扣不存在");
            po = DiscountUtil.buildUpdatePO(poFromDb, dto);
            if(Objects.nonNull(dto.getStatus())){
                po.setStatus(dto.getStatus());
            }else{
                po.setStatus(DiscountUtil.getStatus(po.getStartTime(), po.getEndTime()).getCode());
            }
            validParam(po);
            log.info("insertOrUpdate.step2,poStatus: {}", JSON.toJSONString(po));
            discountActivityDao.updateAll(po);
            List<DiscountActivityRange> rangPoFromDbList = Lists.newArrayList();
            List<DiscountActivityRange> updateRangeList = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(po.getRangeList())){
                log.info("poRangeList:{}", JSON.toJSONString(po.getRangeList()));
                rangPoFromDbList = discountActivityRangeDao.findByActivityId(po.getId());
                for (DiscountActivityRange discountActivityRange : po.getRangeList()) {
                    Optional<DiscountActivityRange> first = rangPoFromDbList.stream().filter(e ->
                            e.getProductId().equals(discountActivityRange.getProductId()) &&
                                    e.getSkuId().equals(discountActivityRange.getSkuId())).findFirst();
                    if (first.isPresent()) {
                        log.info("enterPresent");
                        DiscountActivityRange dbRange = first.get();
                        if (discountActivityRange.getStock() - dbRange.getUsed() < 0) {
                            throw new BusinessException("修改后库存不能小于已下单库存");
                        }
                        discountActivityRange.setUpdateTime(new Date());
                        discountActivityRange.setId(dbRange.getId());
                        updateRangeList.add(discountActivityRange);
                        log.info("rangPoFromDbList.before: {}", JSON.toJSONString(rangPoFromDbList));
                        rangPoFromDbList.remove(dbRange);
                        log.info("rangPoFromDbList.after: {}", JSON.toJSONString(rangPoFromDbList));
                        continue;
                    }
                    discountActivityRange.setDiscountActivityId(po.getId());
                    discountActivityRangeDao.insert(discountActivityRange);
                }
                if(CollectionUtils.isNotEmpty(rangPoFromDbList)){
                    log.info("final.rangPoFromDbList:{}", JSON.toJSONString(rangPoFromDbList));
                    discountActivityRangeDao.deleteList(rangPoFromDbList);
                }
            }
            if(CollectionUtils.isNotEmpty(rangPoFromDbList)){
                rangPoFromDbList.forEach(range -> redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), po.getId().toString()));
            }
            if (CollectionUtils.isNotEmpty(updateRangeList)) {
                log.info("updateRangeList:{}", JSON.toJSONString(updateRangeList));
                discountActivityRangeDao.batchUpdate(updateRangeList);
            }
            // 活动结束
            if(Objects.equals(po.getStatus(), DiscountActivityStatusEnum.END.getCode())){
                redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITYID, po.getId().toString());
                List<DiscountActivityRange> rangList = discountActivityRangeDao.findByActivityId(po.getId());
                for (DiscountActivityRange range : rangList) {
                    redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), po.getId().toString());
                }
                return;
            }
            // 活动未开始和进行中
            redisOperation.hset(DiscountUtil.DISCOUNT_ACTIVITYID, po.getId().toString(), po);
            if(CollectionUtils.isEmpty(po.getRangeList())){
                List<DiscountActivityRange> rangList = discountActivityRangeDao.findByActivityId(po.getId());
                po.setRangeList(rangList);
            }
            for (DiscountActivityRange range : po.getRangeList()) {
                SkuDiscountActivityDTO skuDTO = DiscountUtil.buildSkuDTO(po, range);
                redisOperation.hset(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), po.getId().toString(), JSON.toJSONString(skuDTO));
            }
        }
    }

    private void validParam(DiscountActivity po) {
        // 校验活动名称是否重复
        DiscountActivity discountActivity = discountActivityDao.findByName(po.getName());
        if (po.getId() != null) {
            Assert.isTrue(discountActivity == null || Objects.equals(discountActivity.getId(), po.getId()), "活动名称重复");
        }else{
            Assert.isNull(discountActivity, "活动名称重复");
        }
        if(CollectionUtils.isNotEmpty(po.getRangeList())){
            List<Long> skudIds = StreamUtils.toList(po.getRangeList(), DiscountActivityRange::getSkuId);
            // 校验活动商品是否存在
            Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuManager.skuProductList(skudIds), SkuProductBaseDTO::getId);
            for (DiscountActivityRange range : po.getRangeList()) {
                Assert.notNull(range.getStock(), "商品skuId: " + range.getSkuId() + "库存不能为空");
                SkuProductBaseDTO skuProductBaseDTO = skuMap.get(range.getSkuId());
                Assert.notNull(skuProductBaseDTO, "商品不存在");
                Assert.isTrue(Objects.equals(skuProductBaseDTO.getProductId(), range.getProductId()), "商品与产品不匹配");
                // minimumCount起售数量校验,取值范围1-100
                if(Objects.nonNull(range.getMinimumCount())){
                    if(range.getMinimumCount() < 1 || range.getMinimumCount() > 100){
                        throw new BusinessException("起售数量取值范围1-100");
                    }
                    if(Objects.nonNull(range.getLimitNum()) && range.getMinimumCount() > range.getLimitNum()){
                        throw new BusinessException("起售数量不可大于限购数量");
                    }
                }
            }
            // 校验活动时间重复
            List<DiscountActivity> activities = discountActivityDao.findBySkuIds(skudIds);
            for (DiscountActivity activity : activities) {
                if(Objects.equals(activity.getId(), po.getId())){
                    continue;
                }
                // 更新的情况
                if (DateUtil.isOverlap(po.getStartTime(), po.getEndTime(), activity.getStartTime(), activity.getEndTime())) {
                    throw new BusinessException("该商品【"+skuMap.get(activity.getSkuId()).getProductName()+"】的时间段与名称为【" + activity.getName() + "】的有重复，请修改");
                }
            }
        }
    }

    @Override
    public List<DiscountActivity> getList(DiscountActivityDTO dto) {
        return discountActivityDao.findByCondition(dto);
    }

    @Override
    public DiscountActivity get(Long id) {
        return discountActivityDao.findById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        discountActivityDao.deleteById(id);
        List<DiscountActivityRange> rangeList = discountActivityRangeDao.findByActivityId(id);
        rangeList.forEach(range -> redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), id.toString()));
        discountActivityRangeDao.deleteByDiscountActivityId(id);
        redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITYID, id.toString());
    }

    @Override
    public List<DiscountActivity> findByCondition(DiscountActivityDTO dto) {
        return discountActivityDao.findByCondition(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DiscountActivity activity) {
        discountActivityDao.update(activity);
    }

    @Override
    public List<DiscountActivity> getListV1(DiscountActivityDTO dto) {
        return discountActivityMapper.findByCondition(dto);
    }

    @Override
    public List<DiscountActivity> getByIds(List<Long> discountActivityIds) {
        if (CollectionUtils.isEmpty(discountActivityIds)) return Collections.emptyList();
        return discountActivityDao.getByIds(discountActivityIds);
    }

    @Override
    public List<DiscountActivity> findStartNoticeDiscount(Date startTimeLess) {
        return discountActivityMapper.findStartNoticeDiscount(startTimeLess);
    }

    @Override
    public List<DiscountActivity> findNewNoticeDiscount() {
        return discountActivityMapper.findNewNoticeDiscount();
    }

    @Override
    public List<DiscountActivity> manualFindByCondition(DiscountActivityDTO accountActivityDTO) {
        return discountActivityMapper.manualFindByCondition(accountActivityDTO);
    }

    @Override
    public List<DiscountActivityProductVO> getDiscountActivityProductList(Integer status, List<Long> discountActivityIds) {
        return discountActivityDao.getDiscountActivityProductList(status, discountActivityIds);
    }
}
