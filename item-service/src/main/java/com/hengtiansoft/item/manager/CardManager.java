package com.hengtiansoft.item.manager;

import com.alibaba.excel.ExcelWriter;
import com.hengtiansoft.common.entity.vo.CardParentNoVO;
import com.hengtiansoft.item.entity.dto.CardBatchDTO;
import com.hengtiansoft.item.entity.dto.CardBatchUpdateDTO;
import com.hengtiansoft.item.entity.dto.CardBindDTO;
import com.hengtiansoft.item.entity.dto.CardCountCategoryDTO;
import com.hengtiansoft.item.entity.dto.CardDTO;
import com.hengtiansoft.item.entity.dto.CardListDTO;
import com.hengtiansoft.item.entity.dto.CardPwdListDTO;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 奶卡manager
 *
 * <AUTHOR>
 * @date 2020/9/2 15:50
 */
public interface CardManager {

    /**
     * 判断类别下有无奶卡
     *
     * @param name
     * @return
     */
    Boolean checkRelevance(String name);

    /**
     * 批量插入
     *
     * @param cardList
     */
    void batchInsert(List<Card> cardList);

    /**
     * 新建/编辑
     *
     * @param dto
     * @return
     */
    Long save(CardDTO dto);

    /**
     * 更新
     *
     * @param card
     * @return
     */
    Long update(Card card);

    /**
     *  根据主键批量更新
     *
     * @param card 更新信息
     * @param ids 主键
     * @return
     */
    void updateByPrimaryKey(List<Long> ids, Card card);

    /**
     * 插入
     *
     * @param card
     * @return
     */
    Long insert(Card card);

    /**
     * 根据id查询奶卡
     *
     * @param id
     * @return
     */
    Card selectByPrimaryKey(Long id);

    /**
     * 查询奶卡列表
     *
     * @param ids
     * @return
     */
    List<Card> selectByPrimaryKeyList(List<Long> ids);

    /**
     * 根据卡号卡密查询奶卡
     *
     * @param dto
     * @return
     */
    Card findByNumberAndPassword(CardBindDTO dto);

    List<Card> createV2(CardBatchDTO dto);

    /**
     * 批量创建
     *
     * @param dto
     * @return
     */
    List<String> createCycleCard(CardBatchDTO dto,Integer count);

    /**
     * 删除奶卡
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量更新状态（封装）
     *
     * @param dto
     */
    void batchUpdateStatus(CardBatchUpdateDTO dto);

    /**
     * 批量更新状态
     *
     * @param cardList
     */
    void batchUpdateStatus(List<Card> cardList);

    /**
     * 奶卡列表
     *
     * @param dto
     * @return
     */
    CardPageVO<CardListVO> list(CardListDTO dto);

    /**
     * 奶卡卡密列表
     *
     * @param dto
     * @return
     */
    List<CardCreateListVO> findCardPwdList(CardPwdListDTO dto);

    /**
     * 用户未过期奶卡列表
     *
     * @param userId
     * @return
     */
    List<CardListVO> userList(Long userId);

    /**
     * 用户已订完列表（服务完成/已过期）
     *
     * @param userId
     * @return
     */
    List<CardListVO> finishedList(Long userId);

    /**
     * 导入
     *
     * @param file
     * @return
     */
    List<CardVO> importCards(MultipartFile file);

    /**
     * 查询奶卡
     *
     * @param number 卡号
     * @return 奶卡
     */
    Card selectByNumber(String number);

    /**
     * 查询奶卡
     *
     * @param numberList
     * @return
     */
    List<Card> selectByNumberList(List<String> numberList);

    BigDecimal countActivateCards(boolean dateFlag);

    List<CardRemainingCountVO> countRemainingCount();

    int countRemainingCountSum();

    List<CardRemainingCountVO> countRemainingByDate(String date);

    void noRemainingHis(String date, ExcelWriter excelWriter);

    List<Card> findExpiration(Integer days, List<Long> userIdList);

    BigDecimal countUsageStatus(Integer usageStatus);

    List<CardCountCategoryVO> countCategory(CardCountCategoryDTO dto);

    List<Card> findInHome(List<Long> userId,String date);

    Card activateAndCheckCard(String cardNumber);

    List<CardParentNoVO> findCardWithParentNo(List<String> cardNumberList);
}
