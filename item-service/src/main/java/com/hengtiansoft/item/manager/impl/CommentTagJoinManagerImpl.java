package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.item.dao.CommentTagJoinDao;
import com.hengtiansoft.item.entity.dto.CommentTagJoinListDTO;
import com.hengtiansoft.item.entity.po.CommentTagJoin;
import com.hengtiansoft.item.dao.CommentTagJoinDao;
import com.hengtiansoft.item.entity.po.CommentTagJoin;
import com.hengtiansoft.item.manager.CommentTagJoinManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommentTagJoinManagerImpl implements CommentTagJoinManager {

    @Resource
    private CommentTagJoinDao commentTagJoinDao;

    @Override
    public void deleteByTagIdWithType(Long id, Integer type) {
        commentTagJoinDao.deleteByTagIdWithType(id, type);
    }

    @Override
    public List<CommentTagJoin> findByCondition(CommentTagJoinListDTO dto) {
        return commentTagJoinDao.findByCondition(dto);
    }

    @Override
    public void insertList(List<CommentTagJoin> list) {
        commentTagJoinDao.insertList(list);
    }

    @Override
    public List<CommentTagJoin> findByCommentId(Long id) {
        return commentTagJoinDao.findByTargetId(id);
    }
}
