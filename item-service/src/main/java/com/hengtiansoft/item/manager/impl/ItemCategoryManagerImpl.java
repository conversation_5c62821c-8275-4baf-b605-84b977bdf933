package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.item.dao.CommentTagJoinDao;
import com.hengtiansoft.item.dao.ItemCategoryDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.CategorySearchDTO;
import com.hengtiansoft.item.entity.dto.CommentTagJoinCategoryDTO;
import com.hengtiansoft.item.entity.dto.ItemCategorySaveDTO;
import com.hengtiansoft.item.entity.dto.ItemCategoryUpdateDTO;
import com.hengtiansoft.item.entity.po.Category;
import com.hengtiansoft.item.entity.po.CommentTagJoin;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.vo.*;
import com.hengtiansoft.item.enumeration.CommentTagJoinTypeEnum;
import com.hengtiansoft.item.enumeration.ItemCategoryLevelEnum;
import com.hengtiansoft.item.manager.ItemCategoryManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 商品分类Manager实现类
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
@Component
public class ItemCategoryManagerImpl implements ItemCategoryManager {

    @Autowired
    private ItemCategoryDao itemCategoryDao;

    @Autowired
    private ProductDao productDao;

    @Autowired
    private CommentTagJoinDao commentTagJoinDao;

    @Override
    public void add(ItemCategorySaveDTO dto, String operator) {
        List<String> cateName = new ArrayList<>();
        Category category = new Category();
        BeanUtils.copy(dto, category);
        category.setCateLevel(dto.getCateLevel().getCode());
        Category firstCate = itemCategoryDao.findById(dto.getParentId());
        if (dto.getCateLevel() == ItemCategoryLevelEnum.FIRSTCATE) {
            category.setNamePath(dto.getCateName());
        } else {
            cateName.add(firstCate.getCateName());
            cateName.add(dto.getCateName());
            String name = String.join(",", cateName);
            category.setNamePath(name);
        }
        itemCategoryDao.insert(category);
        dto.setId(category.getId());
        //分类绑定标签
        if (dto.getCateLevel() == ItemCategoryLevelEnum.FIRSTCATE) {
            insertTagJoin(dto.getId(), dto.getTagIds(), operator);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ItemCategoryUpdateDTO dto, String operator, boolean isTagJoin) {
        boolean flag=false;
        if (dto.getCateLevel() == ItemCategoryLevelEnum.FIRSTCATE) {
            Category firstCate = itemCategoryDao.findById(dto.getId());
            if(!firstCate.getCateName().equals(dto.getCateName())){
                flag=true;
            }
            if(isTagJoin){
                //先删除分类的关联
                commentTagJoinDao.deleteByTargetIdWithType(dto.getId(), CommentTagJoinTypeEnum.CATEGORY.getCode());
                insertTagJoin(dto.getId(), dto.getTagIds(), operator);
            }
        }

        Category category = new Category();
        BeanUtils.copy(dto, category);
        itemCategoryDao.update(category);

        //一级改名同时也刷二级
        if(flag){
            List<Category> categoryList = itemCategoryDao.secondListByParentId(dto.getId());
            for (Category cate : categoryList) {
                List<String> cateName = new ArrayList<>();
                cateName.add(dto.getCateName());
                cateName.add(cate.getCateName());
                String name = String.join(",", cateName);
                Category secondCate= new Category();
                secondCate.setNamePath(name);
                secondCate.setId(cate.getId());
                itemCategoryDao.update(secondCate);
            }
        }

    }

    private void insertTagJoin(Long targetId, List<Long> tagIds, String operator) {
        if(!CollectionUtils.isEmpty(tagIds)){
            for (Long tagId: tagIds) {
                CommentTagJoin tagJoin = new CommentTagJoin();
                tagJoin.setTagId(tagId);
                tagJoin.setTargetId(targetId);
                tagJoin.setType(CommentTagJoinTypeEnum.CATEGORY.getCode());
                tagJoin.setOperator(StringUtils.isBlank(operator) ? "" : operator);
                commentTagJoinDao.insert(tagJoin);
            }
        }
    }

    @Override
    public void delete(Long id) {
        Category category = new Category();
        category.setId(id);
        category.setDelflag(1);
        itemCategoryDao.update(category);
    }

    @Override
    public ItemCategoryVO one(Long id) {
        ItemCategoryVO itemCategoryVO = new ItemCategoryVO();
        Category category = itemCategoryDao.findById(id);
        if(category == null) {
            return null;
        }
        BeanUtils.copy(category, itemCategoryVO);
        itemCategoryVO.setCateLevel(ItemCategoryLevelEnum.getEnumByCode(category.getCateLevel()));
        if (itemCategoryVO.getCateLevel() == ItemCategoryLevelEnum.FIRSTCATE) {
            itemCategoryVO.setCateUrl(category.getPicUrl());
            List<CommentTagJoinCategoryDTO> tagJoinCateList = commentTagJoinDao.selectTagByTargetIds(Arrays.asList(id));
            List<CommentTagVO> tagList = new ArrayList<>();
            for (CommentTagJoinCategoryDTO categoryDTO: tagJoinCateList) {
                CommentTagVO vo = new CommentTagVO();
                vo.setId(categoryDTO.getTagId());
                vo.setName(categoryDTO.getName());
                tagList.add(vo);
            }
            itemCategoryVO.setTagList(tagList);
        } else {
            itemCategoryVO.setIconUrl(category.getPicUrl());
        }
        return itemCategoryVO;
    }

    @Override
    public PageVO<ItemCategoryListVO> list(CategorySearchDTO dto) {
        List<Category> firstLists = itemCategoryDao.search(dto);
        if (CollectionUtils.isEmpty(firstLists)) {
            return PageUtils.emptyPage();
        }
        List<Long> cateIds = firstLists.stream().map(Category::getId).collect(Collectors.toList());
        Optional.ofNullable(cateIds).orElse(new ArrayList<>());
        List<Product> byCateIds = productDao.findByCateIds(cateIds);
        Map<Long, List<Product>> map = byCateIds.stream().collect(Collectors.groupingBy(Product::getCateId));
        return PageUtils.convert(firstLists, data -> {
            ItemCategoryListVO itemCategoryListVO = new ItemCategoryListVO();
            BeanUtils.copy(data, itemCategoryListVO);
            itemCategoryListVO.setCateLevel(ItemCategoryLevelEnum.getEnumByCode(data.getCateLevel()));
            if (data.getPicUrl() != null) {
                itemCategoryListVO.setCateUrl(data.getPicUrl());
            }
            if (CollectionUtils.isEmpty(map.get(data.getId()))) {
                itemCategoryListVO.setIsRelatePro(0);
            } else {
                itemCategoryListVO.setIsRelatePro(1);
            }
            return itemCategoryListVO;
        });
    }

    @Override
    public List<ItemCategoryVO> secondList(ItemCategoryLevelEnum level) {
        List<ItemCategoryVO> itemCategoryVOS = new ArrayList<>();
        List<Category> search = itemCategoryDao.listByLevel(level);
        if (CollectionUtils.isEmpty(search)) {
            return new ArrayList<>();
        }
        List<Long> cateIds = search.stream().map(Category::getId).collect(Collectors.toList());
        List<Product> byCateIds = productDao.findByCateIds(cateIds);
        Map<Long, List<Product>> map = byCateIds.stream().collect(Collectors.groupingBy(Product::getCateId));
        search.stream().forEach(data -> {
            ItemCategoryVO itemCategoryVO = new ItemCategoryVO();
            BeanUtils.copy(data, itemCategoryVO);
            if (data.getPicUrl() != null) {
                itemCategoryVO.setIconUrl(data.getPicUrl());
            }
            if (CollectionUtils.isEmpty(map.get(data.getId()))) {
                itemCategoryVO.setIsRelatePro(0);
            } else {
                itemCategoryVO.setIsRelatePro(1);
            }
            itemCategoryVOS.add(itemCategoryVO);
        });
        return itemCategoryVOS.stream().sorted(Comparator.comparing(ItemCategoryVO::getSort))
                .collect(Collectors.toList());

    }

    @Override
    public List<FirstCategoryVO> firstList() {
        List<Category> categories = itemCategoryDao.listByLevel(ItemCategoryLevelEnum.FIRSTCATE);
        return BeanUtils.copyList(categories, FirstCategoryVO::new);
    }

    @Override
    public List<CategoryListVO> cateList() {
        List<Category> categories = itemCategoryDao.cateList();
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public List<CategoryListVO> catePublicList(CategorySearchDTO dto) {
        List<Category> categories = itemCategoryDao.catePublicList(dto);
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public List<CategoryListVO> secondCateList(Integer isPublic) {
        CategorySearchDTO dto = new CategorySearchDTO();
        dto.setItemCategoryLevel(ItemCategoryLevelEnum.SECONDCATE);
        if(Objects.nonNull(isPublic)){
            dto.setIsPublic(isPublic);
        }
        List<Category> firstList = itemCategoryDao.search(dto);
        return BeanUtils.copyList(firstList, CategoryListVO::new);
    }

    @Override
    public Map<Long, String> selectNamePath() {
        Map<Long, String> map = new HashMap<>();
        List<Category> categories = itemCategoryDao.selectAll();
        categories.stream().forEach(data -> map.put(data.getId(), data.getNamePath()));
        return map;
    }

    @Override
    public List<CategoryListVO> secondListByParentId(Long parentId) {
        List<Category> categories = itemCategoryDao.secondCates(parentId);
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public ItemCategoryVO searchByName(String name, Long parentId, Long id) {
        ItemCategoryVO itemCategoryVO = new ItemCategoryVO();
        Category category = itemCategoryDao.findByName(name, parentId, id);
        if (category != null) {
            BeanUtils.copy(category, itemCategoryVO);
            itemCategoryVO.setCateLevel(ItemCategoryLevelEnum.getEnumByCode(category.getCateLevel()));
            return itemCategoryVO;
        } else {
            return null;
        }
    }

    @Override
    public List<CategoryListVO> categoryList(Integer isPublic) {
        List<Category> categories = itemCategoryDao.categoryList(isPublic);
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public List<CategoryListVO> categoryByCateTypeList(List<Integer> cateType) {
        List<Category> categories = itemCategoryDao.categoryByCateTypeList(cateType);
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public List<CategoryListVO> categoryByCateType(Integer cateType, Integer isPublic) {
        List<Category> categories = itemCategoryDao.categoryByCateType(cateType, isPublic);
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public List<CategoryListVO> categoryByCateTypeNotId(Integer cateType, Integer isPublic, Long id) {
        List<Category> categories = itemCategoryDao.categoryByCateTypeNotId(cateType, isPublic, id);
        return BeanUtils.copyList(categories, CategoryListVO::new);
    }

    @Override
    public ItemCategoryVO searchName(String cateName) {
        Category category = itemCategoryDao.searchName(cateName);
        return BeanUtils.copy(category, ItemCategoryVO::new);
    }

}
