package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.po.CommentTag;

import java.util.List;

import com.hengtiansoft.item.entity.dto.CommentTagListDTO;
import com.hengtiansoft.item.entity.dto.CommentTagSaveDTO;
import com.hengtiansoft.item.entity.po.CommentTag;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommentTagManager {

    List<CommentTag> search(CommentTagListDTO dto);

    void save(CommentTagSaveDTO dto);

    void checkTagName(CommentTagSaveDTO dto);

    void deleteById(Long id);

    CommentTag findBySystemType(Integer code);

    List<CommentTag> findByIds(List<Long> tagIds);
}
