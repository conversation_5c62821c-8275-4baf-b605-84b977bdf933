package com.hengtiansoft.item.manager.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.item.dao.ProductSuggestDao;
import com.hengtiansoft.item.entity.dto.ProductSuggestListDTO;
import com.hengtiansoft.item.entity.po.ProductSuggest;
import com.hengtiansoft.item.manager.ProductSuggestManager;
import com.hengtiansoft.item.mapper.ProductSuggestMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ProductSuggestManagerImpl implements ProductSuggestManager {

    @Resource
    private ProductSuggestDao productSuggestDao;
    @Resource
    private ProductSuggestMapper productSuggestMapper;

    @Override
    public List<ProductSuggest> findByCondition(ProductSuggestListDTO dto) {
        return productSuggestDao.findByCondition(dto);
    }

    @Override
    public ProductSuggest findBySort(Integer sort, Integer position) {
        Condition condition = new Condition(ProductSuggest.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("sort", sort);
        criteria.andEqualTo("position", position);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return productSuggestMapper.selectOneByExample(condition);
    }

    @Override
    public ProductSuggest findById(Long id){
        return productSuggestDao.findById(id);
    }

    @Override
    public void insert(ProductSuggest po) {
        productSuggestDao.insert(po);
    }

    @Override
    public void update(ProductSuggest po) {
        productSuggestDao.update(po);
    }

    @Override
    public void updateAll(ProductSuggest po) {
        productSuggestDao.updateAll(po);
    }

    @Override
    public void updateGreaterSort(Integer sort, Integer position){
        productSuggestMapper.updateGreaterSort(sort, position);
    }

    @Override
    public void delete(Long id){
        productSuggestDao.delete(id);
    }

    @Override
    public List<ProductSuggest> finAutoList(Date now, CommonActivityStatusEnum statusEnum) {
        Example example = new Example(ProductSuggest.class);
        Example.Criteria criteria = example.createCriteria();
        switch (statusEnum){
            case NOT_STARTED:
                criteria.andGreaterThan("startTime", now);
                criteria.andNotEqualTo("status", CommonActivityStatusEnum.NOT_STARTED.getCode());
                break;
            case IN_PROGRESS:
                criteria.andLessThanOrEqualTo("startTime", now);
                criteria.andGreaterThanOrEqualTo("endTime", now);
                criteria.andNotEqualTo("status", CommonActivityStatusEnum.IN_PROGRESS.getCode());
                break;
            case END:
                criteria.andLessThan("endTime", now);
                criteria.andNotEqualTo("status", CommonActivityStatusEnum.END.getCode());
                break;
            default:
                return Lists.newArrayList();
        }
        return productSuggestMapper.selectByExample(example);
    }

    @Override
    public List<ProductSuggest> findHasItemSuggests(ProductSuggestListDTO dto) {
        return productSuggestDao.findHasItemSuggests(dto);
    }

}
