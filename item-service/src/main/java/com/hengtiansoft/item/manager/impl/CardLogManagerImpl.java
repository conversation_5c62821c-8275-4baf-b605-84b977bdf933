/**
 * @(#)CardLogManagerImpl.java, 2021/1/13.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.hengtiansoft.item.manager.impl;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hengtiansoft.item.dao.CardLogDao;
import com.hengtiansoft.item.entity.po.CardLog;
import com.hengtiansoft.item.manager.CardLogManager;

import java.util.List;

/**
 * 奶卡操作日志manager
 *
 * <AUTHOR>
 * @date 2021/1/13 17:10
 */
@Slf4j
@Service
public class CardLogManagerImpl implements CardLogManager {

    @Autowired
    private CardLogDao cardLogDao;

    /**
     * 插入日志
     *
     * @param cardLog
     * @return
     */
    @Override
    public Long insert(CardLog cardLog) {
        return cardLogDao.insert(cardLog);
    }

    /**
     * 查询日志
     *
     * @param cardNumber
     * @return
     */
    @Override
    public List<CardLog> selectByCardNumber(String cardNumber) {
        return cardLogDao.selectByCardNumber(cardNumber);
    }

    /**
     * 批量插入日志
     *
     * @param cardLogs
     * @return
     */
    @Override
    public Integer batchInsert(List<CardLog> cardLogs) {
        return cardLogDao.batchInsert(cardLogs);
    }
}
