package com.hengtiansoft.item.manager.impl;
import java.util.Date;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.item.dao.ProductFrontClassifyRelateItemDao;
import com.hengtiansoft.item.entity.po.ProductFrontClassifyRelateItem;
import com.hengtiansoft.item.manager.ProductFrontClassifyRelateItemManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:41
 **/
@Slf4j
@Service
public class ProductFrontClassifyRelateItemManagerImpl implements ProductFrontClassifyRelateItemManager {

    @Resource
    private ProductFrontClassifyRelateItemDao productFrontClassifyRelateItemDao;
    @Override
    public void deleteByClassifyId(Long classifyId) {
        productFrontClassifyRelateItemDao.deleteByClassifyId(classifyId);
    }

    @Override
    public void batchInsert(Long classifyId, List<Long> itemIds, String userName) {
        if(CollectionUtils.isEmpty(itemIds)){
            return;
        }
        List<ProductFrontClassifyRelateItem> list = new ArrayList<>();
        itemIds.forEach(itemId -> {
            ProductFrontClassifyRelateItem relateItem = new ProductFrontClassifyRelateItem();
            relateItem.setProductFrontClassifyId(classifyId);
            relateItem.setProductId(itemId);
            relateItem.setOperator(userName);
            relateItem.setCreateTime(new Date());
            relateItem.setUpdateTime(new Date());
            relateItem.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            list.add(relateItem);
        });
        productFrontClassifyRelateItemDao.batchInsert(list);
    }

    @Override
    public List<ProductFrontClassifyRelateItem> findByClassifyId(Long classifyId) {
        return productFrontClassifyRelateItemDao.findByClassifyId(classifyId);
    }
}
