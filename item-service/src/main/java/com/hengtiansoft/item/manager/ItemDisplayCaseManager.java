package com.hengtiansoft.item.manager;

import com.hengtiansoft.common.entity.dto.PageParams;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseSaveDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseSearchDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseUpdateDTO;
import com.hengtiansoft.item.entity.po.DisplayCaseProduct;
import com.hengtiansoft.item.entity.vo.DisplayListVO;
import com.hengtiansoft.item.entity.vo.FirstDisplayListVO;
import com.hengtiansoft.item.entity.vo.HomePageDisplayVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseVO;

import java.util.List;

/**
 * Description: 运营分类管理Manager
 *
 * <AUTHOR>
 * @since 24.03.2020
 */
public interface ItemDisplayCaseManager {

    void add(DisplayCaseSaveDTO dto);

    void update(DisplayCaseUpdateDTO dto);

    void delete(Long id);

    ItemDisplayCaseVO one(Long id);

    PageVO<ItemDisplayCaseListVO> list(DisplayCaseSearchDTO dto);

    List<FirstDisplayListVO> firstList();

    List<ItemDisplayCaseVO> secondList();

    ItemDisplayCaseVO selectByType(Integer type);

    List<ItemDisplayCaseVO> selectIndexType(boolean isAllShow);

    List<DisplayListVO> displayList();

    List<DisplayListVO> secondDisplayList();

    List<DisplayListVO> secondListByParentId(Long id);

    List<ItemDisplayCaseVO> selectByIsHomepage(Integer isHomepage);

    ItemDisplayCaseVO searchByName(String displayCaseName,Long parentId,Long id);

    HomePageDisplayVO homepageInfo();

    ItemDisplayCaseVO searchName(String displayCaseName);

    List<DisplayCaseProduct> selectByDisplayCaseIds(List<Long> displayCaseIds);

    PageVO<DisplayListVO> displayListInfo(PageParams params);
}
