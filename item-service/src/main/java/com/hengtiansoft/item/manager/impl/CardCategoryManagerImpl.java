package com.hengtiansoft.item.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.item.dao.CardCategoryDao;
import com.hengtiansoft.item.entity.dto.CardCategoryDTO;
import com.hengtiansoft.item.entity.dto.CardCategoryListDTO;
import com.hengtiansoft.item.entity.po.CardCategory;
import com.hengtiansoft.item.enumeration.ProductCategoryEnum;
import com.hengtiansoft.item.manager.CardCategoryManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 奶卡类别manager
 *
 * <AUTHOR>
 * @date 2020/9/2 15:55
 */
@Slf4j
@Service
public class CardCategoryManagerImpl implements CardCategoryManager {

    @Autowired
    private CardCategoryDao cardCategoryDao;

    @Override
    public CardCategory selectById(Long id) {
        return cardCategoryDao.selectByPrimaryKey(id);
    }

    /**
     * 根据类别名称批量查询
     *
     * @param names
     * @return
     */
    @Override
    public List<CardCategory> selectByNames(List<String> names) {
        return cardCategoryDao.selectByNames(names);
    }


    @Override
    public List<Long> selectByCategoryCount(Integer categoryCount){
        CardCategoryListDTO dto = new CardCategoryListDTO();
        dto.setCategoryCount(categoryCount);
        dto.setPageNum(null);
        dto.setPageSize(null);
        List<CardCategory> cardCategories = cardCategoryDao.findByCondition(dto);
        if (CollectionUtils.isNotEmpty(cardCategories)){
             return cardCategories.stream().map(CardCategory::getId).collect(Collectors.toList());
        }
        return null;
    }


    @Override
    public List<Long> findByECardSpecialSql (List<Integer> categoryCount){
        CardCategoryListDTO dto = new CardCategoryListDTO();
        dto.setPageNum(null);
        dto.setPageSize(null);
        List<CardCategory> cardCategories = cardCategoryDao.findByECard(dto, categoryCount);
        if (CollectionUtils.isNotEmpty(cardCategories)){
            return cardCategories.stream().map(CardCategory::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<CardCategory> selectCardCategoryByCategoryCount(Integer categoryCount) {
        CardCategoryListDTO dto = new CardCategoryListDTO();
        dto.setCategoryCount(categoryCount);
        dto.setPageNum(null);
        dto.setPageSize(null);
        return cardCategoryDao.findByCondition(dto);
    }

    /**
     * 获取所有类别名称
     *
     * @return
     */
    @Override
    public List<CardCategory> getAll() {
        return cardCategoryDao.getAll();
    }

    /**
     * 新建/编辑
     *
     * @param dto
     * @return
     */
    @Override
    public Long save(CardCategoryDTO dto) {

        //上下限价格校验
        checkPrice(dto);
        Integer productCategory = dto.getProductCategory();
        boolean lowTemperature = ProductCategoryEnum.isLowTemperature(productCategory);
        if (lowTemperature) {
            dto.setTemperature(1);
        } else {
            dto.setTemperature(0);
        }
        // 重名校验
        Long checkId = cardCategoryDao.checkName(dto.getCategoryName());
        Long id = dto.getId();
        CardCategory milkCardCategory = new CardCategory();
        BeanUtils.copy(dto, milkCardCategory);
        if (Objects.isNull(id)) {
            // 新建
            if (Objects.nonNull(checkId)) {
                throw new BusinessException("类别名称已存在，请重试！");
            }
            id = cardCategoryDao.insert(milkCardCategory);
        } else {
            // 编辑
            if (Objects.nonNull(checkId) && !checkId.equals(id)) {
                throw new BusinessException("类别名称已存在，请重试！");
            }
            CardCategory old = cardCategoryDao.selectByPrimaryKey(id);
            Assert.notNull(old, "类别不存在");
            // 更新换成了updateByPrimaryKey，就不能把null值赋给product了，不然会把数据库的值覆盖掉
            BeanUtil.copyProperties(dto, old, CopyOptions.create().setIgnoreNullValue(true));
            old.setAddressLimit(dto.getAddressLimit());
            id = cardCategoryDao.updateAll(old);
        }
        return id;
    }

    private void checkPrice(CardCategoryDTO dto) {
        if (Objects.isNull(dto.getCapPrice()) || Objects.isNull(dto.getFloorPrice())) {
            throw new BusinessException("价格上下限不能为空，请重试！");
        }
        if (dto.getCapPrice().compareTo(dto.getFloorPrice()) < 0) {
            throw new BusinessException("上限价格不能小于下限价格，请重试！");
        }
    }

    /**
     * 列表查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<CardCategory> findByCondition(CardCategoryListDTO dto) {
        return cardCategoryDao.findByCondition(dto);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public int delete(Long id) {
        return cardCategoryDao.delete(id);
    }

    @Override
    public CardCategory selectByPrimaryKey(Long categoryId) {
        return cardCategoryDao.selectByPrimaryKey(categoryId);
    }
}
