package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.SkuAttrDao;
import com.hengtiansoft.item.entity.dto.SkuAttrDTO;
import com.hengtiansoft.item.entity.po.SkuAttr;
import com.hengtiansoft.item.manager.SkuAttrManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class SkuAttrManagerImpl implements SkuAttrManager {

    @Autowired
    private SkuAttrDao skuAttrDao;

    @Override
    public Map<Long, List<SkuAttrDTO>> getSkuAttrMapBySkuIds(List<Long> skuIds) {
        List<SkuAttr> skuAttrs = skuAttrDao.selectBySkuIds(skuIds);
        Map<Long, List<SkuAttrDTO>> skuAttrMap = StreamUtils.mapGroup(skuAttrs,
                e -> BeanUtils.copy(e, SkuAttrDTO::new), SkuAttrDTO::getSkuId);
        return skuAttrMap;
    }

}
