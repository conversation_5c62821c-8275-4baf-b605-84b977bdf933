package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.item.dao.ProductFrontClassifyActivityDao;
import com.hengtiansoft.item.entity.po.ProductFrontClassifyActivity;
import com.hengtiansoft.item.manager.ProductFrontClassifyActivityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:41
 **/
@Slf4j
@Service
public class ProductFrontClassifyActivityManagerImpl implements ProductFrontClassifyActivityManager {

    @Autowired
    private ProductFrontClassifyActivityDao productFrontClassifyActivityDao;
    @Override
    public List<ProductFrontClassifyActivity> selectAll() {
        return productFrontClassifyActivityDao.selectAll();
    }

    @Override
    public ProductFrontClassifyActivity getById(Long id) {
        return productFrontClassifyActivityDao.getById(id);
    }
}
