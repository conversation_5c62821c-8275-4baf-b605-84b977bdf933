package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.ProductSuggestItemListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSortDTO;
import com.hengtiansoft.item.entity.po.ProductSuggestItem;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemResultVO;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-21 08:50
 **/
public interface ProductSuggestItemManager {


    List<ProductSuggestItemResultVO> findPageByCondition(ProductSuggestItemListDTO dto);

    ProductSuggestItem selectExistByTypeAndSuggestId(Integer type, Long suggestId);

    void deleteByTypeAndSuggestId(Integer type, Long suggestId);

    List<ProductSuggestItem> findSelectedItemsByTypeAndSuggestId(Integer type, Long suggestId);

    List<ProductSuggestItem> findSelectedItemsBySuggestId(Long suggestId);

    void batchInsert(List<ProductSuggestItem> addItems);

    void deleteByTypeAndSuggestIdAndProductIds(Integer type, Long suggestId, List<Long> deleteProductIds, String userName);

    ProductSuggestItem selectById(Long id);

    List<ProductSuggestItem> selectGreaterThanSortList(Integer type, Long productSuggestId, Integer sort);

    void updateSortById(ProductSuggestItemSortDTO sortDTO);

    void batchUpdate(List<ProductSuggestItem> greaterThanCurrentSortItems);

    void deleteById(Long id);

    List<ProductSuggestItem> findSelectedItemsByType(Integer type);

    void autoRecommendProduct();
}
