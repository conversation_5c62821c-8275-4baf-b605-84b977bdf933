package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.DiscountActivityDTO;
import com.hengtiansoft.item.entity.po.DiscountActivity;
import com.hengtiansoft.item.entity.vo.DiscountActivityProductVO;

import java.util.Date;
import java.util.List;

public interface DiscountActivityManager {
    void insertOrUpdate(DiscountActivityDTO dto);

    List<DiscountActivity> getList(DiscountActivityDTO dto);

    DiscountActivity get(Long id);

    void deleteById(Long id);

    List<DiscountActivity> findByCondition(DiscountActivityDTO dto);

    void update(DiscountActivity activity);

    List<DiscountActivity> getListV1(DiscountActivityDTO dto);

    List<DiscountActivity> getByIds(List<Long> discountActivityIds);

    List<DiscountActivityProductVO> getDiscountActivityProductList(Integer status, List<Long> discountActivityIds);

    List<DiscountActivity> findStartNoticeDiscount(Date startTimeLess);

    List<DiscountActivity> findNewNoticeDiscount();

    List<DiscountActivity> manualFindByCondition(DiscountActivityDTO accountActivityDTO);
}
