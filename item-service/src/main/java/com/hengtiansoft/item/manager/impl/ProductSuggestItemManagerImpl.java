package com.hengtiansoft.item.manager.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.item.dao.ProductSuggestItemDao;
import com.hengtiansoft.item.entity.dto.AutoRecommendProductDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemListDTO;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemSortDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.po.ProductSuggestItem;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemResultVO;
import com.hengtiansoft.item.enumeration.ProductSuggestItemTypeEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.manager.ProductSuggestItemManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-03-21 08:51
 **/
@Service
public class ProductSuggestItemManagerImpl implements ProductSuggestItemManager {

    @Resource
    private ProductSuggestItemDao suggestItemDao;

    @Resource
    private ProductManager productManager;

    @Override
    public List<ProductSuggestItemResultVO> findPageByCondition(ProductSuggestItemListDTO dto) {
        return suggestItemDao.findPageByCondition(dto);
    }

    @Override
    public ProductSuggestItem selectExistByTypeAndSuggestId(Integer type, Long suggestId) {
        return suggestItemDao.selectExistByTypeAndSuggestId(type, suggestId);
    }

    @Override
    public void deleteByTypeAndSuggestId(Integer type, Long suggestId) {
        suggestItemDao.deleteByTypeAndSuggestId(type, suggestId);
    }

    @Override
    public List<ProductSuggestItem> findSelectedItemsByTypeAndSuggestId(Integer type, Long suggestId) {
        return suggestItemDao.findSelectedItemsByTypeAndSuggestId(type, suggestId);
    }

    @Override
    public List<ProductSuggestItem> findSelectedItemsBySuggestId(Long suggestId) {
        return suggestItemDao.findSelectedItemsBySuggestId(suggestId);
    }

    @Override
    public void batchInsert(List<ProductSuggestItem> addItems) {
        suggestItemDao.batchInsert(addItems);
    }

    @Override
    public void deleteByTypeAndSuggestIdAndProductIds(Integer type, Long suggestId, List<Long> deleteProductIds, String userName) {
        if (CollectionUtils.isEmpty(deleteProductIds)) return;
        suggestItemDao.deleteByTypeAndSuggestIdAndProductIds(type, suggestId, deleteProductIds, userName);
    }

    @Override
    public ProductSuggestItem selectById(Long id) {
        return suggestItemDao.selectById(id);
    }

    @Override
    public List<ProductSuggestItem> selectGreaterThanSortList(Integer type, Long productSuggestId, Integer sort) {
        return suggestItemDao.selectGreaterThanSortList(type, productSuggestId, sort);
    }

    @Override
    public void updateSortById(ProductSuggestItemSortDTO sortDTO) {
        suggestItemDao.updateSortById(sortDTO);
    }

    @Override
    public void batchUpdate(List<ProductSuggestItem> greaterThanCurrentSortItems) {
        suggestItemDao.batchUpdate(greaterThanCurrentSortItems);
    }

    @Override
    public void deleteById(Long id) {
        suggestItemDao.deleteById(id);
    }

    @Override
    public List<ProductSuggestItem> findSelectedItemsByType(Integer type) {
        return suggestItemDao.findSelectedItemsByType(type);
    }

    @Override
    public void autoRecommendProduct() {
        List<ProductSuggestItem> selectedItems = this.findSelectedItemsByType(ProductSuggestItemTypeEnum.AUTO_RECOMMEND.getCode());
        if (CollectionUtils.isEmpty(selectedItems)) {
            return;
        }
        List<Long> productIds = selectedItems.stream().map(ProductSuggestItem::getProductId).distinct().collect(Collectors.toList());
        List<Product> productList = productManager.findByIds(productIds);
        Map<Long, Integer> productSalesMap = productList.stream().collect(HashMap::new, (m, v)->m.put(v.getId(), v.getHotListSort()), HashMap::putAll);
        Map<Long, List<ProductSuggestItem>> suggestItemGroupMap = selectedItems.stream().collect(Collectors.groupingBy(ProductSuggestItem::getProductSuggestId));
        for (Map.Entry<Long, List<ProductSuggestItem>> entry : suggestItemGroupMap.entrySet()) {
            List<ProductSuggestItem> noSortItems = entry.getValue();
            List<AutoRecommendProductDTO> autoRecommendProductDTOList = Lists.newArrayList();
            noSortItems.forEach(e -> {
                Integer hotSort = productSalesMap.get(e.getProductId());
                AutoRecommendProductDTO productDTO = new AutoRecommendProductDTO();
                productDTO.setId(e.getId());
                productDTO.setHotListSort(Objects.isNull(hotSort)? 999 : hotSort);
                productDTO.setSort(e.getSort());
                autoRecommendProductDTOList.add(productDTO);
            });
            List<AutoRecommendProductDTO> sortedItems = autoRecommendProductDTOList.stream().sorted(Comparator.comparing(AutoRecommendProductDTO::getHotListSort)).collect(Collectors.toList());
            List<ProductSuggestItem> updateSuggestItems = Lists.newArrayList();
            Integer recountSort = 1;
            for (AutoRecommendProductDTO sortedItem : sortedItems) {
                ProductSuggestItem suggestItem = new ProductSuggestItem();
                suggestItem.setId(sortedItem.getId());
                suggestItem.setSort(recountSort);
                updateSuggestItems.add(suggestItem);
                recountSort++;
            }
            this.batchUpdate(updateSuggestItems);
        }
    }
}
