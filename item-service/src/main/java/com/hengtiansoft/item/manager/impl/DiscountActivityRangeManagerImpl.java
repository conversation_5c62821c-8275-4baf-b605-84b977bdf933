package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.item.dao.DiscountActivityRangeDao;
import com.hengtiansoft.item.entity.dto.DiscountProductDTO;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.vo.DiscountActivityProductVO;
import com.hengtiansoft.item.manager.DiscountActivityRangeManager;
import com.hengtiansoft.item.mapper.DiscountActivityRangeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class DiscountActivityRangeManagerImpl implements DiscountActivityRangeManager {
    @Resource
    private DiscountActivityRangeDao discountActivityRangeDao;
    @Resource
    private DiscountActivityRangeMapper discountActivityRangeMapper;


    @Override
    public List<DiscountActivityRange> findByActivityId(Long id) {
        return discountActivityRangeDao.findByActivityId(id);
    }

    @Override
    public List<DiscountActivityRange> findByActivityIdsWithDel(List<Long> ids) {
        return discountActivityRangeDao.findByActivityIdsWithDel(ids);
    }

    @Override
    public List<DiscountActivityRange> findByProductInfo(Long skuId, Long productId) {
        return discountActivityRangeDao.findByProductInfo(skuId, productId);
    }

    @Override
    public List<DiscountActivityRange> findByActivityIds(List<Long> pageActivityIds) {
        if (CollectionUtils.isEmpty(pageActivityIds)) return Collections.emptyList();
        return discountActivityRangeDao.findByActivityIds(pageActivityIds);
    }

    @Override
    public List<DiscountActivityRange> findByProductId(Long productId) {
        return discountActivityRangeDao.findByProductId(productId);
    }

    @Override
    public List<DiscountActivityRange> findByProductIds(List<Long> productIds) {
        return discountActivityRangeDao.findByProductIds(productIds);
    }

    @Override
    public List<DiscountActivityRange> selectByIds(List<Long> ids) {
        return discountActivityRangeDao.selectByIds(ids);
    }

    @Override
    public void batchUpdate(List<DiscountActivityRange> updateRanges) {
        if (CollectionUtils.isEmpty(updateRanges)) return;
        discountActivityRangeDao.batchUpdate(updateRanges);
    }

    @Override
    public void batchUpdateStock(List<DiscountActivityRange> updateRanges) {
        if (CollectionUtils.isEmpty(updateRanges)) return;
        discountActivityRangeDao.batchUpdateStock(updateRanges);
    }

    @Override
    public List<DiscountProductDTO> findProductByActivityId(Long discountActivityId) {
        return discountActivityRangeMapper.findProductByActivityId(discountActivityId);
    }

    @Override
    public List<DiscountActivityRange> findBySkuId(Long skuId) {
        return discountActivityRangeDao.findBySkuId(skuId);
    }

    @Override
    public List<DiscountActivityProductVO> selectByProductIds(List<Long> productIds) {
        return discountActivityRangeDao.selectByProductIds(productIds);
    }
}
