package com.hengtiansoft.item.manager.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.hengtiansoft.common.entity.dto.PageParams;
import com.hengtiansoft.common.redis.RedisOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.DisplayCaseDao;
import com.hengtiansoft.item.dao.DisplayCaseProDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.DisplayCaseSaveDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseSearchDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseUpdateDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.po.DisplayCase;
import com.hengtiansoft.item.entity.po.DisplayCaseProduct;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.vo.DisplayListVO;
import com.hengtiansoft.item.entity.vo.DisplayProductVO;
import com.hengtiansoft.item.entity.vo.FirstDisplayListVO;
import com.hengtiansoft.item.entity.vo.HomePageDisplayVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseVO;
import com.hengtiansoft.item.enumeration.DisplayCaseLevelEnum;
import com.hengtiansoft.item.enumeration.IndexStyleEnum;
import com.hengtiansoft.item.enumeration.ProductSaleStatusEnum;
import com.hengtiansoft.item.manager.ItemDisplayCaseManager;

/**
 * Description: 运营分类Manager实现类
 *
 * <AUTHOR>
 * @since 24.03.2020
 */
@Component
public class ItemDisplayCaseManagerImpl implements ItemDisplayCaseManager {

    private static final int TWO = 2;

    private static final int THREE = 3;

    @Autowired
    private DisplayCaseDao displayCaseDao;

    @Autowired
    private DisplayCaseProDao displayCaseProDao;

    @Autowired
    private ProductDao productDao;

    @Autowired
    private RedisOperation redisOperation;

    @Override
    public void add(DisplayCaseSaveDTO dto) {
        DisplayCase displayCase = new DisplayCase();
        BeanUtils.copy(dto, displayCase);
        if (dto.getIndexStyle() != null) {
            displayCase.setIndexStyle(dto.getIndexStyle().getCode());
        }
        displayCase.setDisplayCaseLevel(dto.getDisplayCaseLevel().getCode());
        displayCaseDao.insert(displayCase);
    }

    @Override
    public void update(DisplayCaseUpdateDTO dto) {
        DisplayCase displayCase = new DisplayCase();
        BeanUtils.copy(dto, displayCase);
        if (dto.getDisplayCaseLevel() != null) {
            displayCase.setDisplayCaseLevel(dto.getDisplayCaseLevel().getCode());
        }
        if (dto.getIndexStyle() != null) {
            displayCase.setIndexStyle(dto.getIndexStyle().getCode());
        }
        displayCaseDao.update(displayCase);
    }

    @Override
    public void delete(Long id) {
        displayCaseDao.deleteById(id);
    }

    @Override
    public ItemDisplayCaseVO one(Long id) {
        ItemDisplayCaseVO itemDisplayCaseVO = new ItemDisplayCaseVO();
        DisplayCase displayCase = displayCaseDao.findById(id);
        BeanUtils.copy(displayCase, itemDisplayCaseVO);
        itemDisplayCaseVO.setDisplayCaseLevel(DisplayCaseLevelEnum.getEnumByCode(displayCase.getDisplayCaseLevel()));
        if (displayCase.getIndexStyle() != null) {
            itemDisplayCaseVO.setIndexStyle(IndexStyleEnum.getEnumByCode(displayCase.getIndexStyle()));
        }
        return itemDisplayCaseVO;
    }

    @Override
    public PageVO<ItemDisplayCaseListVO> list(DisplayCaseSearchDTO dto) {
        List<DisplayCase> displayCases = displayCaseDao.search(dto);
        if (CollectionUtils.isEmpty(displayCases)) {
            return PageUtils.emptyPage();
        }
        List<Long> firstDisplayIds = displayCases.stream().map(DisplayCase::getId).collect(Collectors.toList());
        List<DisplayCaseProduct> proIdsBySecondIds = displayCaseProDao.findProIdsBySecondIds(firstDisplayIds);
        Map<Long, List<DisplayCaseProduct>> displayCaseIdmap = StreamUtils.group(proIdsBySecondIds,
                DisplayCaseProduct::getDisplayCaseId);
        return PageUtils.convert(displayCases, data -> {
            ItemDisplayCaseListVO itemDisplayCaseListVO = new ItemDisplayCaseListVO();
            BeanUtils.copy(data, itemDisplayCaseListVO);
            itemDisplayCaseListVO.setDisplayCaseLevel(DisplayCaseLevelEnum.getEnumByCode(data.getDisplayCaseLevel()));
            itemDisplayCaseListVO.setIndexStyle(IndexStyleEnum.getEnumByCode(data.getIndexStyle()));
            if (CollectionUtils.isEmpty(displayCaseIdmap.get(data.getId()))) {
                itemDisplayCaseListVO.setProductNum(0);
            } else {
                itemDisplayCaseListVO.setProductNum(displayCaseIdmap.get(data.getId()).size());
            }
            return itemDisplayCaseListVO;
        });

    }

    @Override
    public List<FirstDisplayListVO> firstList() {
        List<DisplayCase> displayCases = displayCaseDao.listByLevel(DisplayCaseLevelEnum.FIRSTCATE);
        return BeanUtils.copyList(displayCases, FirstDisplayListVO::new);
    }

    @Override
    public List<ItemDisplayCaseVO> secondList() {
        List<DisplayCase> displayCases = displayCaseDao.listByLevel(DisplayCaseLevelEnum.SECONDCATE);
        if (displayCases.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> secondDisplayIds = displayCases.stream().map(DisplayCase::getId).collect(Collectors.toList());
        List<DisplayCaseProduct> proIdsBySecondIds = displayCaseProDao.findProIdsBySecondIds(secondDisplayIds);
        Map<Long, List<DisplayCaseProduct>> map = proIdsBySecondIds.stream()
                .collect(Collectors.groupingBy(DisplayCaseProduct::getDisplayCaseId));
        return StreamUtils.convert(displayCases, displayCase -> {
            ItemDisplayCaseVO displayCaseVO = BeanUtils.copy(displayCase, ItemDisplayCaseVO::new);
            displayCaseVO.setDisplayCaseLevel(DisplayCaseLevelEnum.getEnumByCode(displayCase.getDisplayCaseLevel()));
            List<DisplayCaseProduct> caseProducts = map.get(displayCase.getId());
            if (CollectionUtils.isEmpty(caseProducts)) {
                displayCaseVO.setProductNum(0);
            } else {
                displayCaseVO.setProductNum(caseProducts.size());
            }
            return displayCaseVO;
        });

    }

    @Override
    public ItemDisplayCaseVO selectByType(Integer type) {
        DisplayCase displayCase = displayCaseDao.findByType(type);
        return BeanUtils.deepCopy(displayCase, ItemDisplayCaseVO.class);
    }

    @Override
    public List<ItemDisplayCaseVO> selectIndexType(boolean isAllShow) {
        List<DisplayCase> displayCases = displayCaseDao.findIndexType();
        if (!isAllShow) {
            // 判断大牌下是否有商品
            List<Product> products = productDao.isShowBig();
            if (CollectionUtils.isEmpty(products)) {
                displayCases.remove(THREE);
            }
            ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
            searchDTO.setPageSize(null);
            searchDTO.setPageNum(null);
            List<Integer> saleStatus = new ArrayList<>();
            saleStatus.add(ProductSaleStatusEnum.ON_SHELVES.getCode());
            searchDTO.setSaleStatus(saleStatus);
            List<Product> byCondition = productDao.findByCondition(searchDTO);
            if (CollectionUtils.isEmpty(byCondition)) {
                displayCases.remove(TWO);
            }
        }
        return StreamUtils.toList(displayCases, data -> {
            ItemDisplayCaseVO itemDisplayCaseVO = new ItemDisplayCaseVO();
            BeanUtils.copy(data, itemDisplayCaseVO);
            itemDisplayCaseVO.setIndexStyle(IndexStyleEnum.getEnumByCode(data.getIndexStyle()));
            return itemDisplayCaseVO;
        });
    }

    @Override
    public List<DisplayListVO> displayList() {
        List<DisplayCase> displayCases = displayCaseDao.displayList();
        return StreamUtils.convert(displayCases, data -> {
            DisplayListVO displayListVO = BeanUtils.copy(data, DisplayListVO::new);
            displayListVO.setIndexStyle(IndexStyleEnum.getEnumByCode(data.getIndexStyle()));
            return displayListVO;
        });
    }

    @Override
    public List<DisplayListVO> secondDisplayList() {
        DisplayCaseSearchDTO dto = new DisplayCaseSearchDTO();
        dto.setDisplayCaseLevel(DisplayCaseLevelEnum.SECONDCATE);
        List<DisplayCase> search = displayCaseDao.search(dto);
        return BeanUtils.copyList(search, DisplayListVO::new);
    }

    @Override
    public List<DisplayListVO> secondListByParentId(Long id) {
        List<DisplayCase> displayCases = displayCaseDao.allSecondDisplays(id);
        return BeanUtils.copyList(displayCases, DisplayListVO::new);
    }

    @Override
    public List<ItemDisplayCaseVO> selectByIsHomepage(Integer isHomepage) {
        List<DisplayCase> byIsHomepage = displayCaseDao.findByIsHomepage(isHomepage);
        return BeanUtils.copyList(byIsHomepage, ItemDisplayCaseVO::new);
    }

    @Override
    public ItemDisplayCaseVO searchByName(String displayCaseName, Long parentId, Long id) {
        DisplayCase displayCase = displayCaseDao.findByName(displayCaseName, parentId, id);
        return BeanUtils.copy(displayCase, ItemDisplayCaseVO::new);
    }

    @Override
    public HomePageDisplayVO homepageInfo() {
        HomePageDisplayVO homePageDisplayVO = new HomePageDisplayVO();
        List<DisplayProductVO> displayProductVOList = new ArrayList<>();
        DisplayCaseSearchDTO dto = new DisplayCaseSearchDTO();
        dto.setDisplayCaseLevel(DisplayCaseLevelEnum.FIRSTCATE);
        List<DisplayCase> firstDisplay = displayCaseDao.search(dto);
        Map<Long, Integer> map = displayCaseDao.homepageInfo();
        firstDisplay.stream().forEach(data -> {
            DisplayProductVO displayProductVO = new DisplayProductVO();
            displayProductVO.setDisplayCaseName(data.getDisplayCaseName());
            if (map.get(data.getId()) == null) {
                displayProductVO.setProductNum(0);
            } else {
                displayProductVO.setProductNum(map.get(data.getId()));
            }
            displayProductVOList.add(displayProductVO);
        });
        homePageDisplayVO.setDisplayProductVOS(displayProductVOList);
        return homePageDisplayVO;
    }

    @Override
    public ItemDisplayCaseVO searchName(String displayCaseName) {
        DisplayCase displayCase = displayCaseDao.findName(displayCaseName);
        return BeanUtils.copy(displayCase, ItemDisplayCaseVO::new);
    }

    @Override
    public List<DisplayCaseProduct> selectByDisplayCaseIds(List<Long> displayCaseIds) {
        return displayCaseProDao.findByDisplayCaseIds(displayCaseIds);

    }

    @Override
    public PageVO<DisplayListVO> displayListInfo(PageParams params) {
        List<DisplayCase> displayCases = displayCaseDao.displayListInfo(params);
        return PageUtils.convert(displayCases, data -> {
            DisplayListVO displayListVO = BeanUtils.copy(data, DisplayListVO::new);
            displayListVO.setIndexStyle(IndexStyleEnum.getEnumByCode(data.getIndexStyle()));
            return displayListVO;
        });
    }
}
