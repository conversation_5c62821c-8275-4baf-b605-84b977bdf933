package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.ProductFrontClassifyListDTO;
import com.hengtiansoft.item.entity.po.ProductFrontClassify;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:39
 **/
public interface ProductFrontClassifyManager {
    List<ProductFrontClassify> findByCondition(ProductFrontClassifyListDTO pageDTO);

    void insertOrUpdate(ProductFrontClassify classify);

    ProductFrontClassify getOne(Long id);

    void update(ProductFrontClassify classify);

    void deleteById(Long id);

    ProductFrontClassify findBySort(Integer sort);

    void updateGreaterSort(Integer sort);
}
