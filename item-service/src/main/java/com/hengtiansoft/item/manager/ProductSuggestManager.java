package com.hengtiansoft.item.manager;


import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.item.entity.dto.ProductSuggestListDTO;
import com.hengtiansoft.item.entity.po.ProductSuggest;

import java.util.Date;
import java.util.List;

public interface ProductSuggestManager {

    List<ProductSuggest> findByCondition(ProductSuggestListDTO dto);

    ProductSuggest findBySort(Integer sort, Integer position);

    ProductSuggest findById(Long id);

    void insert(ProductSuggest po);

    void update(ProductSuggest po);

    void updateAll(ProductSuggest po);

    void updateGreaterSort(Integer sort, Integer position);

    void delete(Long id);

    List<ProductSuggest> finAutoList(Date now, CommonActivityStatusEnum statusEnum);

    List<ProductSuggest> findHasItemSuggests(ProductSuggestListDTO dto);
}
