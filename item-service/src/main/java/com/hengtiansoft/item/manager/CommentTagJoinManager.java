package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.CommentTagJoinListDTO;
import com.hengtiansoft.item.entity.po.CommentTagJoin;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface CommentTagJoinManager {
    List<CommentTagJoin> findByCommentId(Long id);

    void deleteByTagIdWithType(Long id, Integer code);

    List<CommentTagJoin> findByCondition(CommentTagJoinListDTO dto);

    void insertList(List<CommentTagJoin> commentTags);
}
