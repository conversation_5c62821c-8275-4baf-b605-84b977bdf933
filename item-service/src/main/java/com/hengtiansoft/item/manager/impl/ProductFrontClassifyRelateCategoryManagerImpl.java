package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.item.dao.ProductFrontClassifyRelateCategoryDao;
import com.hengtiansoft.item.entity.po.ProductFrontClassifyRelateCategory;
import com.hengtiansoft.item.manager.ProductFrontClassifyRelateCategoryManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:42
 **/
@Slf4j
@Service
public class ProductFrontClassifyRelateCategoryManagerImpl implements ProductFrontClassifyRelateCategoryManager {

    @Resource
    private ProductFrontClassifyRelateCategoryDao productFrontClassifyRelateCategoryDao;

    @Override
    public void deleteByClassifyId(Long classifyId) {
        productFrontClassifyRelateCategoryDao.deleteByClassifyId(classifyId);
    }

    @Override
    public void batchInsert(Long classifyId, List<Long> categoryIds, String userName) {
        if(CollectionUtils.isEmpty(categoryIds)){
            return;
        }
        List<ProductFrontClassifyRelateCategory> list = new ArrayList<>();
        categoryIds.forEach(categoryId -> {
            ProductFrontClassifyRelateCategory relateCate = new ProductFrontClassifyRelateCategory();
            relateCate.setProductFrontClassifyId(classifyId);
            relateCate.setCategoryId(categoryId);
            relateCate.setOperator(userName);
            relateCate.setCreateTime(new Date());
            relateCate.setUpdateTime(new Date());
            relateCate.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            list.add(relateCate);
        });
        productFrontClassifyRelateCategoryDao.batchInsert(list);
    }

    @Override
    public List<ProductFrontClassifyRelateCategory> findByClassifyId(Long classifyId) {
        return productFrontClassifyRelateCategoryDao.findByClassifyId(classifyId);
    }
}
