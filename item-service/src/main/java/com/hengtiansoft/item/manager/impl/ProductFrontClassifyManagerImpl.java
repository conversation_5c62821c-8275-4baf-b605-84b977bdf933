package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.ProductFrontClassifyDao;
import com.hengtiansoft.item.entity.dto.ProductFrontClassifyListDTO;
import com.hengtiansoft.item.entity.po.ProductFrontClassify;
import com.hengtiansoft.item.manager.ProductFrontClassifyManager;
import com.hengtiansoft.item.mapper.ProductFrontClassifyMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:42
 **/
@Slf4j
@Service
public class ProductFrontClassifyManagerImpl implements ProductFrontClassifyManager {

    @Resource
    private ProductFrontClassifyDao productFrontClassifyDao;
    @Resource
    private ProductFrontClassifyMapper productFrontClassifyMapper;

    @Override
    public List<ProductFrontClassify> findByCondition(ProductFrontClassifyListDTO pageDTO) {
        return productFrontClassifyDao.findByCondition(pageDTO);
    }

    @Override
    public void insertOrUpdate(ProductFrontClassify classify) {
        if (Objects.isNull(classify.getId())) {
            productFrontClassifyDao.insert(classify);
        } else {
            productFrontClassifyDao.updateAll(classify);
        }
    }

    @Override
    public ProductFrontClassify getOne(Long id) {
        return productFrontClassifyDao.getOne(id);
    }

    @Override
    public void update(ProductFrontClassify classify) {
        productFrontClassifyDao.update(classify);
    }

    @Override
    public void deleteById(Long id) {
        productFrontClassifyDao.deleteById(id);
    }

    @Override
    public ProductFrontClassify findBySort(Integer sort) {
        Condition condition = new Condition(ProductFrontClassify.class, true ,true);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("sort", sort);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        RowBounds rowBounds = new RowBounds(0, 1);
        return StreamUtils.getFirst(productFrontClassifyMapper.selectByExampleAndRowBounds(condition , rowBounds));
    }

    @Override
    public void updateGreaterSort(Integer sort) {
        productFrontClassifyMapper.updateGreaterSort(sort);
    }


}
