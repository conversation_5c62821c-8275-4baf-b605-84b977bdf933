package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.item.dao.CommentTagDao;
import com.hengtiansoft.item.entity.po.CommentTag;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CommentTagDao;
import com.hengtiansoft.item.entity.dto.CommentTagListDTO;
import com.hengtiansoft.item.entity.dto.CommentTagSaveDTO;
import com.hengtiansoft.item.entity.po.CommentTag;
import com.hengtiansoft.item.manager.CommentTagManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommentTagManagerImpl implements CommentTagManager {

    @Resource
    private CommentTagDao commentTagDao;

    @Override
    public CommentTag findBySystemType(Integer code) {
        return commentTagDao.findBySystemType(code);
    }

    @Override
    public List<CommentTag> findByIds(List<Long> ids) {
        return commentTagDao.findByIds(ids);
    }

    @Override
    public List<CommentTag> search(CommentTagListDTO dto) {
        return commentTagDao.search(dto);
    }

    @Override
    public void save(CommentTagSaveDTO dto) {
        CommentTag tag = BeanUtils.copy(dto, CommentTag::new);
        if(Objects.isNull(dto.getId())){
            commentTagDao.insert(tag);
        }else{
            commentTagDao.update(tag);
        }
    }

    @Override
    public void checkTagName(CommentTagSaveDTO dto) {
        if(StringUtils.isBlank(dto.getName())){
            throw new BusinessException("标签名称不能为空!");
        }
        CommentTagListDTO searchDto = new CommentTagListDTO();
        searchDto.setName(dto.getName().trim());
        List<CommentTag> commentTagList = commentTagDao.search(searchDto);

        if(Objects.nonNull(dto.getId())){
            commentTagList = StreamUtils.filter(commentTagList, x -> !x.getId().equals(dto.getId()));
        }

        if(CollectionUtils.isNotEmpty(commentTagList)){
            throw new BusinessException("标签名称重复!");
        }
    }

    @Override
    public void deleteById(Long id) {
        commentTagDao.deleteById(id);
    }
}
