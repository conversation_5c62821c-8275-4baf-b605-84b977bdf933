package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.CardShareDTO;
import com.hengtiansoft.item.entity.po.CardShare;

import java.util.List;

/**
 * 送卡记录manager
 *
 * <AUTHOR>
 * @since 08.09.2020
 */
public interface CardShareManager {
    /**
     * 新增送卡记录
     *
     * @param cardShareDTO
     */
    void insert(CardShareDTO cardShareDTO);

    /**
     * 批量新增送卡记录
     *
     * @param cardShareList 集合
     */
    void batchInsert(List<CardShareDTO> cardShareList);

    /**
     * 失败删除送卡记录
     *
     * @param id id
     */
    void delete(Long id);

    /**
     * 查询分享记录
     *
     * @param shareNumber 编号
     * @return 分享纪律
     */
    List<CardShare>  findByNumber(String shareNumber);

    /**
     * 修改分享信息
     *
     * @param cardShareDTO dto
     * @return 受影响行数
     */
    int updateOne(CardShareDTO cardShareDTO);

    /**
     * 领取卡
     *
     * @param cardShareDTO dto
     * @return >0领卡成功  =0 领卡失败
     */
    int receiveCard(CardShareDTO cardShareDTO);

    /**
     * 根据条件查询
     *
     * @param cardShareDTO dto
     * @return 集合
     */
    List<CardShare> findByCondition(CardShareDTO cardShareDTO);


    List<CardShare> findByCardIds(List<Long> cardIds);

    void cancelCardShare(List<Long> cardShareIds, CardShare cardShare);

    List<CardShare> selectNewestShare(CardShareDTO cardShareDTO);
}
