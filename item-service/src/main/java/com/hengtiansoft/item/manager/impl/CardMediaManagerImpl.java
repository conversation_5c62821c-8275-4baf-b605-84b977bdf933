package com.hengtiansoft.item.manager.impl;

import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.item.dao.CardMediaDao;
import com.hengtiansoft.item.entity.dto.CardMediaDTO;
import com.hengtiansoft.item.entity.po.CardMedia;
import com.hengtiansoft.item.entity.vo.CardMediaVO;
import com.hengtiansoft.item.enumeration.CardTypeEnum;
import com.hengtiansoft.item.manager.CardMediaManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 **/
@Service
public class CardMediaManagerImpl implements CardMediaManager {

    @Autowired
    private CardMediaDao cardMediaDao;

    @Override
    public CardMediaVO findByCardNumAndCardType(String cardNum, CardTypeEnum cardTypeEnum) {
        CardMedia cardMedia = cardMediaDao.findByCardNumAndCardType(cardNum, cardTypeEnum);
        return Objects.isNull(cardMedia) ? new CardMediaVO() : BeanUtils.copy(cardMedia, CardMediaVO::new);
    }

    @Override
    public int saveOne(CardMediaDTO cardMediaDTO) {
        if (Objects.isNull(cardMediaDTO)){
            return 0;
        }
        CardMedia copy = BeanUtils.copy(cardMediaDTO, CardMedia::new);
        return cardMediaDao.saveOne(copy);
    }
}