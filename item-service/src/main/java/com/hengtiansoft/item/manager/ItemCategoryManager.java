package com.hengtiansoft.item.manager;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.CategorySearchDTO;
import com.hengtiansoft.item.entity.dto.ItemCategorySaveDTO;
import com.hengtiansoft.item.entity.dto.ItemCategoryUpdateDTO;
import com.hengtiansoft.item.entity.vo.CategoryListVO;
import com.hengtiansoft.item.entity.vo.FirstCategoryVO;
import com.hengtiansoft.item.entity.vo.ItemCategoryListVO;
import com.hengtiansoft.item.entity.vo.ItemCategoryVO;
import com.hengtiansoft.item.enumeration.ItemCategoryLevelEnum;

import java.util.List;
import java.util.Map;

/**
 * Description: 商品分类管理Manager
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
public interface ItemCategoryManager {

    void add(ItemCategorySaveDTO dto, String operator);

    void update(ItemCategoryUpdateDTO dto, String operator, boolean isTagJoin);

    void delete(Long id);

    ItemCategoryVO one(Long id);

    PageVO<ItemCategoryListVO> list(CategorySearchDTO dto);

    List<ItemCategoryVO> secondList(ItemCategoryLevelEnum level);

    List<FirstCategoryVO> firstList();

    List<CategoryListVO> cateList();

    List<CategoryListVO> catePublicList(CategorySearchDTO dto);

    List<CategoryListVO> secondCateList(Integer isPublic);

    Map<Long, String> selectNamePath();

    List<CategoryListVO> secondListByParentId(Long parentId);

    ItemCategoryVO searchByName(String name, Long parentId, Long id);

    List<CategoryListVO> categoryList(Integer isPublic);

    List<CategoryListVO> categoryByCateTypeList(List<Integer> cateType);

    List<CategoryListVO> categoryByCateType(Integer cateType, Integer isPublic);

    List<CategoryListVO> categoryByCateTypeNotId(Integer cateType, Integer isPublic, Long id);

    ItemCategoryVO searchName(String cateName);
}
