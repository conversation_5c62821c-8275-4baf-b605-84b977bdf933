package com.hengtiansoft.item.manager;

import java.util.List;
import java.util.Map;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSaveDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProSearchDTO;
import com.hengtiansoft.item.entity.dto.DisplayCaseProUpdateDTO;
import com.hengtiansoft.item.entity.dto.HomePageProSearchDTO;
import com.hengtiansoft.item.entity.dto.HomePageSaveDTO;
import com.hengtiansoft.item.entity.dto.SearchProductByDisplayDTO;
import com.hengtiansoft.item.entity.po.DisplayCaseProduct;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.entity.vo.DisplayProductVO;
import com.hengtiansoft.item.entity.vo.HomePageProListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayCaseProListVO;
import com.hengtiansoft.item.entity.vo.ItemDisplayProductVo;
import com.hengtiansoft.item.entity.vo.SecondProListVO;

/**
 * Description: 运营分类管理下的商品管理Manager
 *
 * <AUTHOR>
 * @since 31.03.2020
 */
public interface ItemDisplayCaseProManager {

    void add(DisplayCaseProSaveDTO dto);

    void delete(Long displayCaseId);

    List<ItemDisplayCaseProListVO> list(DisplayCaseProSearchDTO dto);

    List<SecondProListVO> secondList(HomePageProSearchDTO dto);

    void addHomePage(HomePageSaveDTO dto);

    List<HomePageProListVO> homePageList(HomePageProSearchDTO dto);

    Map<Long, Integer> selectAll();

    void update(DisplayCaseProUpdateDTO dto);

    List<DisplayCaseProduct> selectByDisplayCaseId(Long id);

    List<Long> selectByDisplayCaseIds(List<Long> displayIds);

    List<DisplayCaseProduct> selectProByDisplayCaseId(List<Long> displayIds);



    void deleteByExample(Long displayCaseId,Long productId);

    PageVO<ItemDisplayCaseProListVO> listByWX(DisplayCaseProSearchDTO dto, Integer pageNum, Integer pageSize);

    List<Long> searchProducts(SearchProductByDisplayDTO dto);

    List<Long> searchProductsById(Long id);
}
