package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.dto.DiscountProductDTO;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.vo.DiscountActivityProductVO;

import java.util.List;

public interface DiscountActivityRangeManager {
    List<DiscountActivityRange> findByActivityId(Long id);
    List<DiscountActivityRange> findByActivityIdsWithDel(List<Long> ids);

    List<DiscountActivityRange> findByProductInfo(Long skuId, Long productId);

    List<DiscountActivityRange> findByActivityIds(List<Long> pageActivityIds);

    List<DiscountActivityRange> findByProductId(Long productId);

    List<DiscountActivityRange> findByProductIds(List<Long> productIds);

    List<DiscountActivityRange> selectByIds(List<Long> ids);

    void batchUpdate(List<DiscountActivityRange> updateRanges);

    void batchUpdateStock(List<DiscountActivityRange> updateRanges);

    List<DiscountActivityProductVO> selectByProductIds(List<Long> productIds);

    List<DiscountProductDTO> findProductByActivityId(Long discountActivityId);

    List<DiscountActivityRange> findBySkuId(Long skuId);
}
