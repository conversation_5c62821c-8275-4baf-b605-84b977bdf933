package com.hengtiansoft.item.manager;

import com.hengtiansoft.item.entity.po.ProductFrontClassifyRelateCategory;
import com.hengtiansoft.item.entity.po.ProductFrontClassifyRelateItem;

import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-22 14:40
 **/
public interface ProductFrontClassifyRelateCategoryManager {

    void deleteByClassifyId(Long classifyId);

    void batchInsert(Long classifyId, List<Long> categoryIds, String userName);

    List<ProductFrontClassifyRelateCategory> findByClassifyId(Long classifyId);
}
