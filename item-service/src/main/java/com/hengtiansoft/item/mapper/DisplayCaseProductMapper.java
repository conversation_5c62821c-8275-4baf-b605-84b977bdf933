package com.hengtiansoft.item.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.hengtiansoft.item.entity.dto.SearchProductByDisplayDTO;
import com.hengtiansoft.item.entity.po.DisplayCaseProduct;

import tk.mybatis.mapper.common.Mapper;

public interface DisplayCaseProductMapper extends Mapper<DisplayCaseProduct> {

    List<DisplayCaseProduct> findProIdsByDisplayCaseId(Long displayCaseId);

    List<Long> findProIdsBySecondIds(Set<Long> displayCaseId);

    List<Long> searchProducts(SearchProductByDisplayDTO dto);

    List<DisplayCaseProduct> findProByDisplayCaseId(@Param("displayIds") List<Long> displayIds);

    List<Long> findProductsById(Long id);
}
