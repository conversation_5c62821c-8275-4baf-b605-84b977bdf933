package com.hengtiansoft.item.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hengtiansoft.item.entity.dto.SkuSpecValueDTO;
import com.hengtiansoft.item.entity.po.SkuSpecValue;

import tk.mybatis.mapper.common.Mapper;

public interface SkuSpecValueMapper extends Mapper<SkuSpecValue> {

    List<SkuSpecValueDTO> selectDtoBySkuId(@Param("skuId") Long skuId);

    List<SkuSpecValueDTO> selectDtoBySkuIds(@Param("skuIds") List<Long> skuIds);
    
    /**
     * 删除不存在的Spec关系
     *
     * @param productId
     * @param specIds
     * @return
     */
    int deleteByProductIdAndSpecIdsNotIn(@Param("productId") Long productId, @Param("specIds") List<Long> specIds);


}
