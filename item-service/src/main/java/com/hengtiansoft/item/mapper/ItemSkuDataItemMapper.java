package com.hengtiansoft.item.mapper;

import com.hengtiansoft.item.entity.po.ItemSkuDataItem;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * @Author: haiyang
 * @Date: 2024-12-30 16:59
 * @Desc:
 */
public interface ItemSkuDataItemMapper extends Mapper<ItemSkuDataItem> {
    void deleteBySkuCode(@Param("skuCode") String skuCode);

    List<ItemSkuDataItem> selectBySkuCodeList(@Param("skuCodes") List<String> skuCodes);
}
