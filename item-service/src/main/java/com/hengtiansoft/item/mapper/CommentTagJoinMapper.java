package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.dto.CommentTagJoinCategoryDTO;
import com.hengtiansoft.item.entity.po.CommentTagJoin;
import tk.mybatis.mapper.common.special.InsertListMapper;

import java.util.List;

public interface CommentTagJoinMapper extends CrudMapper<CommentTagJoin>, InsertListMapper<CommentTagJoin> {


    List<CommentTagJoinCategoryDTO> selectCategoryByTagIds(List<Long> tagIds);

    List<CommentTagJoinCategoryDTO> selectTagByTargetIds(List<Long> targetIds);
}