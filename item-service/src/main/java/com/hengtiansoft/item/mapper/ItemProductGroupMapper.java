package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.po.ItemProductGroup;
import com.hengtiansoft.item.entity.vo.GroupTemplateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ItemProductGroupMapper extends CrudMapper<ItemProductGroup> {
    List<ItemProductGroup> selectByProductIds(@Param("productIds") List<Long> productIds);

    List<GroupTemplateVO> groupProductCnt(@Param("ids") List<Long> ids);
}