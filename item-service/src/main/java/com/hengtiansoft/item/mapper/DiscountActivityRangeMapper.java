package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.dto.DiscountProductDTO;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.vo.DiscountActivityProductVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.additional.idlist.DeleteByIdListMapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

import java.util.List;

public interface DiscountActivityRangeMapper extends CrudMapper<DiscountActivityRange>, InsertListMapper<DiscountActivityRange>, DeleteByIdListMapper<DiscountActivityRange, Long> {

    void batchUpdate(@Param("list") List<DiscountActivityRange> updateRangeList);

    void batchUpdateStock(@Param("list") List<DiscountActivityRange> updateRangeList);

    List<DiscountActivityProductVO> selectByProductIds(@Param("productIds") List<Long> productIds);

    List<DiscountProductDTO> findProductByActivityId(@Param("discountActivityId") Long discountActivityId);

    Integer checkStock(@Param("discountActivityId") Long discountActivityId, @Param("skuId") Long skuId, @Param("stockNum") Long stockNum);

    int decreaseStock(@Param("discountActivityId") Long discountActivityId, @Param("skuId") Long skuId, @Param("stockNum") Long stockNum);

    int increaseStock(@Param("discountActivityId") Long discountActivityId, @Param("skuId") Long skuId, @Param("stockNum") Long stockNum);

    List<Long> selectInProgressNoShareCouponProductIds();
}