package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.po.CardAmountChangeDetail;
import com.hengtiansoft.item.entity.vo.CardWriteOffAmountVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;


public interface CardAmountChangeDetailMapper extends CrudMapper<CardAmountChangeDetail> {
    int batchInsert(@Param("list") List<CardAmountChangeDetail> list);

    /**
     * 查询修改金额或修改对账金额修改记录的最新一条
     * @param cardNumber
     * @param
     * @return
     */
    CardAmountChangeDetail selectByCardNumber(@Param("date") String date, @Param("cardNumber") String cardNumber);

    /**
     * 批量查询修改金额或修改对账金额修改记录的最新一条
     * @param cardNumbers
     * @param
     * @return
     */
    List<CardAmountChangeDetail> selectByCardNumbers(@Param("date") String date, @Param("list") Collection<String> cardNumbers);

    /**
     * 查询对账金额修改记录的最新一条
     * @param cardNumber
     * @param
     * @return
     */
    CardAmountChangeDetail selectByCardNumberReconciliation(@Param("cardNumber") String cardNumber);

    /**
     * 批量查询对账金额记录
     * @param list
     * @param
     * @return
     */
    List<CardAmountChangeDetail> selectByCardNumbersReconciliation(@Param("list") List<String> list, @Param("startTime") Date startTime,@Param("endTime") Date endTime);

    /**
     * 批量查询修改金额记录
     * @param list
     * @param
     * @return
     */
    List<CardAmountChangeDetail> selectByCardNumbersChanged(@Param("list") List<String> list ,@Param("startTime") Date startTime,@Param("endTime") Date endTime);

    /**
     * 查询修改金额修改记录的最新一条
     * @param cardNumber
     * @param
     * @return
     */
    CardAmountChangeDetail selectByCardNumberChanged(@Param("cardNumber") String cardNumber);


    /**
     * 查询对账金额修改记录的最新一条
     * @param cardNumber
     * @param date
     * @return
     */
    CardAmountChangeDetail selectReconciliationByCardNumber(@Param("date") String date, @Param("cardNumber") String cardNumber);

    /**
     * 批量查询对账金额修改记录的最新一条
     * @param cardNumbers
     * @param date
     * @return
     */
    List<CardAmountChangeDetail> selectReconciliationByCardNumbers(@Param("date") String date, @Param("list") Collection<String> cardNumbers);

    /**
     * 查询冲销记录
     * @param cardNumber
     * @param
     * @return
     */
    List<CardAmountChangeDetail> selectByCardNumberWriteOff(@Param("cardNumber") String cardNumber);

    /**
     * 查询冲销金额
     * @param cardNumber
     * @param
     * @return
     */
    BigDecimal selectSumWriteOffAmountByCardNumber(@Param("cardNumber") String cardNumber);


    /**
     * 查询冲销金额
     * @param cardNumbers
     * @param
     * @return
     */
    List<CardAmountChangeDetail> selectSumWriteOffAmountByCardNumbers(@Param("list") Collection<String> cardNumbers);

    /**
     * 查询冲销金额汇总
     * @param list
     * @param endTime
     * @return
     */
    List<CardWriteOffAmountVO> selectWriteOffAmountByCardNumbers(@Param("list") List<String> list , @Param("endTime") Date endTime);
}
