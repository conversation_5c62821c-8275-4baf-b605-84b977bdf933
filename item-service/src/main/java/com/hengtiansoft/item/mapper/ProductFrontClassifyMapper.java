package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.dto.ProductFrontClassifyListDTO;
import com.hengtiansoft.item.entity.po.ProductFrontClassify;

import java.util.List;

public interface ProductFrontClassifyMapper extends CrudMapper<ProductFrontClassify> {
    List<ProductFrontClassify> findByCondition(ProductFrontClassifyListDTO pageDTO);

    void updateGreaterSort(Integer sort);
}