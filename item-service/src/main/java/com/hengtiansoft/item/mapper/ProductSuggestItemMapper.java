package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.dto.ProductSuggestItemListDTO;
import com.hengtiansoft.item.entity.po.ProductSuggestItem;
import com.hengtiansoft.item.entity.vo.ProductSuggestItemResultVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductSuggestItemMapper extends CrudMapper<ProductSuggestItem> {
    List<ProductSuggestItemResultVO> findPageByCondition(@Param("dto") ProductSuggestItemListDTO dto);

    void deleteByTypeAndSuggestId(@Param("type") Integer type, @Param("suggestId") Long suggestId);

    void deleteByTypeAndSuggestIdAndProductIds(@Param("type") Integer type, @Param("suggestId") Long suggestId, @Param("productIds") List<Long> deleteProductIds, @Param("userName") String operator);

    List<ProductSuggestItem> selectGreaterThanSortList(@Param("type") Integer type, @Param("productSuggestId") Long productSuggestId, @Param("sort") Integer sort);

    void batchUpdate(@Param("list") List<ProductSuggestItem> greaterThanCurrentSortItems);

    ProductSuggestItem selectExistByTypeAndSuggestId(@Param("type") Integer type, @Param("suggestId") Long suggestId);
}