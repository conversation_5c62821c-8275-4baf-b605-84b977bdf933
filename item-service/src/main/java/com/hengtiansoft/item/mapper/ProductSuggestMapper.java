package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.dto.ProductSuggestListDTO;
import com.hengtiansoft.item.entity.po.ProductSuggest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductSuggestMapper extends CrudMapper<ProductSuggest> {
    void updateGreaterSort(@Param("sort")Integer sort, @Param("position")Integer position);

    List<ProductSuggest> findHasItemSuggests(ProductSuggestListDTO dto);
}