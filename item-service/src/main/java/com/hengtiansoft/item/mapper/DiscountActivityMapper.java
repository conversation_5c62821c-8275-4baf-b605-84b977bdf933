package com.hengtiansoft.item.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.item.entity.dto.DiscountActivityDTO;
import com.hengtiansoft.item.entity.po.DiscountActivity;
import com.hengtiansoft.item.entity.vo.DiscountActivityProductVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface DiscountActivityMapper extends CrudMapper<DiscountActivity> {
    List<DiscountActivity> findBySkuIds(@Param("skuIds") List<Long> skuIds);

    List<DiscountActivity> findByCondition(DiscountActivityDTO dto);

    List<DiscountActivityProductVO> getDiscountActivityProductList(@Param("status") Integer status, @Param("discountActivityIds") List<Long> discountActivityIds);

    List<DiscountActivity> findStartNoticeDiscount(@Param("startTimeLess") Date startTimeLess);

    List<DiscountActivity> findNewNoticeDiscount();

    List<DiscountActivity> manualFindByCondition(@Param("dto") DiscountActivityDTO accountActivityDTO);
}